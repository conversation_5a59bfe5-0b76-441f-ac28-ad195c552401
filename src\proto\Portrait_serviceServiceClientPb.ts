/**
 * @fileoverview gRPC-Web generated client stub for portrait
 * @enhanceable
 * @public
 */

// Code generated by protoc-gen-grpc-web. DO NOT EDIT.
// versions:
// 	protoc-gen-grpc-web v1.5.0
// 	protoc              v6.31.1
// source: portrait_service.proto


/* eslint-disable */
// @ts-nocheck


import * as grpcWeb from 'grpc-web';

import * as portrait_service_pb from './portrait_service_pb'; // proto import: "portrait_service.proto"


export class PortraitServiceClient {
  client_: grpcWeb.AbstractClientBase;
  hostname_: string;
  credentials_: null | { [index: string]: string; };
  options_: null | { [index: string]: any; };

  constructor (hostname: string,
               credentials?: null | { [index: string]: string; },
               options?: null | { [index: string]: any; }) {
    if (!options) options = {};
    if (!credentials) credentials = {};
    options['format'] = 'text';

    this.client_ = new grpcWeb.GrpcWebClientBase(options);
    this.hostname_ = hostname.replace(/\/+$/, '');
    this.credentials_ = credentials;
    this.options_ = options;
  }

  methodDescriptorGetLibrary = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/GetLibrary',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.GetLibraryRequest,
    portrait_service_pb.Library,
    (request: portrait_service_pb.GetLibraryRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.Library.deserializeBinary
  );

  getLibrary(
    request: portrait_service_pb.GetLibraryRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.Library>;

  getLibrary(
    request: portrait_service_pb.GetLibraryRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Library) => void): grpcWeb.ClientReadableStream<portrait_service_pb.Library>;

  getLibrary(
    request: portrait_service_pb.GetLibraryRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Library) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/GetLibrary',
        request,
        metadata || {},
        this.methodDescriptorGetLibrary,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/GetLibrary',
    request,
    metadata || {},
    this.methodDescriptorGetLibrary);
  }

  methodDescriptorListLibraries = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/ListLibraries',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.ListLibrariesRequest,
    portrait_service_pb.LibraryList,
    (request: portrait_service_pb.ListLibrariesRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.LibraryList.deserializeBinary
  );

  listLibraries(
    request: portrait_service_pb.ListLibrariesRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.LibraryList>;

  listLibraries(
    request: portrait_service_pb.ListLibrariesRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.LibraryList) => void): grpcWeb.ClientReadableStream<portrait_service_pb.LibraryList>;

  listLibraries(
    request: portrait_service_pb.ListLibrariesRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.LibraryList) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/ListLibraries',
        request,
        metadata || {},
        this.methodDescriptorListLibraries,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/ListLibraries',
    request,
    metadata || {},
    this.methodDescriptorListLibraries);
  }

  methodDescriptorCreateLibrary = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/CreateLibrary',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.CreateLibraryRequest,
    portrait_service_pb.Library,
    (request: portrait_service_pb.CreateLibraryRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.Library.deserializeBinary
  );

  createLibrary(
    request: portrait_service_pb.CreateLibraryRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.Library>;

  createLibrary(
    request: portrait_service_pb.CreateLibraryRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Library) => void): grpcWeb.ClientReadableStream<portrait_service_pb.Library>;

  createLibrary(
    request: portrait_service_pb.CreateLibraryRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Library) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/CreateLibrary',
        request,
        metadata || {},
        this.methodDescriptorCreateLibrary,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/CreateLibrary',
    request,
    metadata || {},
    this.methodDescriptorCreateLibrary);
  }

  methodDescriptorUpdateLibrary = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/UpdateLibrary',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.UpdateLibraryRequest,
    portrait_service_pb.Library,
    (request: portrait_service_pb.UpdateLibraryRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.Library.deserializeBinary
  );

  updateLibrary(
    request: portrait_service_pb.UpdateLibraryRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.Library>;

  updateLibrary(
    request: portrait_service_pb.UpdateLibraryRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Library) => void): grpcWeb.ClientReadableStream<portrait_service_pb.Library>;

  updateLibrary(
    request: portrait_service_pb.UpdateLibraryRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Library) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/UpdateLibrary',
        request,
        metadata || {},
        this.methodDescriptorUpdateLibrary,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/UpdateLibrary',
    request,
    metadata || {},
    this.methodDescriptorUpdateLibrary);
  }

  methodDescriptorDeleteLibrary = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/DeleteLibrary',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.DeleteLibraryRequest,
    portrait_service_pb.DeleteResponse,
    (request: portrait_service_pb.DeleteLibraryRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.DeleteResponse.deserializeBinary
  );

  deleteLibrary(
    request: portrait_service_pb.DeleteLibraryRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.DeleteResponse>;

  deleteLibrary(
    request: portrait_service_pb.DeleteLibraryRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.DeleteResponse) => void): grpcWeb.ClientReadableStream<portrait_service_pb.DeleteResponse>;

  deleteLibrary(
    request: portrait_service_pb.DeleteLibraryRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.DeleteResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/DeleteLibrary',
        request,
        metadata || {},
        this.methodDescriptorDeleteLibrary,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/DeleteLibrary',
    request,
    metadata || {},
    this.methodDescriptorDeleteLibrary);
  }

  methodDescriptorGetGroup = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/GetGroup',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.GetGroupRequest,
    portrait_service_pb.Group,
    (request: portrait_service_pb.GetGroupRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.Group.deserializeBinary
  );

  getGroup(
    request: portrait_service_pb.GetGroupRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.Group>;

  getGroup(
    request: portrait_service_pb.GetGroupRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Group) => void): grpcWeb.ClientReadableStream<portrait_service_pb.Group>;

  getGroup(
    request: portrait_service_pb.GetGroupRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Group) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/GetGroup',
        request,
        metadata || {},
        this.methodDescriptorGetGroup,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/GetGroup',
    request,
    metadata || {},
    this.methodDescriptorGetGroup);
  }

  methodDescriptorListGroups = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/ListGroups',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.ListGroupsRequest,
    portrait_service_pb.GroupList,
    (request: portrait_service_pb.ListGroupsRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.GroupList.deserializeBinary
  );

  listGroups(
    request: portrait_service_pb.ListGroupsRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.GroupList>;

  listGroups(
    request: portrait_service_pb.ListGroupsRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.GroupList) => void): grpcWeb.ClientReadableStream<portrait_service_pb.GroupList>;

  listGroups(
    request: portrait_service_pb.ListGroupsRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.GroupList) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/ListGroups',
        request,
        metadata || {},
        this.methodDescriptorListGroups,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/ListGroups',
    request,
    metadata || {},
    this.methodDescriptorListGroups);
  }

  methodDescriptorCreateGroup = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/CreateGroup',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.CreateGroupRequest,
    portrait_service_pb.Group,
    (request: portrait_service_pb.CreateGroupRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.Group.deserializeBinary
  );

  createGroup(
    request: portrait_service_pb.CreateGroupRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.Group>;

  createGroup(
    request: portrait_service_pb.CreateGroupRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Group) => void): grpcWeb.ClientReadableStream<portrait_service_pb.Group>;

  createGroup(
    request: portrait_service_pb.CreateGroupRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Group) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/CreateGroup',
        request,
        metadata || {},
        this.methodDescriptorCreateGroup,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/CreateGroup',
    request,
    metadata || {},
    this.methodDescriptorCreateGroup);
  }

  methodDescriptorUpdateGroup = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/UpdateGroup',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.UpdateGroupRequest,
    portrait_service_pb.Group,
    (request: portrait_service_pb.UpdateGroupRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.Group.deserializeBinary
  );

  updateGroup(
    request: portrait_service_pb.UpdateGroupRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.Group>;

  updateGroup(
    request: portrait_service_pb.UpdateGroupRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Group) => void): grpcWeb.ClientReadableStream<portrait_service_pb.Group>;

  updateGroup(
    request: portrait_service_pb.UpdateGroupRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Group) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/UpdateGroup',
        request,
        metadata || {},
        this.methodDescriptorUpdateGroup,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/UpdateGroup',
    request,
    metadata || {},
    this.methodDescriptorUpdateGroup);
  }

  methodDescriptorDeleteGroup = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/DeleteGroup',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.DeleteGroupRequest,
    portrait_service_pb.DeleteResponse,
    (request: portrait_service_pb.DeleteGroupRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.DeleteResponse.deserializeBinary
  );

  deleteGroup(
    request: portrait_service_pb.DeleteGroupRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.DeleteResponse>;

  deleteGroup(
    request: portrait_service_pb.DeleteGroupRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.DeleteResponse) => void): grpcWeb.ClientReadableStream<portrait_service_pb.DeleteResponse>;

  deleteGroup(
    request: portrait_service_pb.DeleteGroupRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.DeleteResponse) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/DeleteGroup',
        request,
        metadata || {},
        this.methodDescriptorDeleteGroup,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/DeleteGroup',
    request,
    metadata || {},
    this.methodDescriptorDeleteGroup);
  }

  methodDescriptorPagedQueryPortraits = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/PagedQueryPortraits',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.PagedQueryPortraitsRequest,
    portrait_service_pb.PortraitList,
    (request: portrait_service_pb.PagedQueryPortraitsRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.PortraitList.deserializeBinary
  );

  pagedQueryPortraits(
    request: portrait_service_pb.PagedQueryPortraitsRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.PortraitList>;

  pagedQueryPortraits(
    request: portrait_service_pb.PagedQueryPortraitsRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void): grpcWeb.ClientReadableStream<portrait_service_pb.PortraitList>;

  pagedQueryPortraits(
    request: portrait_service_pb.PagedQueryPortraitsRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/PagedQueryPortraits',
        request,
        metadata || {},
        this.methodDescriptorPagedQueryPortraits,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/PagedQueryPortraits',
    request,
    metadata || {},
    this.methodDescriptorPagedQueryPortraits);
  }

  methodDescriptorSearchPortraits = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/SearchPortraits',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.SearchPortraitsRequest,
    portrait_service_pb.PortraitList,
    (request: portrait_service_pb.SearchPortraitsRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.PortraitList.deserializeBinary
  );

  searchPortraits(
    request: portrait_service_pb.SearchPortraitsRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.PortraitList>;

  searchPortraits(
    request: portrait_service_pb.SearchPortraitsRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void): grpcWeb.ClientReadableStream<portrait_service_pb.PortraitList>;

  searchPortraits(
    request: portrait_service_pb.SearchPortraitsRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/SearchPortraits',
        request,
        metadata || {},
        this.methodDescriptorSearchPortraits,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/SearchPortraits',
    request,
    metadata || {},
    this.methodDescriptorSearchPortraits);
  }

  methodDescriptorFindPortraitById = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/FindPortraitById',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.FindPortraitByIdRequest,
    portrait_service_pb.Portrait,
    (request: portrait_service_pb.FindPortraitByIdRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.Portrait.deserializeBinary
  );

  findPortraitById(
    request: portrait_service_pb.FindPortraitByIdRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.Portrait>;

  findPortraitById(
    request: portrait_service_pb.FindPortraitByIdRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Portrait) => void): grpcWeb.ClientReadableStream<portrait_service_pb.Portrait>;

  findPortraitById(
    request: portrait_service_pb.FindPortraitByIdRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.Portrait) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/FindPortraitById',
        request,
        metadata || {},
        this.methodDescriptorFindPortraitById,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/FindPortraitById',
    request,
    metadata || {},
    this.methodDescriptorFindPortraitById);
  }

  methodDescriptorFindPortraitsByIds = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/FindPortraitsByIds',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.FindPortraitsByIdsRequest,
    portrait_service_pb.PortraitList,
    (request: portrait_service_pb.FindPortraitsByIdsRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.PortraitList.deserializeBinary
  );

  findPortraitsByIds(
    request: portrait_service_pb.FindPortraitsByIdsRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.PortraitList>;

  findPortraitsByIds(
    request: portrait_service_pb.FindPortraitsByIdsRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void): grpcWeb.ClientReadableStream<portrait_service_pb.PortraitList>;

  findPortraitsByIds(
    request: portrait_service_pb.FindPortraitsByIdsRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/FindPortraitsByIds',
        request,
        metadata || {},
        this.methodDescriptorFindPortraitsByIds,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/FindPortraitsByIds',
    request,
    metadata || {},
    this.methodDescriptorFindPortraitsByIds);
  }

  methodDescriptorFindPortraitsByIdsLibraries = new grpcWeb.MethodDescriptor(
    '/portrait.PortraitService/FindPortraitsByIdsLibraries',
    grpcWeb.MethodType.UNARY,
    portrait_service_pb.FindPortraitsByIdsLibrariesRequest,
    portrait_service_pb.PortraitList,
    (request: portrait_service_pb.FindPortraitsByIdsLibrariesRequest) => {
      return request.serializeBinary();
    },
    portrait_service_pb.PortraitList.deserializeBinary
  );

  findPortraitsByIdsLibraries(
    request: portrait_service_pb.FindPortraitsByIdsLibrariesRequest,
    metadata?: grpcWeb.Metadata | null): Promise<portrait_service_pb.PortraitList>;

  findPortraitsByIdsLibraries(
    request: portrait_service_pb.FindPortraitsByIdsLibrariesRequest,
    metadata: grpcWeb.Metadata | null,
    callback: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void): grpcWeb.ClientReadableStream<portrait_service_pb.PortraitList>;

  findPortraitsByIdsLibraries(
    request: portrait_service_pb.FindPortraitsByIdsLibrariesRequest,
    metadata?: grpcWeb.Metadata | null,
    callback?: (err: grpcWeb.RpcError,
               response: portrait_service_pb.PortraitList) => void) {
    if (callback !== undefined) {
      return this.client_.rpcCall(
        this.hostname_ +
          '/portrait.PortraitService/FindPortraitsByIdsLibraries',
        request,
        metadata || {},
        this.methodDescriptorFindPortraitsByIdsLibraries,
        callback);
    }
    return this.client_.unaryCall(
    this.hostname_ +
      '/portrait.PortraitService/FindPortraitsByIdsLibraries',
    request,
    metadata || {},
    this.methodDescriptorFindPortraitsByIdsLibraries);
  }

}

