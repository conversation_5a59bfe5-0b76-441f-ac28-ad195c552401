/**
 * @Author: yuz<PERSON>isme
 * @Date: 2025-07-24 10:39:04
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 14:40:37
 * @FilePath: \platform-face-web\src\components\Business\TagSelector\composables\useTagOperations.js
 * @Description: TagSelector 标签操作逻辑
 */

import { ref, computed } from 'vue';
import debounce from 'lodash/debounce';
import { message } from 'ant-design-vue';
import {
  isTagExists,
  isTagNameExists,
  filterSelectedTags,
  createTempTag,
  canAddMoreTags
} from '../utils/index.js';

/**
 * 标签操作逻辑
 * @param {Object} props - 组件props
 * @param {Function} emit - 组件emit函数
 * @returns {Object} 标签操作相关的状态和方法
 */
export function useTagOperations(props, emit) {
  // 响应式状态
  const searchText = ref('');
  const suggestions = ref([]);
  const loading = ref(false);
  const showSuggestions = ref(false);
  const activeSuggestionIndex = ref(-1);

  // 计算属性
  const canAddMore = computed(() => {
    return canAddMoreTags(props.modelValue, props.maxTags);
  });

  // 搜索标签的防抖函数
  const debouncedSearch = debounce(async (query) => {
    if (!query.trim()) {
      suggestions.value = [];
      showSuggestions.value = false;
      return;
    }

    loading.value = true;
    try {
      const result = await props.onSearch(query.trim());

      // 确保返回的是数组
      if (Array.isArray(result)) {
        // 过滤掉已选择的标签
        suggestions.value = filterSelectedTags(result, props.modelValue, props.uniqueKey);
        showSuggestions.value = true;
        activeSuggestionIndex.value = -1;
      } else {
        suggestions.value = [];
        showSuggestions.value = false;
      }

      emit('search', query.trim(), result);
    } catch (error) {
      console.error('搜索标签失败:', error);
      message.error('搜索标签失败，请重试');
      suggestions.value = [];
      showSuggestions.value = false;
    } finally {
      loading.value = false;
    }
  }, props.debounceDelay);

  // 处理输入
  const handleInput = (value) => {
    searchText.value = value;
    debouncedSearch(value);
  };

  // 处理输入框焦点
  const handleInputFocus = () => {
    if (suggestions.value.length > 0) {
      showSuggestions.value = true;
    }
  };

  // 处理输入框失焦
  const handleInputBlur = () => {
    // 延迟隐藏，以便点击建议项时能正常触发
    setTimeout(() => {
      showSuggestions.value = false;
      activeSuggestionIndex.value = -1;
    }, 200);
  };

  // 选择建议项
  const handleSelectSuggestion = (tag) => {
    if (!canAddMore.value) {
      message.warning(`最多只能选择 ${props.maxTags} 个标签`);
      return;
    }

    // 检查是否已存在
    if (isTagExists(props.modelValue, tag, props.uniqueKey)) {
      message.warning('该标签已存在');
      return;
    }

    const newTags = [...props.modelValue, tag];
    emit('update:modelValue', newTags);
    emit('change', newTags);

    // 清空输入
    clearInput();
  };

  // 添加新标签
  const handleAddTag = async () => {
    const tagName = searchText.value.trim();

    if (!tagName) {
      return;
    }

    if (!canAddMore.value) {
      message.warning(`最多只能选择 ${props.maxTags} 个标签`);
      return;
    }

    // 检查是否已存在同名标签
    if (isTagNameExists(props.modelValue, tagName)) {
      message.warning('该标签已存在');
      return;
    }

    // 如果不允许创建新标签，只能从建议中选择
    if (!props.allowCreate) {
      message.warning('请从建议列表中选择标签');
      return;
    }

    // 如果有onCreate函数，调用它创建新标签
    if (props.onCreate) {
      loading.value = true;
      try {
        const newTag = await props.onCreate(tagName);
        if (newTag) {
          const newTags = [...props.modelValue, newTag];
          emit('update:modelValue', newTags);
          emit('create', newTag);
          emit('change', newTags);
          message.success('标签创建成功');
        }
      } catch (error) {
        console.error('创建标签失败:', error);
        message.error('创建标签失败，请重试');
      } finally {
        loading.value = false;
      }
    } else {
      // 如果没有onCreate函数，直接创建本地标签
      const newTag = createTempTag(tagName, props.uniqueKey);
      const newTags = [...props.modelValue, newTag];
      emit('update:modelValue', newTags);
      emit('create', newTag);
      emit('change', newTags);
    }

    // 清空输入
    clearInput();
  };

  // 删除标签
  const handleRemoveTag = (tag) => {
    const newTags = props.modelValue.filter(item =>
      item[props.uniqueKey] !== tag[props.uniqueKey]
    );

    emit('update:modelValue', newTags);
    emit('remove', tag);
    emit('change', newTags);
  };

  // 清空输入
  const clearInput = () => {
    searchText.value = '';
    suggestions.value = [];
    showSuggestions.value = false;
    activeSuggestionIndex.value = -1;
  };

  // 键盘事件处理
  const handleKeydown = (e) => {
    if (!showSuggestions.value || suggestions.value.length === 0) {
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        activeSuggestionIndex.value = Math.min(
          activeSuggestionIndex.value + 1,
          suggestions.value.length - 1
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        activeSuggestionIndex.value = Math.max(
          activeSuggestionIndex.value - 1,
          -1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (activeSuggestionIndex.value >= 0) {
          handleSelectSuggestion(suggestions.value[activeSuggestionIndex.value]);
        } else {
          handleAddTag();
        }
        break;
      case 'Escape':
        showSuggestions.value = false;
        activeSuggestionIndex.value = -1;
        break;
    }
  };

  // 清理函数
  const cleanup = () => {
    debouncedSearch.cancel();
  };

  return {
    // 状态
    searchText,
    suggestions,
    loading,
    showSuggestions,
    activeSuggestionIndex,

    // 计算属性
    canAddMore,

    // 方法
    handleInput,
    handleInputFocus,
    handleInputBlur,
    handleSelectSuggestion,
    handleAddTag,
    handleRemoveTag,
    handleKeydown,
    clearInput,
    cleanup,
  };
}
