/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-11 10:21:51
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-11 16:35:07
 * @FilePath: \frontend-template\src\router\modules\tools.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import lineIcon from '@/assets/svg/menus/line.svg'
import lineActiveIcon from '@/assets/svg/menus/line-active.svg'

export default [
  {
    path: '/tools',
    name: 'Tools',
    meta: { title: '智能工具', icon: lineIcon, activeIcon: lineActiveIcon, menu: true, keepAlive: false, layout: 'main' },
    redirect: '/tools/text-search',
    children: [
      {
        path: 'text-search',
        name: 'ToolsTextSearch',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '图文检索', menu: true }
      },
      {
        path: 'file-search',
        name: 'ToolsFileSearch',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '档案检索', menu: true }
      },
      {
        path: 'pictrue-edit',
        name: 'ToolsPictrueEdit',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '图片编辑', menu: true }
      },
      {
        path: 'pictrue-break',
        name: 'ToolsPictrueBreak',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '图片分解', menu: true }
      },
    ]
  },
];