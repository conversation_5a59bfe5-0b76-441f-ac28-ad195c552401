<!--
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-14 17:38:38
 * @FilePath: \vue3-js-template-master\src\components\Error\404.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="error-bg">
    <div class="error-card">
      <div class="error-svg">
        <!-- 两边平衡的暗色系渐变幽灵SVG插画 -->
        <svg width="140" height="140" viewBox="0 0 140 140" fill="none" xmlns="http://www.w3.org/2000/svg">
          <!-- 幽灵发光阴影 -->
          <ellipse cx="70" cy="110" rx="42" ry="10" fill="url(#shadow)" opacity="0.8" />
          <!-- 幽灵主体 -->
          <defs>
            <radialGradient id="ghostBody" cx="0.5" cy="0.4" r="0.8">
              <stop offset="0%" stop-color="#b6cfff" />
              <stop offset="100%" stop-color="#3a4660" />
            </radialGradient>
            <linearGradient id="ghostEdge" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stop-color="#6ed0ff" />
              <stop offset="100%" stop-color="#232b3b" />
            </linearGradient>
            <radialGradient id="shadow" cx="0.5" cy="0.5" r="0.5">
              <stop offset="0%" stop-color="#6ed0ff" />
              <stop offset="100%" stop-color="#232b3b" />
            </radialGradient>
          </defs>
          <!-- 主体和边缘渐变 -->
          <ellipse cx="70" cy="75" rx="36" ry="32" fill="url(#ghostBody)" opacity="0.55" />
          <ellipse cx="70" cy="75" rx="36" ry="32" fill="url(#ghostEdge)" fill-opacity="0.28" />
          <!-- 幽灵底部波浪，严格对称 -->
          <!-- <path d="M34 95 Q44 105 54 95 Q64 105 74 95 Q84 105 94 95 Q104 105 114 95 Q114 120 34 120 Z" fill="url(#ghostBody)" /> -->
          <!-- 眼睛严格对称 -->
          <ellipse cx="58" cy="78" rx="4" ry="7" fill="#232b3b" />
          <ellipse cx="82" cy="78" rx="4" ry="7" fill="#232b3b" />
          <ellipse cx="58" cy="80" rx="1.2" ry="2" fill="#fff" opacity="0.7" />
          <ellipse cx="82" cy="80" rx="1.2" ry="2" fill="#fff" opacity="0.7" />
          <!-- 嘴巴居中 -->
          <ellipse cx="70" cy="92" rx="6" ry="2.5" fill="#6ed0ff" />
          <!-- 星星点缀，两侧对称分布 -->
          <g opacity="0.7">
            <circle cx="45" cy="62" r="1.5" fill="#fff" />
            <circle cx="95" cy="62" r="1.5" fill="#fff" />
            <circle cx="55" cy="50" r="1.2" fill="#b6cfff" />
            <circle cx="85" cy="50" r="1.2" fill="#b6cfff" />
            <circle cx="60" cy="44" r="1.1" fill="#6ed0ff" />
            <circle cx="80" cy="44" r="1.1" fill="#6ed0ff" />
            <circle cx="50" cy="90" r="1" fill="#b6cfff" />
            <circle cx="90" cy="90" r="1" fill="#b6cfff" />
          </g>
        </svg>
      </div>
      <div class="error-code">404</div>
      <div class="error-desc">抱歉，您访问的页面不存在或已被移除</div>
      <button class="back-btn" @click="goHome">返回首页</button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
const router = useRouter();
const goHome = () => {
  router.push('/');
};
</script>

<style lang="scss" scoped>
.error-bg {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #242b3a 0%, #242b3a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-card {
  background: rgba(86, 102, 134, 0.3);
  backdrop-filter: blur(14px);
  border-radius: 2.5rem;
  box-shadow: 0 8px 32px rgba(30, 60, 120, 0.22), 0 1.5px 8px rgba(0, 0, 0, 0.12);
  padding: 3rem 2.5rem 2.5rem 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 340px;
  animation: fadeIn 0.8s;
}

.error-svg {
  margin-bottom: 1.2rem;
  animation: float 2.5s infinite ease-in-out;
}

.error-code {
  font-size: 4.5rem;
  font-weight: 900;
  color: #6ed0ff;
  letter-spacing: 0.18em;
  text-shadow: 0 4px 24px rgba(110, 208, 255, 0.18);
  margin-bottom: 1.2rem;
  line-height: 1;
}

.error-desc {
  font-size: 1rem;
  color: #b6cfff;
  margin-bottom: 2.2rem;
}

.back-btn {
  position: relative;
  overflow: hidden;
  padding: 0.5em 1.8em;
  font-size: 1rem;
  color: #6ed0ff;
  background: transparent;
  border: 2px solid #6ed0ff;
  border-radius: 2em;
  cursor: pointer;
  box-shadow: 0 0 12px 0 #6ed0ff66;
  transition: border-color 0.2s, color 0.2s, background 0.2s;
  outline: none;
  font-weight: 700;
  letter-spacing: 0.04em;
  z-index: 1;
}
.back-btn::before {
  content: '';
  position: absolute;
  left: -60%;
  top: 0;
  width: 220%;
  height: 100%;
  background: linear-gradient(1000deg, transparent 0%, rgba(110, 208, 255, 0.333) 30%, rgba(79, 140, 255, 0.3) 50%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 0;
}
.back-btn:hover {
  border-color: #4f8cff;
  color: #fff;
  background: rgba(110, 208, 255, 0.1);
  box-shadow: 0 0 24px 0 #4f8cff99;
}
.back-btn:hover::before {
  opacity: 1;
  animation: flowLight 3s infinite;
}
@keyframes flowLight {
  0% {
    left: -60%;
  }
  100% {
    left: 60%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-12px);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
