<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 15:55:00
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 15:55:00
 * @FilePath: \platform-face-web\src\components\Business\GroupedImageDisplay\components\NestedLayout.vue
 * @Description: 嵌套布局组件 - 处理5-7张图片的嵌套网格布局
 -->
<template>
  <div class="nested-layout">
    <div class="main-grid">
      <!-- 前三张图片 -->
      <div 
        v-for="(image, index) in firstThreeImages" 
        :key="index" 
        class="main-grid-item"
      >
        <ImageItem
          :image="image"
          :selected-items="selectedItems"
          :ring-color="ringColor"
          :selected-ring-color="selectedRingColor"
          :ring-thickness="ringThickness"
          :thumbnail-size="mainThumbnailSize"
          @dragstart="handleDragStart"
          @click="handleClick"
        />
      </div>
      
      <!-- 嵌套子网格 -->
      <div class="main-grid-item nested-grid">
        <div class="nested-grid-container">
          <div 
            v-for="(image, index) in nestedImages" 
            :key="index" 
            class="nested-grid-item"
          >
            <ImageItem
              :image="image"
              :selected-items="selectedItems"
              :ring-color="ringColor"
              :selected-ring-color="selectedRingColor"
              :ring-thickness="ringThickness"
              :thumbnail-size="nestedThumbnailSize"
              @dragstart="handleDragStart"
              @click="handleClick"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ImageItem from './ImageItem.vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 前三张图片数据 */
  firstThreeImages: {
    type: Array,
    required: true
  },
  /** 嵌套网格中的图片数据 */
  nestedImages: {
    type: Array,
    required: true
  },
  /** 已选中的图片ID数组 */
  selectedItems: {
    type: Array,
    default: () => []
  },
  /** 默认边框颜色 */
  ringColor: {
    type: String,
    default: '#4c5f78'
  },
  /** 选中状态边框颜色 */
  selectedRingColor: {
    type: String,
    default: '#4DBFFF'
  },
  /** 边框厚度 */
  ringThickness: {
    type: Number,
    default: 0
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['dragstart', 'click']);

/**
 * 缩略图尺寸配置
 */
// 主网格缩略图尺寸
const mainThumbnailSize = {
  width: '54px',
  height: '36px',
  bottom: '2px',
  right: '2px'
};

// 嵌套网格缩略图尺寸
const nestedThumbnailSize = {
  width: '36px',
  height: '24px',
  bottom: '2px',
  right: '2px'
};

/**
 * 事件处理方法
 */
const handleDragStart = (event, image) => {
  emit('dragstart', event, image);
};

const handleClick = (event, image) => {
  emit('click', event, image);
};
</script>

<style lang="scss" scoped>
.nested-layout {
  width: 100%;
  height: 100%;
}

.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  width: 100%;
  height: 100%;
  gap: 2px;
}

.main-grid-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.nested-grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  width: 100%;
  height: 100%;
  gap: 2px;
}

.nested-grid-item {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
