import { ref } from 'vue';

export function useZoomPan() {
  const scale = ref(1);
  const offset = ref({ x: 0, y: 0 });
  const isPanning = ref(false);
  const lastMousePos = ref({ x: 0, y: 0 });

  const onWheel = (event) => {
    event.preventDefault();
    
    const zoomIntensity = 0.1;

    // 以鼠标位置为中心缩放：计算偏移调整
    const rect = event.currentTarget.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    const delta = event.deltaY < 0 ? 1 + zoomIntensity : 1 - zoomIntensity;
    const newScale = scale.value * delta;

    // 计算缩放前后鼠标点对应图像坐标的变化，调整 offset 实现锚点缩放
    const scaleRatio = newScale / scale.value;

    offset.value = {
      x: mouseX - scaleRatio * (mouseX - offset.value.x),
      y: mouseY - scaleRatio * (mouseY - offset.value.y)
    };

    scale.value = newScale;
  };

  const onMouseDown = (event) => {
    // 开始平移
    isPanning.value = true;
    lastMousePos.value = { x: event.clientX, y: event.clientY };
  };

  const onMouseMove = (event) => {
    if (!isPanning.value) return;
    // 计算鼠标移动差值，更新偏移
    const dx = event.clientX - lastMousePos.value.x;
    const dy = event.clientY - lastMousePos.value.y;
    offset.value.x += dx;
    offset.value.y += dy;
    lastMousePos.value = { x: event.clientX, y: event.clientY };
  };

  const onMouseUp = () => {
    isPanning.value = false;
  };

  // 更新状态时保留 6 位小数（你也可以根据情况调整为 3~6 位）
  const setZoomPanState = ({ scale: newScale, offset: newOffset }) => {
    // 保留 6 位小数，注意 toFixed 返回字符串，所以需要 parseFloat 转换为数字
    const preciseScale = parseFloat(newScale.toFixed(6));
    const preciseOffset = {
      x: parseFloat(newOffset.x.toFixed(6)),
      y: parseFloat(newOffset.y.toFixed(6))
    };
    scale.value = preciseScale;
    offset.value = preciseOffset;
  };
    
  return {
    scale,
    offset,
    isPanning,
    onWheel,
    onMouseDown,
    onMouseMove,
    onMouseUp,
    setZoomPanState,
  };
}