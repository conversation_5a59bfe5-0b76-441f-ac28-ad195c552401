<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-30 14:28:11
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-30 16:27:53
 * @FilePath: \platform-face-web\src\views\graph\components\BilateralContent.vue
 * @Description: 左右布局组件，支持可调节宽度和独立滚动条
-->
<template>
  <div class="bilateral-container" ref="containerRef">
    <!-- 左侧内容区域 -->
    <div class="left-panel" :style="{ width: leftWidth + 'px' }" ref="leftPanelRef">
      <div class="panel-content">
        <div class="panel-content-wrapper" ref="leftWrapperRef">
          <slot name="left"></slot>
        </div>
      </div>
    </div>

    <!-- 中间拖拽分栏 -->
    <div class="resize-handle" @mousedown="startResize" @wheel="handleWheel" ref="resizeHandleRef">
      <div class="resize-line"></div>
      <div class="resize-icon" @mousedown="startVerticalDrag" ref="dragIconRef" :style="{ top: iconTop + 'px' }">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M6 4h4v2H6V4zm0 5h4v2H6V9zm0 5h4v2H6v-2z" />
        </svg>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="right-panel" :style="{ width: rightWidth + 'px' }" ref="rightPanelRef">
      <div class="panel-content">
        <div class="panel-content-wrapper" ref="rightWrapperRef">
          <slot name="right"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BilateralContent',
  props: {
    // 最小宽度限制
    minWidth: {
      type: Number,
      default: 200
    },
    // 初始左侧宽度比例 (0-1)
    initialLeftRatio: {
      type: Number,
      default: 0.5
    }
  },
  data() {
    return {
      leftWidth: 0,
      rightWidth: 0,
      containerWidth: 0,
      isResizing: false,
      isVerticalDragging: false,
      startX: 0,
      startY: 0,
      startLeftWidth: 0,
      startScrollTop: { left: 0, right: 0 },
      scrollFeedbackTimer: null,
      iconTop: 0,
      handleHeight: 0
    }
  },
  mounted() {
    this.initLayout()
    this.addEventListeners()
    this.updateIconPosition()
  },
  beforeUnmount() {
    this.removeEventListeners()
  },
  methods: {
    // 初始化布局
    initLayout() {
      this.$nextTick(() => {
        this.containerWidth = this.$refs.containerRef.offsetWidth
        this.handleHeight = this.$refs.resizeHandleRef.offsetHeight
        const handleWidth = 12 // 拖拽手柄宽度
        const availableWidth = this.containerWidth - handleWidth

        this.leftWidth = Math.floor(availableWidth * this.initialLeftRatio)
        this.rightWidth = availableWidth - this.leftWidth

        // 确保最小宽度
        if (this.leftWidth < this.minWidth) {
          this.leftWidth = this.minWidth
          this.rightWidth = availableWidth - this.minWidth
        }
        if (this.rightWidth < this.minWidth) {
          this.rightWidth = this.minWidth
          this.leftWidth = availableWidth - this.minWidth
        }

        this.updateIconPosition()
      })
    },

    // 更新图标位置
    updateIconPosition() {
      const leftWrapper = this.$refs.leftWrapperRef
      const rightWrapper = this.$refs.rightWrapperRef

      if (leftWrapper && rightWrapper) {
        // 计算两边的最大滚动距离
        const leftMaxScroll = leftWrapper.scrollHeight - leftWrapper.clientHeight
        const rightMaxScroll = rightWrapper.scrollHeight - rightWrapper.clientHeight

        // 确定哪个区域有更长的滚动条（内容更多）
        const dominantSide = leftMaxScroll > rightMaxScroll ? 'left' : 'right'

        // 只根据主导区域的滚动位置来更新图标
        if (dominantSide === 'left') {
          const leftScrollRatio = leftWrapper.scrollTop / Math.max(1, leftMaxScroll)
          const maxScrollTop = this.handleHeight - 24
          this.iconTop = Math.max(0, Math.min(maxScrollTop, leftScrollRatio * maxScrollTop))
        } else {
          const rightScrollRatio = rightWrapper.scrollTop / Math.max(1, rightMaxScroll)
          const maxScrollTop = this.handleHeight - 24
          this.iconTop = Math.max(0, Math.min(maxScrollTop, rightScrollRatio * maxScrollTop))
        }
      }
    },

    // 添加事件监听器
    addEventListeners() {
      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)
      window.addEventListener('resize', this.handleResize)

      // 监听滚动事件
      this.$nextTick(() => {
        const leftWrapper = this.$refs.leftWrapperRef
        const rightWrapper = this.$refs.rightWrapperRef
        leftWrapper && leftWrapper.addEventListener('scroll', this.handleScroll)
        rightWrapper && rightWrapper.addEventListener('scroll', this.handleScroll)
      })
    },

    // 移除事件监听器
    removeEventListeners() {
      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)
      window.removeEventListener('resize', this.handleResize)

      const leftWrapper = this.$refs.leftWrapperRef
      const rightWrapper = this.$refs.rightWrapperRef

      if (leftWrapper) {
        leftWrapper.removeEventListener('scroll', this.handleScroll)
      }
      if (rightWrapper) {
        rightWrapper.removeEventListener('scroll', this.handleScroll)
      }
    },

    // 处理滚动事件
    handleScroll() {
      !this.isVerticalDragging && this.updateIconPosition()
    },

    // 开始拖拽调整大小
    startResize(event) {
      // 如果点击的是图标，不启动宽度调整
      if (event.target.closest('.resize-icon')) return
      event.preventDefault()
      this.isResizing = true
      this.startX = event.clientX
      this.startLeftWidth = this.leftWidth

      // 添加拖拽时的样式
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    },

    // 开始垂直拖拽
    startVerticalDrag(event) {
      event.preventDefault()
      event.stopPropagation()

      this.isVerticalDragging = true
      this.startY = event.clientY
      this.startIconTop = this.iconTop

      // 记录当前滚动位置
      const leftWrapper = this.$refs.leftWrapperRef
      const rightWrapper = this.$refs.rightWrapperRef

      if (leftWrapper && rightWrapper) {
        this.startScrollTop.left = leftWrapper.scrollTop
        this.startScrollTop.right = rightWrapper.scrollTop
      }

      // 添加拖拽时的样式
      document.body.style.cursor = 'ns-resize'
      document.body.style.userSelect = 'none'

      // 添加拖拽时的视觉反馈
      this.$refs.dragIconRef.classList.add('dragging')
    },

    // 处理鼠标移动
    handleMouseMove(event) {
      if (this.isResizing) {
        const deltaX = event.clientX - this.startX
        const newLeftWidth = this.startLeftWidth + deltaX
        const handleWidth = 12
        const availableWidth = this.containerWidth - handleWidth

        // 限制最小宽度
        if (newLeftWidth >= this.minWidth && newLeftWidth <= availableWidth - this.minWidth) {
          this.leftWidth = newLeftWidth
          this.rightWidth = availableWidth - newLeftWidth
        }
      } else if (this.isVerticalDragging) {
        const deltaY = event.clientY - this.startY

        const leftWrapper = this.$refs.leftWrapperRef
        const rightWrapper = this.$refs.rightWrapperRef

        if (leftWrapper && rightWrapper) {
          // 使用requestAnimationFrame确保流畅的动画
          requestAnimationFrame(() => {
            // 直接更新图标位置，实时跟随鼠标移动
            const maxScrollTop = this.handleHeight - 24
            const newIconTop = this.startIconTop + deltaY
            this.iconTop = Math.max(0, Math.min(maxScrollTop, newIconTop))

            // 根据图标位置计算滚动比例
            const scrollRatio = newIconTop / maxScrollTop

            // 计算两边的最大滚动距离
            const leftMaxScroll = leftWrapper.scrollHeight - leftWrapper.clientHeight
            const rightMaxScroll = rightWrapper.scrollHeight - rightWrapper.clientHeight

            // 同步控制两边的滚动位置
            leftWrapper.scrollTop = scrollRatio * leftMaxScroll
            rightWrapper.scrollTop = scrollRatio * rightMaxScroll
          })
        }
      }
    },

    // 处理鼠标释放
    handleMouseUp() {
      this.isVerticalDragging && this.$refs.dragIconRef.classList.remove('dragging')
      this.isResizing = false
      this.isVerticalDragging = false
      // 移除拖拽时的视觉反馈
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    },

    // 处理窗口大小变化
    handleResize() {
      this.initLayout()
    },

    // 处理滚轮事件 - 同步滚动两边内容
    handleWheel(event) {
      event.preventDefault()

      const leftWrapper = this.$refs.leftWrapperRef
      const rightWrapper = this.$refs.rightWrapperRef

      if (leftWrapper && rightWrapper) {
        const scrollAmount = event.deltaY * 0.5 // 减缓滚动速度

        // 同步滚动两边内容
        leftWrapper.scrollTop += scrollAmount
        rightWrapper.scrollTop += scrollAmount

        // 更新图标位置
        this.updateIconPosition()

        // 添加滚动时的视觉反馈
        this.showScrollFeedback()
      }
    },

    // 显示滚动反馈
    showScrollFeedback() {
      const hint = this.$refs.resizeHandleRef.querySelector('.scroll-hint')
      if (hint) {
        hint.style.opacity = '1'
        clearTimeout(this.scrollFeedbackTimer)
        this.scrollFeedbackTimer = setTimeout(() => {
          hint.style.opacity = '0'
        }, 1000)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bilateral-container {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.left-panel,
.right-panel {
  height: 100%;
  overflow: hidden;
  position: relative;
}

.panel-content {
  height: 100%;
  padding: 16px 0;
  box-sizing: border-box;

  &-wrapper {
    padding: 0 16px;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

/* 自定义滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.resize-handle {
  width: 12px;
  height: 100%;
  background: linear-gradient(to right, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%);
  cursor: col-resize;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  user-select: none;
  transition: all 0.2s ease;
  border-left: 1px solid #dee2e6;
  border-right: 1px solid #dee2e6;
}

.resize-handle:hover {
  background: linear-gradient(to right, #e3f2fd 0%, #bbdefb 50%, #e3f2fd 100%);
  border-left-color: #2196f3;
  border-right-color: #2196f3;
}

.resize-line {
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, #dee2e6 0%, #adb5bd 50%, #dee2e6 100%);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 1px;
}

.resize-handle:hover .resize-line {
  background: linear-gradient(to bottom, #2196f3 0%, #1976d2 50%, #2196f3 100%);
  width: 3px;
}

.resize-icon {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: #6c757d;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 3px;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: ns-resize;
  user-select: none;
}

.resize-icon:hover {
  opacity: 1;
  color: #2196f3;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.2);
  transform: translateX(-50%) scale(1.1);
}

.resize-icon.dragging {
  color: #2196f3;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  transform: translateX(-50%) scale(1.2);
}

/* 拖拽时的样式 */
.bilateral-container.resizing {
  cursor: col-resize;
}

.bilateral-container.resizing .resize-handle {
  background: linear-gradient(to right, #e3f2fd 0%, #bbdefb 50%, #e3f2fd 100%);
  border-left-color: #2196f3;
  border-right-color: #2196f3;
}

.bilateral-container.resizing .resize-line {
  background: linear-gradient(to bottom, #2196f3 0%, #1976d2 50%, #2196f3 100%);
  width: 3px;
}

.bilateral-container.resizing .resize-icon {
  color: #2196f3;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
}
</style>
