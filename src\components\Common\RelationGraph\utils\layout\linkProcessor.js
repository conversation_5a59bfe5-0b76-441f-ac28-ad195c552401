/**
 * 链接处理模块
 * 负责处理单中心图和多中心图的链接预处理逻辑
 */

/**
 * 给link增加一个__key值
 * @param {*} links
 * @returns
 */
export function preprocessLinks(links) {
    const nodeLinkMap = {};
    const linkKeyArr = []; // 给连线数据增加一个__key的值

    links.forEach((link) => {
        // 创建复合键：source+target 排序后组合，确保 A-B 和 B-A 被视为同一条线
        const sortedPair = [link.source, link.target].sort().join('|');
        link.__key = sortedPair; // 动态添加缓存键

        if (!nodeLinkMap[link.source]) nodeLinkMap[link.source] = new Set();
        if (!nodeLinkMap[link.target]) nodeLinkMap[link.target] = new Set();

        nodeLinkMap[link.source].add(sortedPair);
        nodeLinkMap[link.target].add(sortedPair);
        linkKeyArr.push(link);
    });

    return { nodeLinkMap, linkKeyArr };
}

/**
 * 预处理多中心连线数据
 * @param {Link[]} links - 所有连线数据
 * @param {string[]} centers - 中心节点ID数组
 * @returns {Object} { nodeLinkMap, linkGroups }
 */
export function preprocessMultiLinks(links, centers) {
    const nodeLinkMap = {};
    const linkGroups = {
        intraGroup: [], // 组内连线
        interGroup: [], // 跨组连线
        centerLinks: [], // 中心节点间连线
    };

    links.forEach((link) => {
        // 创建复合键
        const sortedPair = [link.source, link.target].sort().join('|');
        link.__key = sortedPair;

        // 更新节点连线映射
        if (!nodeLinkMap[link.source]) nodeLinkMap[link.source] = new Set();
        if (!nodeLinkMap[link.target]) nodeLinkMap[link.target] = new Set();
        nodeLinkMap[link.source].add(sortedPair);
        nodeLinkMap[link.target].add(sortedPair);

        // 分类连线
        const isSourceCenter = centers.includes(link.source);
        const isTargetCenter = centers.includes(link.target);

        if (isSourceCenter && isTargetCenter) {
            linkGroups.centerLinks.push(link);
        } else if (isSourceCenter || isTargetCenter) {
            linkGroups.interGroup.push(link);
        } else {
            linkGroups.intraGroup.push(link);
        }
    });

    return { nodeLinkMap, linkGroups };
}
