# RoseDiagram 玫瑰图组件

## 组件简介 | Introduction

`RoseDiagram` 是一个基于 SVG 实现的自定义玫瑰图（极坐标柱状图）Vue 3 组件，支持动态渲染多扇区、百分比高亮、标签与刻度等功能，适用于数据可视化场景。

`RoseDiagram` is a custom SVG-based rose diagram (polar bar chart) Vue 3 component. It supports dynamic sectors, percentage highlighting, labels, and ticks, suitable for data visualization.

---

## 属性说明 | Props

| 属性名         | 类型     | 默认值    | 说明（中文）                 | Description (EN)                |
| -------------- | -------- | --------- | ---------------------------- | ------------------------------- |
| circleList     | Array    | 见下方    | 扇区数据列表，每项包含 label、value、color | Sector data list, each with label, value, color |
| size           | Number   | 200       | 图表宽高（正方形）           | Chart width/height (square)     |
| centerColor    | String   | #1D2028   | 中心圆颜色                   | Center circle color             |

### circleList 示例 | Example
```js
[
  { label: '归档', value: 20, color: '#fff' },
  { label: '上传', value: 100, color: '#3a7adf' },
  { label: '归一', value: 80, color: '#3a7adf' },
  { label: '比对', value: 70, color: '#3a7adf' },
  { label: '关联', value: 50, color: '#3a7adf' },
]
```
- `label`：扇区名称 | Sector name
- `value`：百分比（0~100）| Percentage (0~100)
- `color`：扇区高亮色 | Highlight color

---

## 用法示例 | Usage Example

```vue
<template>
  <RoseDiagram :circleList="dataList" :size="260" centerColor="#222" />
</template>

<script setup>
import RoseDiagram from './Index.vue';

const dataList = [
  { label: '归档', value: 20, color: '255,255,255' },
  { label: '上传', value: 100, color: '58,122,223' },
  { label: '归一', value: 80, color: '58,122,223' },
  { label: '比对', value: 70, color: '58,122,223' },
  { label: '关联', value: 50, color: '58,122,223' },
];
</script>
```

---

## 注意事项 | Notes
- `color` 支持 `rgb(r,g,b)` 格式或十六进制色值（如 `#3a7adf`）。
- `circleList` 数组长度决定扇区数量，建议 3 个及以上。
- 组件自适应 `size`，但建议宽高不小于 120。
- 组件为纯展示型，无交互事件。

---

## 样式自定义 | Style Customization
可通过覆盖 `.circle-label`, `.circle-tick`, `.circle-center-wrap` 等类名自定义字体、颜色等样式。

---

## 贡献与维护 | Contribution & Maintenance
如需扩展功能或修复 bug，欢迎提交 PR 或 issue。

---

## License
MIT 