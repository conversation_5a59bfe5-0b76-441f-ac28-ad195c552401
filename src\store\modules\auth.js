import auth from '@/utils/auth';
import { urlUtils } from '@/utils/urlUtils';
import { login, getKoalCookie, logout } from '@/api/auth';
import { initializeWebSocketManager } from '@/websocket';
const state = {
  LOGIN_LOADING: false,
  forceUpdatePassword: false
};

const mutations = {};

const actions = {
  login: async (context, payload) => {
    const { $message, $router } = window.app.config.globalProperties;
    try {
      const { data: result } = await login(payload.keyNum, payload.userName, payload.password);

      if (result.authInfo) {
        auth.setAuthInfo(result.authInfo);
      }

      if (result.userInfo) {
        auth.setUserInfo(result.userInfo);
      }

      if (result.oldToken) {
        auth.setToken(result.oldToken);
      }

      if (result.token) {
        auth.setJWTInfo(result.token, true);
      }

      if (result.userId) {
        await context.dispatch('getInfosAfterLogin');

        // 若是重定向，跳回'重定向地址'
        $router.push(urlUtils.getFromParam());
      }
    } catch (error) {
      console.log('error =', error);
      $message.error(error.errHint || error.errMsg || '用户名或密码错误，或用户被禁用，登录失败');
    }
  },
  getInfosAfterLogin: async () => {
    try {
      if (auth.isLogin()) {
        const accessPermissions = auth.getAuthInfo().permissions;

        // 校验可显示的菜单列表
        const checkAccessMenus = (menus, accessMenus) => {
          if (menus) {
            menus.forEach((val) => {
              // if (accessPermissions.indexOf(val.path) > -1) {
              // 当满足权限，加入集合
              const item = JSON.parse(JSON.stringify(val));
              item.children = [];
              const index = accessMenus.push(item);
              if (val.children) {
                checkAccessMenus(val.children, accessMenus[index - 1].children);
              }
              // }
            });
          }
        };

        // 执行校验
        const accessMenus = [];
        checkAccessMenus(auth.getMenus(), accessMenus);

        sessionStorage.setItem('menus', JSON.stringify(accessMenus));

        // 登录成功后才初始化 WebSocketManager
        console.log('After login, initializeWebSocketManager');
        initializeWebSocketManager();
      }
    } catch (error) {
      console.error('error =', error);
      const { $message } = window.app.config.globalProperties;
      $message.error(error);
    }
  },
  logout: async () => {
    const userInfo = auth.getUserInfo();
    const authInfo = auth.getAuthInfo();
    const payload = {
      userId: userInfo.id,
      ...authInfo
    };

    const { $router } = window.app.config.globalProperties;
    try {
      await logout(payload);
    } catch (e) {
      // 登出出现异常可以直接跳转到登陆页，故仅捕获不进行处理
    }
    // 调用 logout 成功后，才清空前端的 auth 认证相关信息。
    auth.clearAuthInfo();
    auth.clearUserInfo();
    auth.clearJWTInfo();
    auth.clearTokenInfo();
    sessionStorage.clear();
    $router.push(config.loginUrl);
  },

  getKoalCookie: async (context, callback) => {
    const result = await getKoalCookie();
    if (callback) callback(result);
  }
};

export default {
  namespaced: true,
  state: () => ({ ...state }),
  mutations,
  actions
};
