# FaceCanvas 人脸框画布组件

## 📖 组件概述

FaceCanvas 是一个功能强大的人脸框绘制和展示组件，支持图像显示、人脸框渲染、手动绘制等功能。主要用于人脸识别、标注和编辑场景。

## ✨ 主要功能

- 🖼️ **图像显示**：支持各种格式图像的加载和显示
- 🎯 **人脸框渲染**：自动渲染检测到的人脸框和手动绘制的人脸框
- ✏️ **手动绘制**：支持鼠标拖拽绘制新的人脸框
- 🎨 **样式定制**：不同类型人脸框有不同的视觉样式
- 📱 **响应式设计**：自适应容器尺寸变化

## 🏗️ 组件架构

```
FaceCanvas/
├── index.vue          # 主组件
├── FaceBox.vue        # 人脸框子组件
└── README.md          # 文档
```

### 相关组合式函数

- `useCanvasDraw` - 处理绘制逻辑
- `useZoomPan` - 处理缩放平移
- `useCanvasRenderer` - Canvas渲染逻辑
- `useFaceCanvasLogic` - 核心业务逻辑

## 📋 Props 属性

| 属性名            | 类型    | 必填 | 默认值 | 说明                       |
| ----------------- | ------- | ---- | ------ | -------------------------- |
| `imageSrc`        | String  | ✅   | -      | 图像源地址                 |
| `faceBoxes`       | Array   | ❌   | `[]`   | 自动检测的人脸框数组       |
| `manualFaceBoxes` | Array   | ❌   | `[]`   | 手动绘制的人脸框数组       |
| `selectedItems`   | Array   | ❌   | `[]`   | 当前选中的人脸框数组       |
| `editable`        | Boolean | ❌   | `true` | 是否允许编辑（绘制和选择） |

### 人脸框数据结构

```javascript
{
  x: Number,              // X坐标
  y: Number,              // Y坐标
  width: Number,          // 宽度
  height: Number,         // 高度
  key: String,            // 唯一标识（可选）
  manualSelection: Boolean // 是否为手动绘制（可选）
}
```

## 🎪 Events 事件

| 事件名        | 参数                     | 说明                 |
| ------------- | ------------------------ | -------------------- |
| `boxDrawn`    | `{ face, croppedImage }` | 绘制完成人脸框时触发 |
| `boxSelected` | `key`                    | 选择人脸框时触发     |

### 事件参数详情

- `boxDrawn.face`: 新绘制的人脸框坐标信息
- `boxDrawn.croppedImage`: 裁剪后的人脸图像Base64数据
- `boxSelected.key`: 被选中人脸框的唯一标识

## 🎨 样式系统

### 人脸框样式类型

1. **自动检测人脸框**

   - 未选中：灰色四角框 `#9EA6B2`
   - 选中：绿色四角框 `#00E875`

2. **手动绘制人脸框**
   - 绘制中：蓝色虚线矩形 + 浅蓝色填充
   - 完成后：蓝色虚线矩形，无填充

### CSS类名

- `.face-box` - 基础人脸框样式
- `.face-box.auto` - 自动检测的人脸框
- `.face-box.manual` - 手动绘制的人脸框
- `.face-box.selected` - 选中状态
- `.face-box.drawing` - 绘制中状态

## 🚀 使用示例

### 基础用法

```vue
<template>
  <FaceCanvas
    :image-src="imageUrl"
    :face-boxes="detectedFaces"
    :manual-face-boxes="manualFaces"
    :selected-items="selectedFaces"
    @box-drawn="handleBoxDrawn"
    @box-selected="handleBoxSelected" />
</template>

<script setup>
import FaceCanvas from '@/components/Common/FaceCanvas/index.vue';

const imageUrl = ref('path/to/image.jpg');
const detectedFaces = ref([{ x: 100, y: 100, width: 80, height: 80, key: 'face1' }]);
const manualFaces = ref([]);
const selectedFaces = ref([]);

const handleBoxDrawn = ({ face, croppedImage }) => {
  console.log('新绘制的人脸框:', face);
  console.log('裁剪图像:', croppedImage);
};

const handleBoxSelected = (key) => {
  console.log('选中的人脸框:', key);
};
</script>
```

### 只读模式

```vue
<FaceCanvas :image-src="imageUrl" :face-boxes="faces" :editable="false" />
```

## 🎮 交互操作

| 操作         | 功能                 |
| ------------ | -------------------- |
| 鼠标拖拽     | 绘制新的人脸框       |
| 点击人脸框   | 选择/取消选择人脸框  |
| Shift + 拖拽 | 平移画布             |
| 鼠标滚轮     | 缩放画布（暂时禁用） |

## ⚠️ 注意事项

1. **图像跨域**：确保图像资源支持跨域访问
2. **性能优化**：大图像建议先压缩再使用
3. **坐标系统**：所有坐标基于原始图像尺寸
4. **浏览器兼容**：需要支持Canvas API的现代浏览器

## 🔧 开发指南

### 添加新功能

1. 在相应的组合式函数中添加逻辑
2. 更新Props和Events定义
3. 添加相应的样式类
4. 更新文档和示例

### 样式定制

修改 `FaceBox.vue` 中的样式常量：

```javascript
const FACE_BOX_COLORS = {
  autoDefault: '#9EA6B2',
  autoSelected: '#00E875',
  manual: '#4DBFFF',
  drawing: '#4DBFFF',
};
```

## 📝 更新日志

- **v2.0.0** - 重构组件架构，提升性能和可维护性
- **v1.5.0** - 添加手动绘制功能
- **v1.0.0** - 初始版本，基础人脸框显示功能
