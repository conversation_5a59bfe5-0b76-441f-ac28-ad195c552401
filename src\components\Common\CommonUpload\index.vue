<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-17 15:14:52
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-18 17:19:36
 * @FilePath: \platform-face-web\src\components\Common\Upload\Index.vue
 * @Description: 通用上传组件，支持插槽自定义上传区域
-->
<template>
  <div style="width: max-content;" @click="showUploadBtn ? triggerUpload() : null">
    <slot v-if="!showUploadBtn">
      <!-- 单图上传且已上传时，显示图片和删除按钮 -->
      <div v-if="isSingleImageUploaded" class="single-image-uploaded">
        <img :src="previewList[0].url" :alt="previewList[0].name" class="uploaded-img" />
        <span class="delete-btn" @click.stop="handleRemove(0)">×</span>
      </div>
    </slot>
    <slot v-else>
      <!-- 默认内容：根据type类型切换 -->
      <template v-if="type === 'image'">
        <div class="upload-btn-img">
          <span style="font-size: 36px; line-height: 1;">+</span>
          <span style="font-size: 14px; color: #999; margin-top: 8px;">上传</span>
        </div>
      </template>
      <template v-else>
        <button class="upload-btn-file">
          <span>上传</span>
        </button>
      </template>
    </slot>
    <input ref="inputRef" type="file" :accept="acceptType" :multiple="multiple" style="display: none"
      @change="handleChange" />
  </div>
  <!-- 回显区域，支持插槽自定义 -->
  <slot name="preview" :list="previewList" :type="type">
    <div v-if="previewList && previewList.length && !isSingleImageUploaded" class="upload-preview-list">
      <template v-if="type === 'image'">
        <div v-for="(item, idx) in previewList" :key="item.url || item.name || idx" class="upload-preview-item">
          <img :src="item.url" :alt="item.name" style="max-width: 120px; max-height: 120px;" />
          <div>{{ item.name }}</div>
        </div>
      </template>
      <template v-else>
        <div v-for="(item, idx) in previewList" :key="item.url || item.name || idx" class="upload-preview-item">
          <a :href="item.url" target="_blank" rel="noopener">{{ item.name }}</a>
        </div>
      </template>
    </div>
  </slot>
</template>

<script setup>
import { ref, computed } from 'vue';

// 组件props定义，支持自定义上传接口、请求头、额外参数、文件类型、是否多选、钩子及回调
const props = defineProps({
  // 上传接口地址，可选。如果未传，需通过beforeUpload自定义上传逻辑
  action: { type: String },
  // 请求头，可选
  headers: { type: Object, default: () => ({}) },
  // 额外参数，会一同上传，可选
  data: { type: Object, default: () => ({}) },
  // 允许上传的文件类型，如'image/*'，可选。若未设置，将根据type自动设置
  accept: { type: String, default: '' },
  // 上传类型，区分图片/文件/全部，默认'all'，可选值：'all' | 'image' | 'file'
  type: { type: String, default: 'all' },
  // 是否支持多文件上传，默认false
  multiple: { type: Boolean, default: false },
  // 上传前钩子，返回false可中断上传。若无action时，需在此自定义上传逻辑
  beforeUpload: { type: Function },
  // 上传成功回调
  onSuccess: { type: Function },
  // 上传失败回调
  onError: { type: Function },
  // 上传状态变化回调
  onChange: { type: Function },
  // 回显文件/图片列表，数组。每项建议包含url和name字段
  previewList: { type: Array, default: () => [] }
});
const emit = defineEmits(['remove']);

// 判断是否为单图上传且已上传
const isSingleImageUploaded = computed(() =>
  props.type === 'image' && !props.multiple && props.previewList && props.previewList.length === 1
);
// 控制上传按钮显示
const showUploadBtn = computed(() => !isSingleImageUploaded.value);

function handleRemove(idx) {
  // 通知父组件删除
  emit('remove', idx);
}

// 计算最终accept类型，优先使用用户自定义accept，否则根据type自动设置
const acceptType = computed(() => {
  if (props.accept) return props.accept;
  if (props.type === 'image') return 'image/*';
  if (props.type === 'file') return '*';
  return '';
});

// input元素引用，用于触发文件选择
const inputRef = ref(null);

// 触发文件选择弹窗
function triggerUpload() {
  inputRef.value && inputRef.value.click();
}

// 处理文件选择及上传逻辑
async function handleChange(e) {
  const files = e.target.files;
  if (!files.length) return;

  // 支持多文件上传
  for (const file of files) {
    // 上传前钩子，返回false则跳过该文件
    if (props.beforeUpload) {
      // 若beforeUpload返回false则跳过
      const before = await props.beforeUpload(file);
      if (before === false) continue;
      // 若未传action，直接交由beforeUpload处理（如自定义上传），跳过默认上传逻辑
      if (!props.action) continue;
    } else if (!props.action) {
      // 未传action且无beforeUpload，无法上传
      continue;
    }

    // 构造FormData，添加文件及额外参数
    const formData = new FormData();
    formData.append('file', file);
    Object.entries(props.data).forEach(([k, v]) => formData.append(k, v));

    try {
      // 发送上传请求
      const res = await fetch(props.action, {
        method: 'POST',
        headers: props.headers,
        body: formData
      });
      const result = await res.json();
      // 上传成功回调
      props.onSuccess && props.onSuccess(result, file);
      // 状态变化回调
      props.onChange && props.onChange({ file, status: 'success', response: result });
    } catch (err) {
      // 上传失败回调
      props.onError && props.onError(err, file);
      // 状态变化回调
      props.onChange && props.onChange({ file, status: 'error', error: err });
    }
  }
  // 清空 input，避免同一文件无法重复上传
  e.target.value = '';
}
</script>

<style scoped>
.single-image-uploaded {
  position: relative;
  width: 120px;
  height: 120px;
  display: inline-block;
}
.uploaded-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
}
.delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 22px;
  height: 22px;
  background: rgba(0,0,0,0.5);
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 22px;
  font-size: 16px;
  cursor: pointer;
  z-index: 2;
  transition: background 0.2s;
}
.delete-btn:hover {
  background: #ff4d4f;
}
.upload-btn-img {
  width: 120px;
  height: 120px;
  border: 1px dashed #1890ff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #1890ff;
  font-size: 28px;
  transition: border-color 0.2s, background 0.2s;
}
.upload-btn-file {
  display: inline-flex;
  align-items: center;
  padding: 8px 20px;
  font-size: 15px;
  border-radius: 6px;
  border: 1px solid #1890ff;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.2s;
  outline: none;
  gap: 6px;
  background: transparent;
}
.upload-btn-file:hover {
  background: #1890ff;
  color: #fff;
}
</style>
