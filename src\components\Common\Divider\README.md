# Divider 分割线组件

## 组件描述

Divider 是一个轻量级的分割线组件，用于在页面中创建视觉分隔，支持水平和垂直两种方向，提供灵活的样式配置。

## 功能特性

- ✅ **双向支持**：支持水平和垂直两种分割线方向
- ✅ **样式灵活**：支持实线和虚线两种线条样式
- ✅ **颜色自定义**：支持自定义分割线颜色
- ✅ **尺寸控制**：支持自定义宽度、高度和边距
- ✅ **轻量级**：简洁的实现，性能优异
- ✅ **响应式**：支持响应式布局

## 使用场景

- 内容区域分隔
- 菜单项分隔
- 表单字段分组
- 卡片内容分隔
- 侧边栏分隔

## Props 参数

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| direction | String | 'horizontal' | 否 | 分割线方向，可选值：'horizontal'、'vertical' |
| type | String | 'solid' | 否 | 线条样式，可选值：'solid'、'dashed' |
| color | String | 'rgba(184, 221, 255, .12)' | 否 | 分割线颜色 |
| width | String | '100%' | 否 | 分割线宽度（水平方向时生效） |
| height | String | '100%' | 否 | 分割线高度（垂直方向时生效） |
| marginSize | String | '8px' | 否 | 分割线边距大小 |

### Props 详细说明

#### direction
- **类型**：`String`
- **可选值**：`'horizontal'` | `'vertical'`
- **说明**：控制分割线的方向
- **示例**：`'horizontal'` - 水平分割线，`'vertical'` - 垂直分割线

#### type
- **类型**：`String`
- **可选值**：`'solid'` | `'dashed'`
- **说明**：控制分割线的样式
- **示例**：`'solid'` - 实线，`'dashed'` - 虚线

#### color
- **类型**：`String`
- **说明**：分割线颜色，支持任何有效的CSS颜色值
- **示例**：`'#ccc'`、`'red'`、`'rgba(255, 0, 0, 0.5)'`

#### width
- **类型**：`String`
- **说明**：水平分割线的宽度
- **示例**：`'100%'`、`'200px'`、`'50vw'`

#### height
- **类型**：`String`
- **说明**：垂直分割线的高度
- **示例**：`'100%'`、`'50px'`、`'20vh'`

#### marginSize
- **类型**：`String`
- **说明**：分割线的边距大小
- **示例**：`'8px'`、`'1rem'`、`'16px'`

## 使用示例

### 基础用法

```vue
<template>
  <!-- 默认水平分割线 -->
  <Divider />
  
  <!-- 垂直分割线 -->
  <Divider direction="vertical" />
</template>

<script setup>
import Divider from '@/components/Common/Divider/index.vue'
</script>
```

### 自定义样式

```vue
<template>
  <!-- 自定义颜色和样式 -->
  <Divider 
    color="#1890ff" 
    type="dashed" 
    marginSize="16px" 
  />
  
  <!-- 自定义宽度的水平分割线 -->
  <Divider 
    width="50%" 
    color="rgba(255, 0, 0, 0.3)" 
  />
</template>
```

### 垂直分割线

```vue
<template>
  <div style="display: flex; align-items: center; height: 40px;">
    <span>菜单项1</span>
    <Divider 
      direction="vertical" 
      height="20px" 
      color="#d9d9d9"
      marginSize="12px"
    />
    <span>菜单项2</span>
    <Divider 
      direction="vertical" 
      height="20px" 
      color="#d9d9d9"
      marginSize="12px"
    />
    <span>菜单项3</span>
  </div>
</template>
```

### 在表单中使用

```vue
<template>
  <div class="form-section">
    <h3>基本信息</h3>
    <a-form-item label="姓名">
      <a-input />
    </a-form-item>
    <a-form-item label="邮箱">
      <a-input />
    </a-form-item>
    
    <!-- 分组分割线 -->
    <Divider color="#f0f0f0" marginSize="24px" />
    
    <h3>联系信息</h3>
    <a-form-item label="电话">
      <a-input />
    </a-form-item>
    <a-form-item label="地址">
      <a-input />
    </a-form-item>
  </div>
</template>
```

### 在卡片中使用

```vue
<template>
  <a-card title="用户信息">
    <p>用户名：张三</p>
    <p>邮箱：<EMAIL></p>
    
    <Divider color="#e8e8e8" />
    
    <p>注册时间：2024-01-01</p>
    <p>最后登录：2024-07-16</p>
  </a-card>
</template>
```

## 技术实现

### 核心特性

1. **响应式样式**：使用Vue 3的computed属性动态计算样式
2. **CSS变量绑定**：使用v-bind在CSS中绑定响应式数据
3. **条件样式**：根据direction属性应用不同的边框样式
4. **轻量实现**：最小化的代码实现，性能优异

### 样式计算逻辑

- **水平分割线**：使用`border-top`创建，宽度可自定义
- **垂直分割线**：使用`border-left`创建，高度可自定义
- **边距控制**：通过CSS变量动态控制margin值
- **固定线宽**：统一使用0.5px的线条宽度，确保视觉一致性

## 注意事项

1. **容器高度**：垂直分割线需要父容器有明确的高度
2. **布局配合**：垂直分割线通常配合flex布局使用
3. **颜色对比**：建议选择与背景有适当对比度的颜色
4. **响应式**：在移动端可能需要调整marginSize以适应小屏幕

## 常见问题

### Q: 垂直分割线不显示？
A: 检查父容器是否有高度，垂直分割线需要明确的容器高度才能显示。

### Q: 如何实现渐变色分割线？
A: 可以通过color属性传入CSS渐变值，如：`color="linear-gradient(to right, #ff0000, #0000ff)"`

### Q: 分割线太粗或太细？
A: 组件内部固定使用0.5px线宽，如需调整可以通过自定义CSS覆盖。

## 更新日志

### v1.0.0 (2025-07-16)
- ✅ 初始版本发布
- ✅ 支持水平和垂直分割线
- ✅ 支持实线和虚线样式
- ✅ 支持自定义颜色、尺寸和边距
- ✅ 完善的Props验证
- ✅ 响应式样式支持
