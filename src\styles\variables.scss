/**
1. 基于功能命名
- 变量名应能反映其用途，而不是具体值。例如：
- 不推荐：`@blue`、`@red`（表示颜色，但没有语义）。
- 推荐：`@primary-color`、`@success-color`（表示功能）。
示例：
```less
@primary-color: #1890ff;        // 主色
@success-color: #52c41a;       // 成功提示色
@error-color: #ff4d4f;         // 错误提示色
@warning-color: #faad14;       // 警告色
```

2. 使用 Kebab-Case（小写短横线命名）
- CSS 和 Less 中通常使用 Kebab-Case，以便与原生 CSS 属性命名保持一致。
- 避免使用 CamelCase 或 PascalCase。
示例：
```less
@header-height: 64px;
@border-radius-small: 4px;
@font-size-large: 16px;
```

3. 按类别前缀命名
为不同类型的变量添加前缀，便于快速识别变量的作用。

| 类型            | 前缀示例          | 示例变量                 |
|-----------------|-------------------|--------------------------|
| 颜色相关         | `color`           | `@color-primary`         |
| 尺寸相关         | `size` / `width`  | `@size-avatar`           |
| 字体相关         | `font`            | `@font-size-small`       |
| 边距相关         | `spacing`         | `@spacing-large`         |
| 阴影相关         | `shadow`          | `@shadow-light`          |

示例：
```less
@color-primary: #1890ff;        // 主题颜色
@font-size-small: 12px;         // 小字体大小
@spacing-large: 24px;           // 大间距
@shadow-light: 0 2px 8px rgba(0, 0, 0, 0.15); // 浅阴影
```

4. 层级结构命名
对于复杂的变量，可以采用层级命名法，以反映层级关系。例如：
```less
@btn-primary-bg: #1890ff;       // 按钮主色背景
@btn-primary-hover-bg: #40a9ff; // 按钮主色悬停背景
@btn-primary-text: #ffffff;     // 按钮主色文字
```

*/

// 颜色变量
$color-bg-1: #16171c;
$color-bg-2: #202531;
$color-bg-3: #313c4d;
$color-bg-4: #252a39;
$color-bg-5: #242b38;
$color-bg-6: #789bcd;
$color-bg-3-active: rgba(49, 60, 77, 0.2);

$color-text: #fff;
$color-text-secondary: rgba(255, 255, 255, 0.8);
$color-text-light: rgba(255, 255, 255, 0.6);
$color-placeholder: rgba(255, 255, 255, 0.4);
$color-border: #4c5f78;
$color-border-light: rgba(255, 255, 255, 0.1);
$color-action-tooltip: #eef8fe; // 操作类提示背景色

$color-primary: #4dbfff;
$color-primary-hover: rgba(0, 210, 255, 0.8);
$color-primary-bg-image: linear-gradient(0deg, #16171c 0%, #202531 96%);
$color-success: #90fbc9;
$color-error: #ff3d5b;
$color-warning: #faad14;
$color-warning-bg: rgba(250, 173, 20, 0.1);

// 字体变量
$font-size-small: 12px;
$font-size-medium: 13px;
$font-size-large: 16px;
$font-family-base: 'MiSans-Normal';
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;
$line-height-base: 1.5;

// 边距变量
$spacing-small: 8px;
$spacing-medium: 16px;
$spacing-large: 24px;

// 尺寸变量
$size-avatar-small: 32px;
$size-avatar-large: 64px;

// 圆角变量
$border-radius-small: 4px;
$border-radius-medium: 8px;

// 阴影变量
$shadow-light: 0 2px 8px rgba(0, 0, 0, 0.15);
$shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
$shadow-large: 0px 8px 34px 0px rgba(18, 22, 37, 1);

// 按钮
$btn-primary-bg: linear-gradient(
  90deg,
  #90fbc9 0%,
  #4dbfff 100%
); // 按钮主色背景
$btn-primary-hover-bg: #40a9ff; // 按钮主色悬停背景
$btn-primary-text: #202531; // 按钮主色文字
$btn-default-bg: linear-gradient(
  106.05deg,
  #364f4f 5.49%,
  #29445a 92.89%
); // 默认按钮的背景色
$btn-default-hover-bg: linear-gradient(
  131.32deg,
  #57887e 6.49%,
  #407490 95.57%
);
$btn-default-height: 36px; // 按钮默认高度
$btn-default-radius: 18px; // 按钮默认圆角

// 滚动条
$scrollbar-track-bg: #112734;
$scrollbar-thumb-bg: #1c405f;

:root {
  --color-bg-1: #{$color-bg-1};
  --color-bg-2: #{$color-bg-2};
  --color-bg-3: #{$color-bg-3};
  --color-bg-4: #{$color-bg-4};
  --color-bg-5: #{$color-bg-5};
  --color-bg-6: #{$color-bg-6};
  --color-bg-3-active: #{$color-bg-3-active};

  --color-text: #{$color-text};
  --color-text-secondary: #{$color-text-secondary};
  --color-text-light: #{$color-text-light};
  --color-placeholder: #{$color-placeholder};
  --color-border: #{$color-border};
  --color-border-light: #{$color-border-light};
  --color-action-tooltip: #{$color-action-tooltip};

  --color-primary: #{$color-primary};
  --color-primary-hover: #{$color-primary-hover};
  --color-primary-bg-image: #{$color-primary-bg-image};
  --color-success: #{$color-success};
  --color-error: #{$color-error};
  --color-warning: #{$color-warning};
  --color-warning-bg: #{$color-warning-bg};

  --font-size-small: #{$font-size-small};
  --font-size-medium: #{$font-size-medium};
  --font-size-large: #{$font-size-large};
  --font-family-base: #{$font-family-base};
  --font-weight-normal: #{$font-weight-normal};
  --font-weight-medium: #{$font-weight-medium};
  --font-weight-bold: #{$font-weight-bold};
  --line-height-base: #{$line-height-base};

  --spacing-small: #{$spacing-small};
  --spacing-medium: #{$spacing-medium};
  --spacing-large: #{$spacing-large};

  --size-avatar-small: #{$size-avatar-small};
  --size-avatar-large: #{$size-avatar-large};

  --border-radius-small: #{$border-radius-small};
  --border-radius-medium: #{$border-radius-medium};

  --shadow-light: #{$shadow-light};
  --shadow-medium: #{$shadow-medium};
  --shadow-large: #{$shadow-large};

  --btn-primary-bg: #{$btn-primary-bg};
  --btn-primary-hover-bg: #{$btn-primary-hover-bg};
  --btn-primary-text: #{$btn-primary-text};
  --btn-default-bg: #{$btn-default-bg};
  --btn-default-hover-bg: #{$btn-default-hover-bg};

  --scrollbar-track-bg: #{$scrollbar-track-bg};
  --scrollbar-thumb-bg: #{$scrollbar-thumb-bg};
}
