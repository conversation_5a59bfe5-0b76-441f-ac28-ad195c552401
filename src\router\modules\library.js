/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-14 09:21:12
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-29 09:51:01
 * @FilePath: \platform-face-web\src\router\modules\library.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import lineIcon from '@/assets/svg/menus/line.svg'
import lineActiveIcon from '@/assets/svg/menus/line-active.svg'

export default [
  {
    path: '/library',
    name: 'Library',
    meta: { title: '人脸比对', icon: lineIcon, activeIcon: lineActiveIcon, menu: true, keepAlive: false, layout: 'main' },
    redirect: '/library/upload',
    children: [
      {
        path: 'upload',
        name: 'LibraryFace',
        component: () => import('@/views/library/page/libraryFace/Index.vue'),
        meta: { title: '人脸检测', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'face',
        name: 'LibraryComparison',
        component: () => import('@/views/library/page/libraryComparison/Index.vue'),
        meta: { title: '人脸比对', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'detail',
        name: 'LibrarySearch',
        component: () => import('@/views/library/page/librarySearch/Index.vue'),
        meta: { title: '人脸搜索 ', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'assessment',
        name: 'LibraryAssessment',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '比对研判', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'report',
        name: 'LibraryReport',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '比对报告', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
    ]
  },
];