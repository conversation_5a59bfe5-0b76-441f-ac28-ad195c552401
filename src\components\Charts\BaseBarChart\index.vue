<!--
/**
 * @description 基础柱状图组件
 * <AUTHOR>
 * @version 1.0.0
 * @lastModified 2025/07/25
 * 
 * 提供柱状图的基础功能，包括：
 * - SVG初始化和渲染
 * - 渐变定义
 * - 坐标轴和参考线
 * - 复选框管理
 * - 阈值管理
 * - 基础交互逻辑
 */
-->
<template>
  <div ref="svgDiv" class="bar-chart-container"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { toFixedDecimal } from '@/utils/tool.js';
import { useBarChart } from './composables/useBarChart';
import { useThreshold } from './composables/useThreshold';
import { useCheckbox } from './composables/useCheckbox';

const props = defineProps({
  // 图表的原始数据
  data: {
    type: Array,
    default: () => []
  },
  // 用来判断是不是要清空数据，只是一个标识，与true或false没有关系
  clearSelectData: {
    type: Boolean,
    default: false
  },
  // 设定阈值
  threshold: {
    type: Number,
    default: 0.7
  },
  // 配置选项
  options: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['checkboxChange', 'selectedData', 'overviewClick', 'chartMounted', 'chartInitialized', 'dataChanged']);

const svgDiv = ref(null);
let resizeObserver = null;

// 使用 composables
const barChart = useBarChart(props, emit, props.options);
const threshold = useThreshold(props.threshold, barChart.state);
const checkbox = useCheckbox(emit);

// 图例复选框配置（默认配置，子组件可以覆盖）
const legendCheckbox = ref([
  { id: 'score', label: '人脸相似度', x: -130, y: -40, color: '#90FBCA', isChecked: true },
  { id: 'textScore', label: '文本匹配度', x: -130, y: 25, color: '#4DBDFE', isChecked: true }
]);

/**
 * 初始化图表
 */
const initChart = () => {
  try {
    if (!svgDiv.value || !props.data?.length) return;

    // 确保DOM完全渲染后再获取尺寸
    const width = svgDiv.value.offsetWidth;
    const height = svgDiv.value.offsetHeight;

    // 如果尺寸为0，说明DOM还没有完全渲染，延迟执行
    if (width === 0 || height === 0) {
      console.warn('容器尺寸为0，延迟初始化图表');
      setTimeout(() => {
        initChart();
      }, 50);
      return;
    }

    barChart.state.width = width;
    barChart.state.height = height;
    // 计算最大分数
    const max = props.data.reduce((prev, current) => {
      return (prev.score > current.score) ? prev : current;
    });
    barChart.state.maxScore = max.score;

    // 初始化SVG
    const svgElements = barChart.initSVG(svgDiv.value);

    // 绘制坐标轴和参考线
    barChart.drawAxesAndReferences();

    // 绘制阈值线和矩形
    threshold.updateThresholdLineAndRect(
      props.threshold,
      barChart.state.chartWidth,
      svgElements.g3,
      svgElements.altitudeHeight
    );

    // 调整复选框位置
    threshold.thresholdCheckbox.value.forEach((value) => {
      value.x = barChart.state.chartWidth + 50;
    });

    // 绘制复选框
    checkbox.addCheckbox(legendCheckbox, 'legend-checkbox', svgElements.g4);

    const updatedThresholdCheckbox = threshold.updateThresholdCheckbox(barChart.state.maxScore);
    checkbox.addCheckbox({ value: updatedThresholdCheckbox }, 'threshold-checkbox', svgElements.g4);

    barChart.state.initData = false;

    // 暴露给父组件使用
    return svgElements;
  } catch (error) {
    console.log('初始化柱状图失败', error);
  }
};

/**
 * 清理图表
 */
const cleanupChart = () => {
  const { svg } = barChart.getSVGElements();
  if (svg) {
    svg.remove();
  }
};

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (newData && !barChart.state.initData) {
      // 数据更新时的处理逻辑由子组件实现
      emit('dataChanged', newData);
    } else if (newData && barChart.state.initData) {
      cleanupChart();
      if (svgDiv.value) {
        nextTick(() => {
          initChart();
          emit('chartInitialized', newData);
        });
      }
    }
  },
  { deep: true }
);

// 监听复选框状态变化
watch(
  () => legendCheckbox.value,
  () => {
    // 当复选框状态变化时，通知子组件更新图表
    if (props.data && props.data.length > 0 && !barChart.state.initData) {
      emit('dataChanged', props.data);
    }
  },
  { deep: true }
);

// 监听阈值变化
watch(() => props.threshold, (newThreshold) => {
  if (newThreshold && !barChart.state.initData) {
    const { g3 } = barChart.getSVGElements();
    if (g3) {
      threshold.updateThresholdLineAndRect(
        newThreshold,
        barChart.state.chartWidth,
        g3,
        barChart.getSVGElements().altitudeHeight
      );

      // 更新阈值复选框
      threshold.thresholdCheckbox.value.forEach(item => {
        if (item.label.includes('设定阈值')) {
          item.id = toFixedDecimal(newThreshold, 2);
          item.label = `${toFixedDecimal(newThreshold, 2)} 设定阈值`;
        }
      });
      const { g4 } = barChart.getSVGElements();
      g4.selectAll('.threshold-checkbox').remove();
      const updatedThresholdCheckbox = threshold.updateThresholdCheckbox(barChart.state.maxScore);
      checkbox.addCheckbox({ value: updatedThresholdCheckbox }, 'threshold-checkbox', g4);
    }
  }
}, { immediate: true });

onMounted(() => {
  barChart.state.initData = true;

  // 使用 ResizeObserver 监听容器尺寸变化
  if (svgDiv.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          // 容器有有效尺寸时才初始化图表
          if (barChart.state.initData) {
            const svgElements = initChart();
            emit('chartMounted', svgElements);
          }
        }
      }
    });
    resizeObserver.observe(svgDiv.value);
  } else {
    // 降级方案：使用多层 nextTick 确保 DOM 完全渲染
    nextTick(() => {
      nextTick(() => {
        if (svgDiv.value) {
          const svgElements = initChart();
          emit('chartMounted', svgElements);
        }
      });
    });
  }
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});

// 暴露方法给父组件
defineExpose({
  initChart,
  cleanupChart,
  barChart,
  threshold,
  checkbox,
  legendCheckbox
});
</script>

<style scoped>
.bar-chart-container {
  width: 100%;
  height: 100%;
}
</style>
