/*
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 17:35:13
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 11:11:25
 * @FilePath: \platform-face-web\src\components\Comparison\ManInfoForm\composables\useFormData.js
 * @Description:
 *
 *
 */
import { ref, nextTick } from 'vue';
import { cloneDeep, merge } from 'lodash';

/**
 * 表单初始数据结构
 */
export const INITIAL_FORM_DATA = {
  name: '', // 姓名
  sex: 'unknown', // 性别
  usedNames: [''], // 曾用名(可多条)
  age: 20, // 年龄
  ageRange: [0, 0], // 年龄段
  aliasNames: [''], // 别名(可多条)
  nativePlace: '', // 籍贯
  identifications: [
    // 证件信息(可多条)
    {
      businessId: '',
      businessValue: '',
      dictionaryId: null,
      dictionaryItemId: null,
    },
  ],
  addresses: [
    // 居住地
    {
      businessId: '',
      businessValue: '',
      dictionaryId: null,
      dictionaryItemId: null,
    },
  ],
  humanRace: 'yellow', // 肤色
  tagIds: [], // 关键标签
  relatedCases: [
    {
      caseDescription: '',
      caseEndTime: '',
      caseName: '',
      caseStartTime: '',
      involvedCasePeoples: [],
      searchInvolvesPersons: '', // 搜索的关系人名称
      times: [],
    },
  ],
  description: '', // 备注
};

/**
 * 表单数据管理 hook
 * @param {Object} props - 组件 props
 * @returns {Object} 表单数据和操作方法
 */
export function useFormData(props) {
  // 表单数据
  const formData = ref(cloneDeep(INITIAL_FORM_DATA));

  /**
   * 设置表单数据
   * @param {Object} data - 新的表单数据
   */
  const setFormData = async (data) => {
    await nextTick();
    formData.value = cloneDeep(INITIAL_FORM_DATA); // 重置为默认值

    if (!data) {
      return;
    }

    const dataCopy = cloneDeep(data);

    // 处理空数组的情况
    if (!data?.addresses?.length) {
      delete dataCopy.addresses;
    }
    if (!data?.identifications?.length) {
      delete dataCopy.identifications;
    }
    if (!data?.relatedCases?.length) {
      delete dataCopy.relatedCases;
    }

    formData.value = merge({}, formData.value, dataCopy);
  };

  /**
   * 重置表单数据
   */
  const resetFormData = () => {
    formData.value = cloneDeep(INITIAL_FORM_DATA);
  };

  /**
   * 处理表单提交数据
   * @param {Object} infoDictionaries - 字典数据
   * @param {string} involvedFaceId - 关联的人脸ID
   * @returns {Object} 处理后的表单数据
   */
  const processSubmitData = (infoDictionaries, involvedFaceId) => {
    const formDataCopy = cloneDeep(formData.value);

    // 处理地址信息
    formDataCopy.addresses = formDataCopy.addresses.reduce((result, address) => {
      if (address.businessValue) {
        address.businessId = involvedFaceId;
        address.dictionaryId = infoDictionaries['RESIDENTIAL_ADDRESS'][0].dictionaryId;
        result.push(address);
      }
      return result;
    }, []);

    // 处理证件信息
    formDataCopy.identifications = formDataCopy.identifications.reduce((result, idCard) => {
      if (idCard.businessValue) {
        idCard.businessId = involvedFaceId;
        idCard.dictionaryId = infoDictionaries['ID_CARD_NO'][0].dictionaryId;
        result.push(idCard);
      }
      return result;
    }, []);

    // 处理关联事件
    formDataCopy.relatedCases = formDataCopy.relatedCases.reduce((result, relatedCase) => {
      if (relatedCase.caseDescription || relatedCase.times.length > 0 || relatedCase.caseName || relatedCase.involvedCasePeoples.length > 0) {
        relatedCase.caseStartTime = relatedCase?.times?.[0];
        relatedCase.caseEndTime = relatedCase?.times?.[1];
        result.push(relatedCase);
      }
      return result;
    }, []);

    // 处理标签ID
    formDataCopy.tagIds = formData.value.tagIds.map((tag) => {
      return tag?.id;
    });

    return formDataCopy;
  };

  return {
    formData,
    setFormData,
    resetFormData,
    processSubmitData,
  };
}
