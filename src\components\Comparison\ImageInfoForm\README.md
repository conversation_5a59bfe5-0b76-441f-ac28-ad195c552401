# ImageInfoForm 图像信息表单组件

## 📖 组件概述

ImageInfoForm 是一个用于编辑图像详细信息的表单组件，支持图像获取信息、来源信息、类型信息等多个维度的数据录入和管理。

## ✨ 主要功能

- 📅 **获取时间管理**：支持日期选择器设置图像获取时间
- 🔄 **获取方式管理**：通过字典数据选择图像获取方式
- 📷 **照片来源管理**：设置照片的具体来源信息
- 🛣️ **获取途径管理**：记录图像的获取渠道
- 🏷️ **照片类型管理**：分类管理不同类型的照片
- 🏢 **图片归属管理**：设置图像的归属信息
- 📝 **备注信息**：支持添加额外的描述信息
- 💾 **数据持久化**：支持表单数据的保存和回显

## 🏗️ 组件架构

```
src/components/Comparison/ImageInfoForm/
├── index.vue                    # 主组件
└── README.md                    # 组件文档
```

## 📋 Props 属性

| 属性名      | 类型   | 默认值 | 说明           |
| ----------- | ------ | ------ | -------------- |
| initialData | Object | {}     | 初始表单数据   |
| materialId  | String | ''     | 关联的素材ID   |

## 🎪 Events 事件

| 事件名         | 参数     | 说明               |
| -------------- | -------- | ------------------ |
| submit-success | formData | 表单提交成功时触发 |

## 🚀 使用示例

### 基础用法

```vue
<template>
  <ImageInfoForm 
    :initial-data="imageData" 
    :material-id="materialId" 
    @submit-success="handleSubmit" 
  />
</template>

<script setup>
const materialId = ref('material-123');
const imageData = ref({
  captureTime: '2025-07-24',
  obtainMethod: {
    dictionaryItemId: 'method-001'
  },
  source: {
    dictionaryItemId: 'source-001'
  },
  description: '图像描述信息'
});

const handleSubmit = (data) => {
  console.log('提交的表单数据:', data);
  // 处理提交逻辑
};
</script>
```

## 📊 表单数据结构

```javascript
const formData = {
  captureTime: '',        // 获取时间 (YYYY-MM-DD格式)
  obtainMethod: {         // 获取方式
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },
  source: {              // 照片来源
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },
  obtainWay: {           // 获取途径
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },
  type: {                // 照片类型
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },
  obtainScene: {         // 图片归属
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },
  description: ''        // 备注
};
```

## 🔧 字典数据依赖

组件依赖以下字典数据：

| 字典类型             | 用途     | 对应字段     |
| -------------------- | -------- | ------------ |
| ACQUISITION_METHOD   | 获取方式 | obtainMethod |
| PHOTO_SOURCE         | 照片来源 | source       |
| ACQUISITION_CHANNEL  | 获取途径 | obtainWay    |
| PHOTO_TYPE           | 照片类型 | type         |
| IMAGE_OWNERSHIP      | 图片归属 | obtainScene  |

## 🎨 样式特性

- **深色主题**：采用深色背景设计 (#1f242b)
- **模块化布局**：表单区域和图像元数据区域分离
- **响应式设计**：适配不同屏幕尺寸
- **统一边框**：使用统一的边框样式 (#3C4554)

## ⚠️ 注意事项

1. **字典数据**：组件依赖字典接口，确保字典数据正确加载
2. **数据格式**：确保传入的 initialData 格式正确
3. **素材ID**：materialId 用于关联业务数据，提交时会自动设置到各字典项的 businessId
4. **图像元数据**：右侧图像元数据区域目前为TODO状态，待后续开发

## 🔄 数据流转

1. **初始化**：组件挂载时加载字典数据
2. **数据回显**：监听 initialData 变化，自动回显表单数据
3. **数据验证**：提交时验证字典项数据完整性
4. **数据提交**：处理后的数据通过 submit-success 事件传递给父组件

## 🚀 开发指南

### 添加新字段

1. 在表单模板中添加新的 `a-form-item`
2. 在 `formData` 中定义新字段的初始值
3. 在 `setFormData` 函数中处理新字段的回显
4. 在 `handleSubmit` 中处理新字段的提交逻辑

### 扩展字典类型

1. 在 `getDictionary` 调用中添加新的字典类型
2. 在模板中添加对应的选择器
3. 在 `verifyObj` 函数中添加新字典的验证逻辑
