/**
 * 节点渲染器 - 统一的节点渲染入口
 * 协调形状渲染器和装饰渲染器，提供完整的节点渲染功能
 */

import { ShapeRendererFactory } from './ShapeRenderer.js';
import { DecorationRendererFactory } from './DecorationRenderer.js';

/**
 * 中心节点渲染器
 */
export class CenterNodeRenderer {
    constructor(configManager) {
        this.configManager = configManager;
        this.shapeFactory = new ShapeRendererFactory(configManager);
        this.decorationFactory = new DecorationRendererFactory(configManager);
    }

    /**
     * 渲染中心节点
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     */
    render(container, node) {
        const centerRadius = this.configManager.get('nodes.center.size.radius');
        const circlesConfig = this.configManager.get('nodes.center.circles');

        // 渲染基础形状（如果启用）
        let shape = null;
        if (circlesConfig.enabled) {
            shape = this.shapeFactory.renderShape(
                container,
                node,
                centerRadius,
                'center',
                true // 包含图片
            );
        } else {
            // 只渲染图片，不渲染边框圆圈
            shape = this.shapeFactory.renderShape(
                container,
                node,
                centerRadius,
                'center',
                true // 包含图片
            );
            // 移除边框
            if (shape) {
                shape.attr('stroke', 'none').attr('stroke-width', 0);
            }
        }

        // 渲染外圈装饰环（如果启用）
        let outerRing = null;
        if (circlesConfig.outerRing && circlesConfig.outerRing.enabled) {
            const outerRingStyle = circlesConfig.outerRing.style;
            outerRing = container
                .append('circle')
                .attr('r', circlesConfig.outerRing.radius)
                .attr('fill', outerRingStyle.fill || 'none')
                .attr('stroke', outerRingStyle.stroke || '#4DBFFF')
                .attr('stroke-width', outerRingStyle.strokeWidth || 2)
                .attr('stroke-dasharray', outerRingStyle.strokeDasharray || 'none')
                .attr('class', 'center-outer-ring');
        }

        // 中心节点通常不需要其他装饰元素
        const decorations = this.decorationFactory.renderDecorations(container, node, 'center');

        return {
            shape,
            outerRing,
            decorations,
            radius: centerRadius
        };
    }
}

/**
 * 其他节点渲染器
 */
export class OtherNodeRenderer {
    constructor(configManager) {
        this.configManager = configManager;
        this.shapeFactory = new ShapeRendererFactory(configManager);
        this.decorationFactory = new DecorationRendererFactory(configManager);
    }

    /**
     * 渲染其他节点
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     * @param {number} gap - 间隙（用于缩放）
     * @param {number} scale - 缩放比例
     */
    render(container, node, gap = 0, scale = 1) {
        if (!node.scoreData || !Array.isArray(node.scoreData)) {
            return this.renderSimpleNode(container, node, gap, scale);
        }

        const centerRadius = this.configManager.get('nodes.center.size.radius');
        const scaleConfig = this.configManager.get('nodes.other.scoreScale');

        // 确定缩放比例
        const finalScale = node.scoreRank > scaleConfig.threshold ? scaleConfig.smallScale : scaleConfig.normalScale;
        const appliedScale = finalScale * scale;

        // 渲染装饰元素（算法圆环）
        const decorations = this.decorationFactory.renderDecorations(container, node, 'other');

        // 计算最小分数对应的半径（用于图片）
        const sortedScoreData = [...node.scoreData].sort((a, b) => b.score - a.score);
        const minScore = sortedScoreData[sortedScoreData.length - 1].score;
        let imageRadius = minScore * centerRadius * appliedScale;

        // 渲染节点图片
        const shape = this.shapeFactory.renderShape(
            container,
            node,
            imageRadius,
            'other',
            true // 包含图片
        );

        return {
            shape,
            decorations,
            radius: imageRadius,
            algorithmRings: decorations.algorithmRings
        };
    }

    /**
     * 渲染简单节点（没有scoreData的情况）
     * @param {d3.Selection} container - D3容器
     * @param {Object} node - 节点数据
     * @param {number} gap - 间隙
     * @param {number} scale - 缩放比例
     */
    renderSimpleNode(container, node, gap, scale) {
        const centerRadius = this.configManager.get('nodes.center.size.radius');
        const defaultRadius = (centerRadius * 0.3) * scale; // 默认半径

        // 渲染基础形状
        const shape = this.shapeFactory.renderShape(
            container,
            node,
            defaultRadius,
            'other',
            true
        );

        // 简单装饰
        const decorations = {
            nodeLabel: this.decorationFactory.getRenderer('nodeLabel').render(container, node)
        };

        return {
            shape,
            decorations,
            radius: defaultRadius
        };
    }
}

/**
 * 节点渲染器工厂 - 主要入口点
 */
export class NodeRendererFactory {
    constructor(configManager) {
        this.configManager = configManager;
        this.centerRenderer = new CenterNodeRenderer(configManager);
        this.otherRenderer = new OtherNodeRenderer(configManager);
    }

    /**
     * 渲染中心节点
     * @param {d3.Selection} container - D3容器
     * @param {Object} node - 节点数据
     */
    renderCenterNode(container, node) {
        return this.centerRenderer.render(container, node);
    }

    /**
     * 渲染其他节点
     * @param {d3.Selection} container - D3容器
     * @param {Object} node - 节点数据
     * @param {number} gap - 间隙
     * @param {number} scale - 缩放比例
     */
    renderOtherNode(container, node, gap = 0, scale = 1) {
        return this.otherRenderer.render(container, node, gap, scale);
    }

    /**
     * 根据节点类型自动选择渲染器
     * @param {d3.Selection} container - D3容器
     * @param {Object} node - 节点数据
     * @param {string} nodeType - 节点类型 ('center' | 'other')
     * @param {Object} options - 渲染选项
     */
    renderNode(container, node, nodeType = 'other', options = {}) {
        const { gap = 0, scale = 1 } = options;

        if (nodeType === 'center') {
            return this.renderCenterNode(container, node);
        } else {
            return this.renderOtherNode(container, node, gap, scale);
        }
    }

    /**
     * 批量渲染节点
     * @param {Array} nodeContainers - 节点容器数组 [{container, node, type, options}]
     */
    renderNodes(nodeContainers) {
        const results = [];

        nodeContainers.forEach(({ container, node, type = 'other', options = {} }) => {
            const result = this.renderNode(container, node, type, options);
            results.push({
                node,
                type,
                result
            });
        });

        return results;
    }

    /**
     * 更新配置管理器（用于动态配置更新）
     * @param {ConfigManager} newConfigManager - 新的配置管理器
     */
    updateConfigManager(newConfigManager) {
        this.configManager = newConfigManager;
        this.centerRenderer = new CenterNodeRenderer(newConfigManager);
        this.otherRenderer = new OtherNodeRenderer(newConfigManager);
    }
}
