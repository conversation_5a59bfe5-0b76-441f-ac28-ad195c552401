# GroupedImageDisplay 分组图片展示组件

## 组件描述

GroupedImageDisplay 是一个智能的分组图片展示组件，专门用于展示人脸识别相关的图片分组。该组件根据图片数量自动选择最佳的布局方式，支持拖拽操作、图片点击、以及"更多"模态框展示等功能。

## 功能特性

- 🎯 **智能布局**：根据图片数量自动选择最佳展示布局
- 🖼️ **多种布局模式**：支持单张、1x2、2x2、嵌套网格等多种布局
- 🎨 **人脸智能居中**：集成AvatarImage组件，支持人脸坐标智能居中显示
- 🖱️ **拖拽支持**：完整的拖拽事件处理，支持图片拖拽操作
- 👆 **点击交互**：支持图片点击事件，可配置是否启用点击功能
- 📱 **缩略图装饰**：集成ThumbnailDecorator组件，显示原图缩略图
- ➕ **更多展示**：超过7张图片时显示"更多"覆盖层
- 🎪 **选中状态**：支持图片选中状态显示，自定义选中边框颜色

## 布局模式

| 图片数量 | 布局类型      | 描述                      |
| -------- | ------------- | ------------------------- |
| 1张      | `single`      | 单张大图展示              |
| 2张      | `1x2`         | 1行2列网格布局            |
| 3-4张    | `2x2`         | 2行2列网格布局            |
| 5-7张    | `nested`      | 嵌套网格布局（3+4子网格） |
| >7张     | `nested-more` | 嵌套网格+更多覆盖层       |

## Props 参数

| 参数名              | 类型               | 默认值      | 必填 | 说明                         |
| ------------------- | ------------------ | ----------- | ---- | ---------------------------- |
| `groupedImages`     | `Array`            | -           | ✅   | 分组图片数据数组             |
| `groupId`           | `Number \| String` | -           | ✅   | 分组唯一标识ID               |
| `currentImage`      | `Object`           | -           | ❌   | 当前图片对象（无分组时使用） |
| `selectedItems`     | `Array`            | `[]`        | ❌   | 已选中的图片ID数组           |
| `ringColor`         | `String`           | `'#4c5f78'` | ❌   | 默认边框颜色                 |
| `selectedRingColor` | `String`           | `'#4DBFFF'` | ❌   | 选中状态边框颜色             |
| `ringThickness`     | `Number`           | `0`         | ❌   | 边框厚度                     |
| `imageClickable`    | `Boolean`          | `false`     | ❌   | 是否启用图片点击功能         |

### 图片数据结构

```javascript
// groupedImages 数组中每个图片对象的结构
{
  involvedFaceId: "face-id-123",           // 人脸ID
  location: "image-location-path",         // 图片位置
  multiFaceData: {
    sourceImageId: "source-image-id",      // 源图片ID
    items: [{
      coordinate: [x, y, width, height]    // 人脸坐标 [x, y, 宽度, 高度]
    }]
  }
}
```

## Events 事件

| 事件名       | 参数               | 说明                                   |
| ------------ | ------------------ | -------------------------------------- |
| `dragstart`  | `(event, payload)` | 拖拽开始事件，payload包含item和groupId |
| `dragover`   | `(event)`          | 拖拽悬停事件                           |
| `dragenter`  | `(event)`          | 拖拽进入事件                           |
| `dragleave`  | `(event)`          | 拖拽离开事件                           |
| `drop`       | `(event, groupId)` | 拖拽放置事件                           |
| `clickImage` | `(face)`           | 图片点击事件（需启用imageClickable）   |
| `openMore`   | `(groupId)`        | 点击"更多"按钮事件                     |

## 使用示例

### 基础用法

```vue
<template>
  <GroupedImageDisplay
    :grouped-images="imageList"
    :group-id="groupId"
    :selected-items="selectedFaceIds"
    :image-clickable="true"
    ring-color="#4c5f78"
    selected-ring-color="#4DBFFF"
    :ring-thickness="2"
    @click-image="handleImageClick"
    @dragstart="handleDragStart"
    @drop="handleDrop"
    @open-more="handleOpenMore" />
</template>

<script setup>
import GroupedImageDisplay from '@/components/Business/GroupedImageDisplay/index.vue';

const imageList = [
  {
    involvedFaceId: 'face-001',
    location: 'image-path-1',
    multiFaceData: {
      sourceImageId: 'source-001',
      items: [
        {
          coordinate: [100, 50, 200, 250],
        },
      ],
    },
  },
  // ... 更多图片
];

const groupId = 'group-001';
const selectedFaceIds = ['face-001'];

const handleImageClick = (face) => {
  console.log('图片被点击:', face);
};

const handleDragStart = (event, payload) => {
  console.log('开始拖拽:', payload);
};

const handleDrop = (event, groupId) => {
  console.log('拖拽放置到分组:', groupId);
};

const handleOpenMore = (groupId) => {
  console.log('打开更多图片:', groupId);
};
</script>
```

### 单张图片展示

```vue
<template>
  <GroupedImageDisplay :grouped-images="[]" :group-id="groupId" :current-image="singleImage" ring-color="#e0e0e0" :ring-thickness="1" />
</template>

<script setup>
const singleImage = {
  involvedFaceId: 'face-single',
  location: 'single-image-path',
  multiFaceData: {
    sourceImageId: 'source-single',
    items: [
      {
        coordinate: [80, 60, 180, 220],
      },
    ],
  },
};
</script>
```

### 拖拽功能集成

```vue
<template>
  <div class="image-gallery">
    <GroupedImageDisplay
      v-for="group in imageGroups"
      :key="group.id"
      :grouped-images="group.images"
      :group-id="group.id"
      :selected-items="selectedItems"
      @dragstart="handleDragStart"
      @drop="handleDrop"
      @dragover="handleDragOver"
      class="group-item" />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const selectedItems = ref([]);
const draggedItem = ref(null);

const handleDragStart = (event, payload) => {
  draggedItem.value = payload;
  event.dataTransfer.effectAllowed = 'move';
};

const handleDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = 'move';
};

const handleDrop = (event, targetGroupId) => {
  if (draggedItem.value && draggedItem.value.groupId !== targetGroupId) {
    // 处理图片在分组间的移动
    moveImageBetweenGroups(draggedItem.value, targetGroupId);
  }
  draggedItem.value = null;
};
</script>
```

## 样式定制

组件使用SCSS编写，支持以下样式定制：

```scss
.grouped-image-container {
  // 自定义容器样式
  border-radius: 12px;
  overflow: hidden;

  // 1x2布局样式定制
  .group-1x2 {
    gap: 8px; // 自定义间距
  }

  // 2x2布局样式定制
  .group-2x2 {
    gap: 6px;
  }

  // 更多覆盖层样式定制
  .more-overlay {
    background: rgba(0, 0, 0, 0.8);
    font-size: 28px;
    font-weight: bold;

    &:hover {
      background: rgba(0, 0, 0, 0.9);
    }
  }
}
```

## 组件架构

### 目录结构

```
GroupedImageDisplay/
├── index.vue                    # 主入口组件（约80行）
├── components/
│   ├── ImageItem.vue           # 单个图片项组件（约120行）
│   ├── SingleLayout.vue        # 单张图片布局（约70行）
│   ├── GridLayout.vue          # 网格布局（1x2, 2x2）（约100行）
│   ├── NestedLayout.vue        # 嵌套布局（约120行）
│   └── NestedMoreLayout.vue    # 嵌套+更多布局（约150行）
├── composables/
│   ├── useImageEvents.js       # 图片事件处理逻辑（约80行）
│   └── useLayoutLogic.js       # 布局逻辑计算（约70行）
└── README.md
```

### 组件职责

- **index.vue**: 主入口组件，负责根据布局类型渲染对应的子组件
- **ImageItem.vue**: 基础图片项组件，封装ThumbnailDecorator + AvatarImage的组合
- **SingleLayout.vue**: 处理单张图片的展示布局
- **GridLayout.vue**: 处理1x2和2x2的网格布局
- **NestedLayout.vue**: 处理5-7张图片的嵌套网格布局
- **NestedMoreLayout.vue**: 处理超过7张图片的布局，包含"更多"覆盖层
- **useImageEvents.js**: 统一处理拖拽、点击等事件逻辑
- **useLayoutLogic.js**: 计算布局类型、图片分组等逻辑

## 依赖组件

- **AvatarImage**: 智能头像组件，支持人脸居中显示
- **ThumbnailDecorator**: 缩略图装饰器组件

## 注意事项

1. **图片数据完整性**：确保传入的图片数据包含完整的multiFaceData信息
2. **人脸坐标格式**：coordinate数组格式为[x, y, width, height]
3. **拖拽事件处理**：需要在父组件中正确处理拖拽事件的preventDefault
4. **性能优化**：大量图片时建议使用虚拟滚动或分页加载
5. **图片加载**：依赖DEFAULT_CONFIG中的getImageWebURL函数获取图片URL

## 拆分优势

### 🎯 架构优势

- **单一职责**: 每个组件只负责一种布局或功能，职责清晰
- **代码复用**: ImageItem组件消除了大量重复代码
- **易于维护**: 修改某种布局不会影响其他布局
- **便于测试**: 每个组件都可以独立测试
- **性能优化**: 可以对不同布局进行针对性优化
- **扩展性好**: 新增布局类型只需添加新组件

### 📊 代码质量提升

- **文件大小**: 主组件从422行减少到约80行
- **复杂度降低**: 每个子组件都控制在150行以内
- **逻辑分离**: 事件处理和布局计算逻辑独立
- **类型安全**: 更好的TypeScript支持和类型推断

## 更新日志

### v2.0.0 (2025-07-22)

- 🔥 **重大重构**: 按布局类型拆分组件架构
- ✨ **新增组件**: ImageItem、SingleLayout、GridLayout、NestedLayout、NestedMoreLayout
- 🛠️ **新增Composables**: useImageEvents、useLayoutLogic
- 📦 **代码优化**: 消除重复代码，提升可维护性
- 🎯 **性能提升**: 组件按需加载，减少渲染开销

### v1.0.0 (2025-07-22)

- 初始版本发布
- 支持多种智能布局模式
- 集成拖拽和点击功能
- 支持选中状态显示
- 添加"更多"模态框支持
