<template>
  <LoadMoreList :data="data" layout-type="horizontal" gap-size="24px" :has-more="false" :loading="false"
    :key-name="'portraitId'" style="scrollbar-gutter: stable;" :selected-id="selectedId" :show-bottom-title="false"
    @update:selected-id="handleSelect">
    <template #default="{ item, isSelected }">
      <div :ref="el => setMatchingItemRef(el, item.portraitId)" class="d-flex flex-column pt-4">
        <div :class="{ 'active-div': isSelected }">
          <AvatarImage :src="getImageWebURL(item.portrait.standardPortraitImages[0].imageId)" :gap="3"
            :ring-thickness="1" :ring-color="isSelected ? '#4975AC' : '#3E4D61'" border-radius="8px" width="404px"
            height="264px" :face-x="item.portrait.standardPortraitImages[0]?.coordinate[0]"
            :face-y="item.portrait.standardPortraitImages[0]?.coordinate[1]"
            :face-width="item.portrait.standardPortraitImages[0]?.coordinate[2]"
            :face-height="item.portrait.standardPortraitImages[0]?.coordinate[3]">
            <template #statusTopLeft>
              <div class="d-flex align-items-center justify-content-center">
                <span>TOP {{ item.scoreRank }}</span>
                <Divider direction="vertical" height="11px" color="rgba(255, 255, 255, 0.8)" />
                <span>{{ `${toFixedDecimal(item.score, 2)} ${algorithmShortNames[algorithmCode] || algorithmCode}`
                  }}</span>
              </div>
            </template>
            <template #statusTopRight>
              <div class="d-flex align-items-center justify-content-center g-2"
                style="background: #16171c;padding: 0 4px">
                <span style="font-size: 16px;">{{ item?.infoMatching?.score ? toFixedDecimal(item?.infoMatching?.score,
                  2) : null }}</span>
                <CustomButtonWithTooltip v-if="item?.infoMatching?.score" icon-name="text-matching-results"
                  icon-size="14px" :button-bg="'none'" :hover-bg="'none'" tooltip="文本信息匹配度"
                  @click="openModal(item?.infoMatching)" />
              </div>
            </template>
          </AvatarImage>
        </div>

        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center g-2" style="height: 44px;">
            <span>{{ item.portrait.xm }}</span>
            <Divider direction="vertical" color="#40516A" height="10px" />
            <CustomButtonWithTooltip tooltip="人像库详情" icon-name="portrait" icon-size="16px" button-bg="#16171C"
              hover-bg="#2A2D34"></CustomButtonWithTooltip>
            <CustomButtonWithTooltip tooltip="更多11比分" icon-name="image-to-library-zoom-high" icon-size="18px"
              button-bg="#16171C" hover-bg="#2A2D34" @click="handleClick('moreScore', item)"></CustomButtonWithTooltip>
          </div>
          <div v-if="showOtherScore" class="d-flex align-items-center g-2">
            <!-- 详情页没有，但是研判 -->
            <div v-if="item.attachedAlgorithmCodes && item.attachedAlgorithmCodes.length">
              <div v-for="(attached, index) in item.attachedAlgorithmCodes" :key="index">
                <AlgorithmScoreItem :score="attached.score" :code="attached.algorithmCode"
                  :name="algorithmNames[attached.algorithmCode] || attached.algorithmCode" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </LoadMoreList>
</template>
<script setup>
import { ref } from 'vue';
import { DEFAULT_CONFIG } from '@/constants';
import { algorithmNames, algorithmShortNames } from '@/api/algorithm.js';
import { toFixedDecimal } from '@/utils/tool';

import LoadMoreList from '@/components/Common/LoadMoreList/index.vue';
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';
import Divider from '@/components/Common/Divider/index.vue';
import AvatarImage from '@/components/Common/AvatarImage/index.vue';
import AlgorithmScoreItem from '../AlgorithmScoreItem/index.vue';

const { getImageWebURL } = DEFAULT_CONFIG || {};

defineProps({
  data: {
    type: Array,
    required: true,
  },
  // 是否显示其他算法的分数
  showOtherScore: {
    type: Boolean,
    default: true
  },
  algorithmCode: {
    type: String
  }
});

const emits = defineEmits(['matchingBtnClick'])

const selectedId = ref(null)
const handleSelect = (item) => {
  selectedId.value = item?.portrait?.portraitId
  // console.log('portraitId =', portraitId);
};

const matchingItemRef = ref([])

const setMatchingItemRef = (el, id) => {
  matchingItemRef.value[id] = el
};

const scrollToItem = (id) => {
  const itemEl = matchingItemRef.value[id]
  if (itemEl) {
    selectedId.value = id
    itemEl.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'start'
    })
  }
}

defineExpose({
  scrollToItem
})

const handleClick = (type, item) => {
  emits('matchingBtnClick', type, item)
}

const openModal = (textMatchingData) => {
  rows.value = textMatchingData.items.map(item => {
    const newItem = {
      fieldName: textMatchDic[item?.type],
      recordValue: item.inputInfo,
      referenceValue: item.portraitInfo,
      probability: `${toFixedDecimal((item.matchProbability * 100), 2)}%`
    }
    return newItem
  })
  // TODO:文本匹配的弹窗
  // modelVisible.value = true;
}
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  width: 100%;
  gap: 16px;
  overflow: scroll;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 32px 0 0;
}

.active-div {
  box-shadow: 0 3px 6px #4975AC;
  border-radius: 8px;
}
</style>