/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-09 11:52:01
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-22 16:13:52
 * @FilePath: \vue3-js-template-master\src\router\modules\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 自动导入所有模块路由
const modules = import.meta.glob('./*.js', { eager: true });

// 合并所有路由
const modulesRoutes = Object.values(modules)
  .filter(module => module.default && Array.isArray(module.default))
  .flatMap(module => module.default);

export default modulesRoutes; 