import * as d3 from 'd3';
import ConfigManager from './ConfigManager';
import { createRendererSuite } from './renderers/index.js';

let maxVisibleScore; //圈的最大的分数
let clickNodeId; //用来记录选中的节点，如果再次点击该节点，取消高亮加粗

// 渲染单中心图
export function renderSingleCenterGraph({ svg, nodes, linkKeyArr, nodeLinkMap, configManager: passedConfigManager }) {
    if (!svg || !nodes) return;

    // 使用传递的配置管理器，如果没有则创建一个
    const configManager = passedConfigManager;

    // 创建渲染器套件
    const renderers = createRendererSuite(configManager);

    const mainGroup = d3.select(svg).select('.main-group');
    mainGroup.selectAll('*').remove(); // 清除旧元素
    d3.selectAll('.relation-tooltip').remove();

    // 绘制背景的两个参考圆
    const referenceCircleGroup = mainGroup.append('g').attr('class', 'reference-circle');

    // 绘制连线（在其他元素之前绘制，确保在底层）
    const linkGroup = mainGroup.append('g').attr('class', 'links');

    // 分离中心节点和其他节点
    const centerNode = nodes.find((node) => node.isCenter);
    const otherNodes = nodes.filter((node) => !node.isCenter);

    const nodeMap = new Map();
    nodes.forEach((node) => {
        if (node.isCenter) {
            nodeMap.set(node.photoId, node);
        } else {
            nodeMap.set(node.portrait.portraitId, node);
        }
    });

    // 绘制中心节点（使用新的渲染器）
    if (centerNode) {
        const centerGroup = mainGroup.append('g').attr('class', 'center-node').attr('transform', `translate(${centerNode.x},${centerNode.y})`);

        // 使用新的节点渲染器
        renderers.nodeRenderer.renderCenterNode(centerGroup, centerNode);
    }

    // 使用D3数据绑定模式绘制其他节点
    const nodeGroups = mainGroup.selectAll('.node-group').data(otherNodes, (d) => d.portrait.portraitId);
    nodeGroups.exit().remove();

    const newNodes = nodeGroups.enter().append('g').attr('class', 'node-group');

    const allNodes = nodeGroups.merge(newNodes).attr('transform', (d) => `translate(${d.x},${d.y})`);

    // 初始化tooltip渲染器
    const tooltipRenderer = renderers.tooltipRenderer.createRenderer();
    const tooltipInfo = tooltipRenderer.initialize();

    // 先计算maxVisibleScore - 这个值用于确定哪些连线应该可见
    if (otherNodes.length > 10 && otherNodes[10].scoreData) {
        const ciAnScore = otherNodes[10].scoreData.find((item) => item.algorithmCode === 'ci_an');
        maxVisibleScore = ciAnScore ? ciAnScore.score : 0;
    } else if (otherNodes.length > 0 && otherNodes[0].scoreData) {
        // 如果没有第11个节点，使用第一个节点的最高分数
        const firstNodeScores = otherNodes[0].scoreData;
        maxVisibleScore = Math.max(...firstNodeScores.map((item) => item.score));
    } else {
        // 如果没有节点或节点没有分数，设置一个默认值
        maxVisibleScore = 0.5; // 默认值，确保连线可见
    }

    // 先创建连线渲染器和连线
    const linkRenderer = renderers.linkRenderer.createSingleCenterRenderer();
    const linkLines = linkRenderer.renderLinks(linkGroup, linkKeyArr, nodeMap, maxVisibleScore);

    // 添加节点不同算法的圆形、图片以及排名（使用新的渲染器）
    allNodes.each(function (node) {
        const nodeGroup = d3.select(this);

        // 使用新的节点渲染器
        renderers.nodeRenderer.renderOtherNode(nodeGroup, node, 0, 1);

        // 添加tooltip事件
        if (tooltipRenderer.isEnabled()) {
            tooltipRenderer.addTooltipEvents(nodeGroup, node);
        }

        // 添加点击高亮事件 - 现在linkRenderer和linkLines已经创建好了
        nodeGroup.on('click', function () {
            clickNodeId = linkRenderer.highlightLinkedLines(node.portrait.portraitId, linkLines, nodeLinkMap, maxVisibleScore, clickNodeId);
        });
    });

    // 绘制背景（使用新的背景渲染器）
    // 设置背景组的位置以中心节点为中心
    if (centerNode) {
        referenceCircleGroup.attr('transform', `translate(${centerNode.x},${centerNode.y})`);
    }

    const backgroundContext = {
        centerNode,
        nodes: otherNodes,
        width: 800, // 可以从配置或参数获取
        height: 600,
        zones: configManager.get('background.zoneMarkers.zones') || [],
    };

    renderers.backgroundRenderer.renderBackground(referenceCircleGroup, backgroundContext);
}

/**
 * 渲染多中心图形
 */
export function renderMultiCenterGraph({ svg, nodes, centers, nodeGroups, linkGroups, configManager }) {
    if (!svg || !nodes) return;

    // 如果没有传递configManager，创建一个默认的
    const finalConfigManager = configManager || ConfigManager.fromLegacyConfig({});

    const mainGroup = d3.select(svg).select('.main-group');
    mainGroup.selectAll('*').remove(); // 清除旧元素
    // 绘制背景的两个参考圆
    const referenceCircleGroups = mainGroup.append('g').attr('class', 'reference-circle');

    // 1. 渲染所有连线（按分类设置不同样式）
    const linkRenderer = finalConfigManager
        ? createRendererSuite(finalConfigManager).linkRenderer.createMultiCenterRenderer()
        : new (require('./renderers/LinkRenderer.js').MultiCenterLinkRenderer)(finalConfigManager);
    linkRenderer.renderLinks(mainGroup, linkGroups, nodes);

    // 2. 渲染所有节点
    renderAllNodes(mainGroup, nodes, finalConfigManager);

    // 3.绘制背景圆--TODO
    Object.keys(centers).forEach((key) => {
        const referenceCircleGroup = referenceCircleGroups.append('g').attr('class', `reference-circle-${key}`);
        // const value = obj[key];
        // renderBackgroundCircle(referenceCircleGroup, centers[key], nodeGroups[key][2], nodeGroups[key][10], finalConfigManager);
    });
}

function renderAllNodes(group, nodes, configManager) {
    const centerNodes = nodes.filter((n) => n.isCenter);
    const otherNodes = nodes.filter((n) => !n.isCenter);

    // 创建渲染器套件
    const renderers = createRendererSuite(configManager);

    // 渲染中心节点
    centerNodes.forEach((center) => {
        const centerGroup = group.append('g').attr('class', 'center-nodes').attr('transform', `translate(${center.x},${center.y})`);
        renderers.nodeRenderer.renderCenterNode(centerGroup, center);
    });

    // 使用D3数据绑定模式绘制其他节点
    const nodeGroups = group.selectAll('.other-nodes').data(otherNodes, (d) => d.portrait.portraitId);
    nodeGroups.exit().remove();

    const newNodes = nodeGroups.enter().append('g').attr('class', 'node-group');

    const allNodes = nodeGroups.merge(newNodes).attr('transform', (d) => `translate(${d.x},${d.y})`);
    allNodes.each(function (node) {
        const nodeGroup = d3.select(this);
        renderers.nodeRenderer.renderOtherNode(nodeGroup, node, 0.05, 1);
    });
}

export function addZoomBehavior(svg, group) {
    const zoom = d3
        .zoom()
        .scaleExtent([0.5, 8])
        .on('zoom', (event) => {
            group.attr('transform', event.transform);
        });

    svg.call(zoom);
    return zoom;
}

// 添加重置缩放按钮
export function addResetButton(svg, zoom) {
    d3.select('body')
        .append('button')
        .text('重置缩放')
        .style('position', 'fixed')
        .style('bottom', '10px')
        .style('right', '10px')
        .on('click', () => {
            svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity);
        });
}
