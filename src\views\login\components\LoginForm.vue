<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-14 09:21:13
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-14 15:29:41
 * @FilePath: \platform-face-web\src\views\Login\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <a-form :wrapper-col="{ span: 24 }" :model="formState" name="basic" :label-col="{ span: 8 }" autocomplete="off">
    <a-form-item>
      <ul class="type-tabs">
        <li v-for="item in typeList" :key="item.value"
          :class="['tab-item', { 'is-active': activeIndex === item.value }]" @click="changTab(item)">
          {{ item.label }}
        </li>
      </ul>
    </a-form-item>
    <a-form-item has-feedback v-bind="validateInfos.keyNum">
      <a-input v-model:value="formState.keyNum" :placeholder="placeholder.keyNum">
        <template #prefix>
          <div class="input-prefix">
            <img src="@/assets/svg/login/key.svg" alt="" />
            <span>key号</span>
          </div>
        </template>
      </a-input>
    </a-form-item>
    <a-form-item has-feedback v-bind="validateInfos.userName">
      <a-input v-model:value="formState.userName" :placeholder="placeholder.userName">
        <template #prefix>
          <div class="input-prefix">
            <img src="@/assets/svg/login/account.svg" alt="" />
            <span>账号</span>
          </div>
        </template>
      </a-input>
    </a-form-item>
    <a-form-item has-feedback v-bind="validateInfos.password">
      <a-input-password v-model:value="formState.password" :placeholder="placeholder.password">
        <template #prefix>
          <div class="input-prefix">
            <img src="@/assets/svg/login/pass.svg" alt="" />
            <span>密码</span>
          </div>
        </template>
      </a-input-password>
    </a-form-item>
    <a-form-item>
      <a-button type="primary" class="login-button" @click.prevent="handleLogin" size="large" html-type="submit"
        :disabled="loading">{{ loading ? '登录中...' : '登录' }}</a-button>
    </a-form-item>
  </a-form>
</template>
<script setup>
import { useStore } from 'vuex';
import { Form, Input } from 'ant-design-vue';
const AFormItem = Form.Item;
const AInputPassword = Input.Password;
const { useForm } = Form;

const vStore = useStore();
const ruleRef = reactive({
  userName: [{ required: true, message: '请输入用户名' }],
  password: [{ required: true, message: '请输入密码' }],
});

const activeIndex = ref(1);

const typeList = [
  {
    label: '用户登录',
    value: 1,
  },
  {
    label: '管理员登录',
    value: 2,
  },
];

const formState = reactive({
  keyNum: '',
  userName: '',
  password: '',
});

const placeholder = {
  keyNum: '自动获取',
  userName: '用户名',
  password: '密码',
};

const { validate, validateInfos } = useForm(formState, ruleRef);
const loading = ref(false);

// 登录
const handleLogin = () => {
  loading.value = true;
  validate()
    .then(() => {
      const payload = toRaw(formState);
      // 发送登录请求
      return vStore.dispatch('auth/login', payload);
    })
    .catch((err) => {
      console.log('err', err);
    })
    .finally(() => {
      setTimeout(() => {
        loading.value = false;
      }, 500);
    });
};

// 获取KoalCookie
const handleGetKoalCookie = () => {
  const callback = (res) => {
    formState.keyNum = res.data;
  };
  // vStore.dispatch('auth/getKoalCookie', callback);
};

const changTab = item => {
  activeIndex.value = item.value
}

// 初始化页面请求
onMounted(async () => {
  handleGetKoalCookie();
});
</script>

<style scoped lang="scss">
.ant-form {
  width: 396px;
}

.login-form {
  width: 700px;
  height: 100%;
  color: var(--color-text);
  background: transparent;
  overflow: visible;
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;

  .ant-form {
    width: 50%;
    border: 1px solid #505863;
    padding: 50px 30px;
    border-radius: 15px;
  }

  .ant-form-item-control-input-content>span {
    background: transparent;
    border: 1px solid #505863;
    box-shadow: 0 1px 5px 0 rgba(255, 255, 255, 0.1);
    height: 48px;

    :deep(.ant-input) {
      background-color: transparent !important;
      height: 37px;
    }
  }

  .login-button {
    width: 100%;
    margin-top: 40px;
    background: transparent;
    color: #00a3ff;
    border: 1px solid #00a3ff;
    transition: 0.3s all;
    border-radius: 20px;
    font-size: 14px;
    box-shadow: 0 8px 10px 0 rgba(0, 163, 255, 0.4);

    &:hover {
      background: rgba(0, 163, 255, 0.2);
      transform: translateY(-3px);
      box-shadow: 0 0px 15px 0 rgba(0, 163, 255, 0.5);
      // color: var(--color-text);
    }
  }

  .ant-form-item {
    margin-bottom: 15px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  :deep(.ant-input-prefix) {
    color: var(--color-text);
    font-size: 13px;

    span {
      width: 60px;
      text-align: center;
      display: inline-block;
      border-right: 1px solid #505863;
    }
  }

  :deep(.ant-input-suffix) {
    .ant-form-item-feedback-icon-error {
      margin-left: 18px;
    }
  }

  :deep(.ant-input-disabled) {
    color: var(--color-text);
  }
}

:deep(.ant-input),
:deep(.ant-input-affix-wrapper) {
  color: var(--color-text) !important;
  transition: 0.3s all;

  &:hover,
  &:focus {
    border-color: #4DBFFF !important;
  }
}

.login-form {
  opacity: 0;
  animation: formEnter 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation-delay: 0.3s;
}

.type-tabs {
  display: flex;
  align-items: center;
  gap: 40px;
  color: #fff;
  margin-top: -10px;

  .tab-item {
    position: relative;
    color: #818a97;
    cursor: pointer;
    transition: 0.3s all;

    &:hover {
      color: #fff;
    }

    &:last-of-type {
      &::after {
        content: none;
      }
    }

    &::after {
      position: absolute;
      content: '';
      width: 1px;
      height: 50%;
      top: 50%;
      right: -20px;
      color: #fff;
      border-right: 1px solid #505863;
      transform: translateY(-50%);
    }
  }

  .is-active {
    font-size: 16px;
    color: #fff;
  }
}

@keyframes formEnter {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.ant-input) {
  height: 40px;
}

.input-prefix {
  height: 38px;
  display: flex;
  align-items: center;

  img {
    width: 15px;
    margin-right: 5px;
  }

  span {
    width: max-content !important;
    padding-right: 8px;
    margin-right: 5px;
    border-right: 1px solid #505863;
  }
}
:deep(.ant-input::placeholder) {
  color: #606060 !important;
  font-weight: bold;
  font-size: 13px;
}
</style>
