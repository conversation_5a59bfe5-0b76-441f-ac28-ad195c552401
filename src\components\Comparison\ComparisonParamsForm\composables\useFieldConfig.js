/*
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 17:12:48
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-29 11:33:08
 * @FilePath: \platform-face-web\src\components\Comparison\ComparisonParamsForm\composables\useFieldConfig.js
 * @Description: 
 * 
 * 
 */
import { computed } from 'vue';

/**
 * 字段配置管理 composable
 * 管理预设配置和字段配置计算逻辑
 */
export function useFieldConfig(props) {
  // 预设配置
  const PRESET_CONFIGS = {
    library: [
      { type: 'portraitGroup', label: '人像组' },
      { type: 'algorithm', label: '比对算法' },
      { type: 'numberOfReturns', label: '返回结果数' },
      { type: 'matchingThreshold', label: '比对阈值' }
    ],
    image: [
      { type: 'algorithm', label: '比对算法' },
      { type: 'matchingThreshold', label: '比对阈值' },
      { type: 'portraitGroup', label: '身份验证（人像组）' }
    ]
  };

  // 计算最终的字段配置
  const computedFieldConfig = computed(() => {
    // 如果传入了自定义配置，直接使用
    if (props.fieldConfig && props.fieldConfig.length > 0) {
      return props.fieldConfig;
    }
    return props.comparisonType === 'image-to-library'
      ? PRESET_CONFIGS.library
      : PRESET_CONFIGS.image;
  });

  // 获取弹窗容器
  const getPopupContainer = () => {
    // 尝试返回 Popover 的容器，如果没找到则回退到 body
    return document.querySelector('.popover') || document.body;
  };

  return {
    PRESET_CONFIGS,
    computedFieldConfig,
    getPopupContainer
  };
}
