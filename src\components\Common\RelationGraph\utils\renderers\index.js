/*
 * @Author: CaiXiaomin
 * @Date: 2025-07-18 12:00:13
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-21 14:49:44
 * @FilePath: \platform-face-web\src\components\Common\RelationGraph\utils\renderers\index.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
/**
 * 渲染器模块统一导出
 * 提供所有渲染器的便捷访问入口
 */

// 形状渲染器
export { CircleRenderer, RoundedRectRenderer, ShapeRendererFactory } from './ShapeRenderer.js';

// 装饰元素渲染器
export {
    TopLabelRenderer,
    AlgorithmRingRenderer,
    ScoreIndicatorRenderer,
    NodeLabelRenderer,
    DecorationRendererFactory,
} from './DecorationRenderer.js';

// 节点渲染器
export { CenterNodeRenderer, OtherNodeRenderer, NodeRendererFactory } from './NodeRenderer.js';

// 背景渲染器
export {
    ReferenceCircleRenderer,
    DashedCircleRenderer,
    ZoneMarkerRenderer,
    BackgroundRendererFactory,
} from './BackgroundRenderer.js';

// 连线渲染器
export { LinkRendererFactory, SingleCenterLinkRenderer, MultiCenterLinkRenderer } from './LinkRenderer.js';

// Tooltip渲染器
export { TooltipRendererFactory, TooltipRenderer } from './TooltipRenderer.js';

// 导入工厂类用于 createRendererSuite 函数
import { ShapeRendererFactory } from './ShapeRenderer.js';
import { DecorationRendererFactory } from './DecorationRenderer.js';
import { NodeRendererFactory } from './NodeRenderer.js';
import { BackgroundRendererFactory } from './BackgroundRenderer.js';
import { LinkRendererFactory } from './LinkRenderer.js';
import { TooltipRendererFactory } from './TooltipRenderer.js';

/**
 * 创建完整的渲染器套件
 * @param {ConfigManager} configManager - 配置管理器
 * @returns {Object} 包含所有渲染器的对象
 */
export function createRendererSuite(configManager) {
    return {
        nodeRenderer: new NodeRendererFactory(configManager),
        shapeRenderer: new ShapeRendererFactory(configManager),
        decorationRenderer: new DecorationRendererFactory(configManager),
        backgroundRenderer: new BackgroundRendererFactory(configManager),
        linkRenderer: new LinkRendererFactory(configManager),
        tooltipRenderer: new TooltipRendererFactory(configManager),
    };
}

/**
 * 渲染器类型常量
 */
export const RENDERER_TYPES = {
    SHAPES: {
        CIRCLE: 'circle',
        ROUNDED_RECT: 'rounded-rect',
    },
    DECORATIONS: {
        TOP_LABEL: 'topLabel',
        ALGORITHM_RING: 'algorithmRing',
        SCORE_INDICATOR: 'scoreIndicator',
        NODE_LABEL: 'nodeLabel',
    },
    NODES: {
        CENTER: 'center',
        OTHER: 'other',
    },
};
