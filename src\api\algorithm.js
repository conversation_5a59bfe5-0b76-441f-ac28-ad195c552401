import request from '@/utils/service';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

const cache = new Map();

const CACHE_TTL = 60 * 60 * 1000; // 1 小时，单位：毫秒

export async function getAlgorithms(payload) {
  const cacheKey = `algorithms`;
  const now = Date.now();

  if (cache.has(cacheKey)) {
    const { timestamp, result } = cache.get(cacheKey);
    if (now - timestamp < CACHE_TTL) {
      return Promise.resolve(result);
    } else {
      cache.delete(cacheKey);
    }
  }

  const result = await request.get(`${BASE_URL}/api/algorithms`, payload);
  cache.set(cacheKey, { result, timestamp: now });
  return result;
}

export async function searchAllAlgModels(data = {}) {
  return request.get(`${BASE_URL}/api/algorithms`, data);
}

export const algorithmNames = {
  ci_an: '融合模型',
  an: '正脸模型',
  ci: '蒙脸模型',
};

export const algorithmShortNames = {
  ci_an: '融',
  an: '正',
  ci: '蒙',
};
