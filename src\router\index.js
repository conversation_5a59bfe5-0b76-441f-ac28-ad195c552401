/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-10 17:35:59
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-15 10:38:22
 * @FilePath: \frontend-template\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHashHistory } from 'vue-router';
import routes from './routes';
import routerGuards from './routerGuards'; // 新增

const router = createRouter({
  //路由模式:createWebHashHistory为has模式，createWebHistory为history模式
  history: createWebHashHistory(),
  //路由的配置选项，routes为单独引进的
  routes,
  //路由导航让页面滚动到顶部
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 适配 vue-router 4 的 beforeEach
router.beforeEach((to, from, next) => {
  routerGuards(router, { to, _from: from, next });
});

export default router;
