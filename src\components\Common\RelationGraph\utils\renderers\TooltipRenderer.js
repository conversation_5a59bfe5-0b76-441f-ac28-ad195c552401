/**
 * Tooltip渲染器 - 负责所有tooltip相关的渲染和交互
 */

import * as d3 from 'd3';
import { toFixedDecimal } from '@/utils/tool';
import { VueTooltipManager } from '../VueTooltipManager.js';

/**
 * Tooltip渲染器
 */
export class TooltipRenderer {
    constructor(configManager) {
        this.configManager = configManager;
        this.vueTooltipManager = null;
        this.htmlTooltip = null;
    }

    /**
     * 初始化tooltip系统
     * @returns {Object} 包含tooltip实例和模式的对象
     */
    initialize() {
        const tooltipMode = this.configManager.get('tooltip.mode');
        
        if (tooltipMode === 'vue-component') {
            // 使用Vue组件tooltip
            if (!this.vueTooltipManager) {
                this.vueTooltipManager = new VueTooltipManager();
            }
            this.vueTooltipManager.init();
            return { mode: tooltipMode, instance: this.vueTooltipManager };
        } else {
            // 使用传统HTML tooltip
            this.htmlTooltip = d3
                .select('body')
                .append('div')
                .attr('class', 'relation-tooltip')
                .style('position', this.configManager.get('tooltip.style.position'))
                .style('visibility', this.configManager.get('tooltip.style.visibility'))
                .style('background', () => {
                    if (this.configManager.get('tooltip.specialMode.enabled')) {
                        return this.configManager.get('tooltip.specialMode.background');
                    } else {
                        return this.configManager.get('tooltip.style.background');
                    }
                })
                .style('color', this.configManager.get('tooltip.style.color'))
                .style('padding', this.configManager.get('tooltip.style.padding'))
                .style('border-radius', this.configManager.get('tooltip.style.borderRadius'))
                .style('pointer-events', this.configManager.get('tooltip.style.pointerEvents'));
            
            return { mode: tooltipMode, instance: this.htmlTooltip };
        }
    }

    /**
     * 显示tooltip
     * @param {Event} event - 鼠标事件
     * @param {Object} nodeData - 节点数据
     */
    show(event, nodeData) {
        const tooltipMode = this.configManager.get('tooltip.mode');
        
        if (tooltipMode === 'vue-component') {
            this.showVueTooltip(event, nodeData);
        } else {
            this.showHtmlTooltip(event, nodeData);
        }
    }

    /**
     * 显示Vue组件tooltip
     * @param {Event} event - 鼠标事件
     * @param {Object} nodeData - 节点数据
     */
    showVueTooltip(event, nodeData) {
        const vueConfig = this.configManager.get('tooltip.vueComponent');
        const tooltipStyle = this.configManager.get('tooltip.style');
        
        if (this.vueTooltipManager) {
            this.vueTooltipManager.show({
                content: vueConfig.component,
                data: nodeData,
                position: { x: event.pageX, y: event.pageY },
                style: tooltipStyle,
                props: {
                    nodeData,
                    showExtraInfo: vueConfig.showExtraInfo,
                    showActions: vueConfig.showActions,
                    algorithmColors: this.configManager.get('algorithms.colors'),
                    algorithmNames: this.configManager.get('algorithms.shortNames'),
                    ...vueConfig.props,
                },
            });
        }
    }

    /**
     * 显示HTML tooltip
     * @param {Event} event - 鼠标事件
     * @param {Object} nodeData - 节点数据
     */
    showHtmlTooltip(event, nodeData) {
        if (this.htmlTooltip) {
            const content = this.generateHtmlTooltipContent(nodeData);
            this.htmlTooltip.style('visibility', 'visible').html(content);
        }
    }

    /**
     * 更新tooltip位置
     * @param {Event} event - 鼠标事件
     */
    updatePosition(event) {
        const tooltipMode = this.configManager.get('tooltip.mode');
        
        if (tooltipMode === 'vue-component') {
            if (this.vueTooltipManager) {
                this.vueTooltipManager.updatePosition({ x: event.pageX, y: event.pageY });
            }
        } else {
            if (this.htmlTooltip) {
                this.htmlTooltip
                    .style('top', event.pageY - 10 + 'px')
                    .style('left', event.pageX + 10 + 'px');
            }
        }
    }

    /**
     * 隐藏tooltip
     */
    hide() {
        const tooltipMode = this.configManager.get('tooltip.mode');
        
        if (tooltipMode === 'vue-component') {
            if (this.vueTooltipManager) {
                this.vueTooltipManager.hide();
            }
        } else {
            if (this.htmlTooltip) {
                this.htmlTooltip.style('visibility', 'hidden');
            }
        }
    }

    /**
     * 生成HTML tooltip内容
     * @param {Object} nodeData - 节点数据
     * @returns {string} HTML内容
     */
    generateHtmlTooltipContent(nodeData) {
        return ` <strong>${nodeData.portrait.xm}</strong><br/>
        <div style="${this.configManager.get('tooltip.content.listStyle')}">
            ${nodeData.scoreData
                .map(
                    (item) => `
                <div style="${this.configManager.get('tooltip.content.itemStyle')}">
                    <span style="${this.configManager.get('tooltip.content.indicatorStyle')}"></span>
                    ${toFixedDecimal(item.score, 2)} ${this.configManager.getAlgorithmShortName(item.algorithmCode)}
                </div>
            `,
                )
                .join('')}
        </div>`;
    }

    /**
     * 为节点组添加tooltip事件监听器
     * @param {d3.Selection} nodeGroup - 节点组选择器
     * @param {Object} nodeData - 节点数据
     */
    addTooltipEvents(nodeGroup, nodeData) {
        const self = this;
        
        nodeGroup
            .on('mouseover', function (event, d) {
                if (!self.configManager.get('tooltip.specialMode.enabled')) {
                    self.show(event, d || nodeData);
                }
            })
            .on('mousemove', function (event) {
                self.updatePosition(event);
            })
            .on('mouseout', function () {
                self.hide();
            });
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.vueTooltipManager) {
            this.vueTooltipManager.cleanup();
            this.vueTooltipManager = null;
        }
        
        if (this.htmlTooltip) {
            this.htmlTooltip.remove();
            this.htmlTooltip = null;
        }
    }

    /**
     * 检查tooltip是否启用
     * @returns {boolean}
     */
    isEnabled() {
        return this.configManager.get('tooltip.enabled');
    }

    /**
     * 获取当前tooltip模式
     * @returns {string}
     */
    getMode() {
        return this.configManager.get('tooltip.mode');
    }
}

/**
 * Tooltip渲染器工厂
 */
export class TooltipRendererFactory {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 创建tooltip渲染器
     * @returns {TooltipRenderer}
     */
    createRenderer() {
        return new TooltipRenderer(this.configManager);
    }
}
