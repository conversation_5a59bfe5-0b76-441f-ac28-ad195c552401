# RelationGraph 组件

RelationGraph 是一个高度可配置的关系图谱可视化组件，支持单中心和多中心两种模式，提供丰富的配置选项和自定义能力。

## 目录

- [组件类型](#组件类型)
- [基本用法](#基本用法)
- [数据格式](#数据格式)
- [配置系统](#配置系统)
  - [节点配置](#节点配置)
  - [Tooltip配置](#tooltip配置)
  - [背景配置](#背景配置)
  - [算法颜色配置](#算法颜色配置)
  - [布局配置](#布局配置)
- [高级用法](#高级用法)
  - [Vue组件Tooltip](#vue组件tooltip)
  - [自定义渲染函数](#自定义渲染函数)
  - [区域标记](#区域标记)
- [事件处理](#事件处理)
- [性能优化](#性能优化)

## 组件类型

RelationGraph 提供两种主要组件：

1. **SingleCenterGraph**: 单中心关系图，适用于展示一个中心节点与多个周边节点的关系
2. **MultiCenterGraph**: 多中心关系图，适用于展示多个中心节点及其关联节点的复杂关系网络

## 基本用法

### 单中心图

```vue
<template>
  <div class="graph-container">
    <SingleCenterGraph :data="graphData" :config="graphConfig" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SingleCenterGraph from '@/components/Common/RelationGraph/SingleCenterGraph.vue';

// 图数据
const graphData = ref({
  nodes: [
    // 中心节点
    {
      photoId: 'center-1',
      isCenter: true,
      imageId: 'https://example.com/center.jpg',
    },
    // 周边节点
    {
      portrait: {
        portraitId: 'node-1',
        xm: '张三',
        standardPortraitImages: [{ imageId: 'https://example.com/node1.jpg' }],
      },
      scoreData: [
        { algorithmCode: 'ci_an', score: 0.95 },
        { algorithmCode: 'ci', score: 0.88 },
        { algorithmCode: 'an', score: 0.92 },
      ],
      scoreRank: 1,
    },
  ],
  links: [
    {
      source: 'center-1',
      target: 'node-1',
      score: 0.95,
    },
  ],
});

// 基础配置
const graphConfig = {
  nodes: {
    center: {
      size: { radius: 90 },
    },
  },
};
</script>
```

### 多中心图

```vue
<template>
  <div class="graph-container">
    <MultiCenterGraph :data="graphData" :linkThreshold="0.7" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import MultiCenterGraph from '@/components/Common/RelationGraph/MultiCenterGraph.vue';

// 多中心图数据
const graphData = ref({
  nodes: [
    // 中心节点1
    {
      photoId: 'center-1',
      isCenter: true,
      imageId: 'https://example.com/center1.jpg',
    },
    // 中心节点2
    {
      photoId: 'center-2',
      isCenter: true,
      imageId: 'https://example.com/center2.jpg',
    },
    // 周边节点
    // ...其他节点
  ],
  links: [
    // 连线数据
    // ...
  ],
});
</script>
```

## 数据格式

### 节点数据格式

```javascript
// 中心节点
{
  photoId: 'center-1',      // 唯一ID
  isCenter: true,           // 标记为中心节点
  imageId: 'image-url'      // 图片URL
}

// 周边节点
{
  portrait: {
    portraitId: 'node-1',   // 唯一ID
    xm: '张三',             // 名称
    age: 25,                // 年龄
    gender: 1,              // 性别
    standardPortraitImages: [
      { imageId: 'image-url' } // 图片URL
    ]
  },
  scoreData: [              // 算法分数数据
    { algorithmCode: 'ci_an', score: 0.95 },
    { algorithmCode: 'ci', score: 0.88 },
    { algorithmCode: 'an', score: 0.92 }
  ],
  scoreRank: 1              // 排名
}
```

### 连线数据格式

```javascript
{
  source: 'center-1',       // 源节点ID
  target: 'node-1',         // 目标节点ID
  score: 0.95               // 关联分数
}
```

## 配置系统

RelationGraph 使用统一的配置系统管理所有渲染配置，支持配置继承和覆盖。

### 节点配置

```javascript
const config = {
  nodes: {
    // 中心节点配置
    center: {
      shape: 'circle', // 形状: 'circle' | 'rounded-rect'
      size: {
        radius: 90, // 半径大小
      },
      style: {
        fill: '#16171c', // 填充颜色
        stroke: '#4DBFFF', // 边框颜色
        strokeWidth: 1, // 边框宽度
      },
      image: {
        clipPath: 'circle', // 裁剪路径: 'circle' | 'rounded-rect'
        borderRadius: 12, // 圆角矩形时使用
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      },
      // 中心节点圆圈配置
      circles: {
        enabled: true, // 是否显示外圈边框
        showOuterRing: true, // 是否显示外圈装饰环
        outerRing: {
          enabled: false, // 默认不显示装饰环
          radius: 100, // 装饰环半径
          style: {
            fill: 'none',
            stroke: '#4DBFFF',
            strokeWidth: 2,
            strokeDasharray: 'none', // 可以设置为 '5,5' 等虚线样式
          },
        },
      },
    },

    // 其他节点配置
    other: {
      shape: 'circle', // 形状: 'circle' | 'rounded-rect'
      style: {
        fill: '#16171c', // 填充颜色
        strokeWidth: 1, // 边框宽度
      },
      image: {
        clipPath: 'circle', // 裁剪路径
        borderRadius: 12, // 圆角矩形时使用
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      },
      // 算法圆环配置
      algorithmRings: {
        enabled: true, // 是否显示算法圆环
        mode: 'multiple', // 'multiple' | 'none'
        singleRingAlgorithm: 'highest', // 'highest' | 'ci_an' | 'ci' | 'an'
      },
      // TOP标签配置
      topLabel: {
        enabled: true, // 是否显示TOP标签
        fontSize: '12px', // 字体大小
        fill: '#fff', // 字体颜色
        offsetY: 20, // Y轴偏移
        format: 'TOP{rank}', // 格式，{rank}会被替换为实际排名
        showCondition: (rank) => rank < 4, // 显示条件，默认只显示前3名
      },
      // 分数缩放配置
      scoreScale: {
        normalScale: 1.0, // 正常缩放比例
        smallScale: 0.7, // 小尺寸缩放比例（排名>11时使用）
        threshold: 11, // 缩放阈值
      },
    },
  },
};
```

### Tooltip配置

```javascript
const config = {
  tooltip: {
    enabled: true, // 是否启用tooltip
    mode: 'html', // 渲染模式: 'html' | 'vue-component'

    // Vue组件配置（当mode为'vue-component'时使用）
    vueComponent: {
      component: null, // Vue组件定义
      props: {}, // 传递给组件的props
      showExtraInfo: true, // 是否显示额外信息
      showActions: false, // 是否显示操作按钮
    },

    style: {
      position: 'absolute',
      visibility: 'hidden',
      background: 'rgba(0,0,0,0.7)',
      color: 'white',
      padding: '8px',
      borderRadius: '4px',
      pointerEvents: 'none',
    },

    // 特殊tooltip模式
    specialMode: {
      enabled: false, // 是否启用特殊模式
      background: 'transparent',
    },

    // 内容配置（用于HTML模式）
    content: {
      nameStyle: 'font-weight: bold;',
      listStyle: 'margin-top: 5px;',
      itemStyle: 'margin: 3px 0; display: flex; align-items: center;',
      indicatorStyle: 'display: inline-block; width: 3px; height: 12px; margin-right: 5px; background: #FFFFFF4D;',
    },
  },
};
```

### 背景配置

```javascript
const config = {
  background: {
    // 参考圆配置
    referenceCircles: {
      enabled: true, // 是否显示参考圆
      style: {
        fill: 'none',
        stroke: '#8593A7',
        strokeWidth: 1,
      },
    },

    // 虚线圆配置
    dashedCircle: {
      enabled: true, // 是否显示虚线圆
      style: {
        fill: 'none',
        stroke: '#8593A7',
        strokeDasharray: '5,5',
        strokeWidth: 1,
      },
    },

    // 区域标记配置
    zoneMarkers: {
      enabled: false, // 默认关闭
      zones: [
        // 示例区域配置
        // {
        //   type: 'circle',
        //   radius: 100,
        //   color: '#ff6b6b',
        //   label: '核心区域',
        //   opacity: 0.1
        // }
      ],
    },
  },
};
```

### 算法颜色配置

```javascript
const config = {
  algorithms: {
    colors: {
      ci_an: '#90FBC9', // 融合算法颜色
      ci: '#4DBFFF', // 蒙版算法颜色
      an: '#8F8CF3', // 正面算法颜色
    },
    shortNames: {
      ci_an: '融', // 融合算法简称
      an: '正', // 正面算法简称
      ci: '蒙', // 蒙版算法简称
    },
    defaultColor: '#999', // 默认颜色
  },
};
```

### 布局配置

```javascript
const config = {
  layout: {
    margin: { top: 20, right: 30, bottom: 40, left: 50 },
    // 螺旋布局配置
    spiral: {
      spiralNodeCount: 11, // 螺旋排列的节点数量
      innerRadius: 120, // 内圈半径
      outerRadius: 300, // 外圈半径
      sideMinRadius: 400, // 周边节点最小半径
      sideMaxRadius: 800, // 周边节点最大半径
      baseAngleStep: 45, // 基础角度步长
      innerRanks: [1, 2, 3], // 内圈排名范围
      outerStartRank: 4, // 外圈开始排名
      minGap: 5, // 节点间最小间距
    },
  },
};
```

## 高级用法

### Vue组件Tooltip

RelationGraph 支持使用 Vue 组件作为 tooltip 内容，提供更丰富的交互体验。

```javascript
import NodeTooltip from './components/NodeTooltip.vue';

const graphConfig = {
  tooltip: {
    mode: 'vue-component',
    vueComponent: {
      component: NodeTooltip,
      showExtraInfo: true,
      showActions: true,
      props: {
        customProp: 'value',
      },
    },
    style: {
      padding: '0px', // Vue组件自己处理内边距
      borderRadius: '12px',
      border: '1px solid rgba(77, 191, 255, 0.3)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
    },
  },
};
```

### 区域标记

可以在图表中添加区域标记，用于突出显示特定区域：

```javascript
const graphConfig = {
  background: {
    zoneMarkers: {
      enabled: true,
      zones: [
        {
          type: 'circle',
          radius: 200,
          color: '#ff6b6b',
          label: '核心区域',
          opacity: 0.1,
        },
        {
          type: 'ring',
          innerRadius: 300,
          outerRadius: 400,
          color: '#4ecdc4',
          label: '外围区域',
          opacity: 0.05,
        },
      ],
    },
  },
};
```

## 事件处理

RelationGraph 组件支持多种事件处理：

```vue
<template>
  <SingleCenterGraph :data="graphData" :config="graphConfig" @node-click="handleNodeClick" @view-detail="handleViewDetail" @compare="handleCompare" />
</template>

<script setup>
const handleNodeClick = (node) => {
  console.log('Node clicked:', node);
};

const handleViewDetail = (node) => {
  console.log('View detail for:', node);
};

const handleCompare = (node) => {
  console.log('Compare node:', node);
};
</script>
```

## 性能优化

对于大量节点的场景，可以考虑以下优化措施：

1. 使用简单的 HTML tooltip 而非 Vue 组件
2. 减少节点的视觉效果复杂度
3. 调整 `spiralNodeCount` 参数，减少螺旋排列的节点数量
4. 对于多中心图，适当提高 `linkThreshold` 值，减少显示的连线数量
