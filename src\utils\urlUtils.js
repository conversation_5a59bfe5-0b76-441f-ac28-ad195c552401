export const urlUtils = {
    /**
     * 获取当前 URL 中指定的参数值
     * @param {String} name 参数名称
     * @param {String} hashURL 默认从 location.hash 中获取参数
     * @returns {String|null} 参数值
     */
    queryURL(name, hashURL = window.location.hash.split('?')[1]) {
      if (!hashURL) return null;
  
      const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
      const match = hashURL.match(reg);
      return match ? decodeURIComponent(match[2]) : null;
    },
  
    /**
     * 获取 URL 中所有参数并返回为对象
     * @param {String} href 默认从 location.hash 中获取参数
     * @returns {Object} 参数对象
     */
    getUrlParams(href = window.location.hash.split('?')[1]) {
      if (!href) return {};
  
      return href.split('&').reduce((params, param) => {
        const [key, value] = param.split('=');
        if (key && key !== 'from' && key !== '_k') {
          params[key] = decodeURIComponent(value || '');
        }
        return params;
      }, {});
    },
  
    /**
     * 获取 `from` 参数并拼接其他参数
     * @returns {String} 拼接后的 `from` 参数
     */
    getFromParam() {
      let from = this.queryURL('from') || '/';
      const urlParams = this.getUrlParams();
  
      Object.entries(urlParams).forEach(([key, value]) => {
        from += from.includes('?') ? `&${key}=${value}` : `?${key}=${value}`;
      });
  
      return from;
    },
  
    /**
     * 向字符串形式的参数中追加新参数
     * @param {String} key 参数名
     * @param {String} value 参数值
     * @param {String} str 原有参数字符串
     * @returns {String} 新的参数字符串
     */
    appendParams(key, value, str = '') {
      const separator = str.includes('?') ? '&' : '';
      return `${str}${separator}${key}=${value}`;
    },
  };