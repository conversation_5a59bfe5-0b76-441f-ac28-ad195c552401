import { ref, nextTick } from 'vue';
import { debounce } from '@/utils/tool';
import { message } from 'ant-design-vue';

/**
 * FaceCanvas核心逻辑组合式函数
 * 处理图像加载、Canvas尺寸调整、重绘等核心功能
 */
export function useFaceCanvasLogic() {
  // 状态管理
  const imageLoadError = ref(false);
  const imageLoading = ref(false);
  const faceCanvasContext = ref(null);

  /**
   * 设置Canvas尺寸
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {DOMRect} containerRect - 容器尺寸
   */
  const setCanvasSize = (canvas, containerRect) => {
    canvas.width = containerRect.width;
    canvas.height = containerRect.height;
  };

  /**
   * 计算图像适应容器的缩放和偏移
   * @param {DOMRect} containerRect - 容器尺寸
   * @param {HTMLImageElement} image - 图像元素
   * @returns {Object} 包含scale和offset的对象
   */
  const calculateImageFitScale = (containerRect, image) => {
    const containerAspect = containerRect.width / containerRect.height;
    const imageAspect = image.naturalWidth / image.naturalHeight;

    if (imageAspect > containerAspect) {
      // 图像较宽，以容器宽度为准
      const baseScale = containerRect.width / image.naturalWidth;
      const offsetX = 0;
      const offsetY = (containerRect.height - image.naturalHeight * baseScale) / 2;
      return { scale: baseScale, offset: { x: offsetX, y: offsetY } };
    } else {
      // 图像较高，以容器高度为准
      const baseScale = containerRect.height / image.naturalHeight;
      const offsetY = 0;
      const offsetX = (containerRect.width - image.naturalWidth * baseScale) / 2;
      return { scale: baseScale, offset: { x: offsetX, y: offsetY } };
    }
  };

  /**
   * 创建调整Canvas尺寸的函数
   * @param {Object} refs - 引用对象
   * @param {Function} setZoomPanState - 设置缩放平移状态
   * @param {Function} redraw - 重绘函数
   * @returns {Function} adjustCanvasSize函数
   */
  const createAdjustCanvasSize = (refs, setZoomPanState, redraw) => {
    const adjustCanvasSize = () => {
      const containerRect = refs.faceCanvasContainer.value.getBoundingClientRect();
      setCanvasSize(refs.faceCanvas.value, containerRect);

      const { scale: newScale, offset: newOffset } = calculateImageFitScale(containerRect, refs.image);
      setZoomPanState({ scale: newScale, offset: newOffset });

      redraw();
    };

    return adjustCanvasSize;
  };

  /**
   * 创建图像加载函数
   * @param {Object} refs - 引用对象
   * @param {Function} adjustCanvasSize - 调整Canvas尺寸函数
   * @returns {Function} loadImage函数
   */
  const createLoadImage = (refs, adjustCanvasSize) => {
    const debouncedAdjustCanvasSize = debounce(adjustCanvasSize, 100);

    const loadImage = (imageSrc) => {
      if (!imageSrc) return;

      imageLoading.value = true;
      imageLoadError.value = false;

      refs.image.crossOrigin = 'anonymous';
      refs.image.onload = () => {
        imageLoading.value = false;
        imageLoadError.value = false;
        nextTick(() => {
          adjustCanvasSize();
          window.addEventListener('resize', debouncedAdjustCanvasSize);
        });
      };
      refs.image.onerror = () => {
        imageLoading.value = false;
        imageLoadError.value = true;
        message.error('图像加载失败，请检查图像地址是否正确');
      };
      refs.image.src = imageSrc;
    };

    return { loadImage, debouncedAdjustCanvasSize };
  };

  /**
   * 创建重绘函数
   * @param {Object} refs - 引用对象
   * @param {Object} state - 状态对象
   * @returns {Function} redraw函数
   */
  const createRedraw = (refs, state) => {
    const redraw = () => {
      if (!refs.faceCanvas.value || !refs.image.complete || imageLoadError.value) return;

      if (!faceCanvasContext.value) {
        faceCanvasContext.value = refs.faceCanvas.value.getContext('2d');
      }

      // 清空画布
      faceCanvasContext.value.clearRect(0, 0, refs.faceCanvas.value.width, refs.faceCanvas.value.height);

      // 绘制图像
      if (refs.image.naturalWidth > 0 && refs.image.naturalHeight > 0) {
        faceCanvasContext.value.drawImage(
          refs.image,
          state.offset.value.x,
          state.offset.value.y,
          refs.image.naturalWidth * state.scale.value,
          refs.image.naturalHeight * state.scale.value
        );
      }
    };

    // 防抖的重绘函数
    const debouncedRedraw = debounce(redraw, 16); // 约60fps

    return { redraw, debouncedRedraw };
  };

  /**
   * 坐标转换函数
   * @param {Object} canvasRect - Canvas坐标矩形
   * @param {Object} state - 状态对象
   * @returns {Object} 原始图像坐标
   */
  const convertToOriginalCoordinates = (canvasRect, state) => {
    return {
      x: (canvasRect.x - state.offset.value.x) / state.scale.value,
      y: (canvasRect.y - state.offset.value.y) / state.scale.value,
      width: canvasRect.width / state.scale.value,
      height: canvasRect.height / state.scale.value
    };
  };

  /**
   * 图像处理相关函数
   * @param {Object} refs - 引用对象，包含cropCanvas等
   * @returns {Object} 图像处理函数
   */
  const createImageProcessing = (refs) => {
    const cropCurrentRect = ({ x, y, width, height }) => {
      const canvas = refs.cropCanvas.value;
      if (!canvas) {
        throw new Error('cropCanvas is not available');
      }

      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');

      ctx.drawImage(
        refs.image, // 原始图像
        x, y, width, height, // 原图裁剪区域
        0, 0, width, height  // 在canvas上绘制区域
      );

      return canvas.toDataURL('image/png');
    };

    const validateDrawnRegion = (newFaceOriginal, faceBoxes) => {
      return faceBoxes.filter(face => {
        return (
          newFaceOriginal.x <= face.x &&
          newFaceOriginal.y <= face.y &&
          newFaceOriginal.x + newFaceOriginal.width >= face.x + face.width &&
          newFaceOriginal.y + newFaceOriginal.height >= face.y + face.height
        );
      });
    };

    return { cropCurrentRect, validateDrawnRegion };
  };

  /**
   * 资源清理函数
   * @param {Object} refs - 引用对象
   * @param {Function} debouncedAdjustCanvasSize - 防抖调整函数
   */
  const cleanup = (refs, debouncedAdjustCanvasSize) => {
    // 清理事件监听器
    window.removeEventListener('resize', debouncedAdjustCanvasSize);

    // 清理图像资源
    if (refs.image) {
      refs.image.onload = null;
      refs.image.onerror = null;
      refs.image.src = '';
    }

    // 清理Canvas上下文
    if (faceCanvasContext.value) {
      faceCanvasContext.value = null;
    }
  };

  return {
    // 状态
    imageLoadError,
    imageLoading,
    faceCanvasContext,

    // 函数工厂
    createAdjustCanvasSize,
    createLoadImage,
    createRedraw,
    createImageProcessing,

    // 工具函数
    convertToOriginalCoordinates,
    cleanup
  };
}
