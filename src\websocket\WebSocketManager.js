import SockJS from "sockjs-client/dist/sockjs";
import { Client } from '@stomp/stompjs';
import auth from '../utils/auth';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export default class WebSocketManager {
  constructor({ dispatch, subscribeName }) {
    this.state = {
      reconnectCounter: 0,
      maxReconnectCounter: 5,
      created: false
    };
    this.subscriptions = {};
    this.defaultDispatch = dispatch;

    this.stompClient = this.createStompClient();
    this.stompClient.activate();
  }

  createStompClient() {
    return new Client({
      reconnectDelay: 60000,
      logRawCommunication: false,
      webSocketFactory: () => {
        try {
          const token = auth.getJWTInfo(true);
          if (!token) {
            throw new Error('JWT token is missing');
          }
          return new SockJS(`${BASE_URL}/ws?token=${encodeURIComponent(token)}`);
        } catch (error) {
          console.error('Failed to initialize WebSocket: ', error);
          throw error;
        }
      },
      beforeConnect: () => this.handleBeforeConnect(),
      onConnect: () => this.handleOnConnect(),
      onDisconnect: () => this.handleOnDisconnect(),
      onStompError: (frame) => console.error('Stomp Error', frame),
      onWebSocketClose: (error) => console.warn('WebSocket close: ', error),
      onWebSocketError: (frame) => console.error('Stomp WebSocket Error', frame)
    });
  }

  handleBeforeConnect() {
    if (this.state.reconnectCounter >= this.state.maxReconnectCounter) {
      console.log('与消息推送服务器连接中断，请重新刷新');
      this.stompClient.deactivate();
    }
    this.state.reconnectCounter += 1;
  }

  handleOnConnect() {
    console.log('WebSocket connected');
    this.state.reconnectCounter = 0;
    this.state.created = true;
  }

  handleOnDisconnect() {
    console.warn('WebSocket disconnect');
    this.state.created = false;
  }

  subscribe({ dispatch, subscribeName, callback }) {
    if (this.subscriptions[subscribeName]) {
      console.warn(`Already subscribed to ${subscribeName}`);
      return;
    }

    if (this.state.created) {
      const destination = subscribeName;

      console.log(`subscribe destination: ${subscribeName}`);

      const subscription = this.stompClient.subscribe(destination, (response) => {
        console.log('response =', response);
        const data = this.parseResponse(response.body)?.data;
        console.log(`[${subscribeName}]`, '----response =', data);
        (dispatch || this.defaultDispatch)?.(subscribeName, data);
      });

      this.subscriptions[subscribeName] = subscription;
      console.log(`[${subscribeName}]`, '--- WebSocket ready');

      if (callback && typeof callback === 'function') {
        callback();
      }
    } else {
      console.warn('WebSocket not ready, retrying later...');
      setTimeout(() => this.subscribe({ dispatch, subscribeName, callback }), 1000);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  parseResponse(body) {
    if (!body) return [];
    try {
      return JSON.parse(body);
    } catch (error) {
      console.warn('Failed to parse WebSocket response:', error);
      return body;
    }
  }

  unsubscribe({ subscribeName }) {
    const subscription = this.subscriptions[subscribeName];
    if (subscription) {
      subscription.unsubscribe();
      delete this.subscriptions[subscribeName];
      console.warn(`Unsubscribed from ${subscribeName}`);
    } else {
      console.warn(`No subscription found for ${subscribeName}`);
    }
  }

  deactivate() {
    Object.values(this.subscriptions).forEach((sub) => sub.unsubscribe());
    this.subscriptions = {};
    this.stompClient.deactivate();
    this.state.reconnectCounter = 0;
    this.state.created = false;
    console.warn('WebSocket deactivated');
  }
}
