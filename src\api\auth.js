
import request from '@/utils/service';

let currentToken = null;
let refreshToken = null;
let tokenExpiration = null;

// 订阅 token 刷新事件
const tokenRefreshListeners = [];

// 登录
export function login(key, username, password) {
  return request.post('/login', { key, username, password });
}

// 获取用户密钥
export function getKoalCookie() {
  return request.post('/api/getKoalCookie');
}

// 登出
export function logout() {
  clearToken();
}

// 获取当前的 JWT token
export function getJWTInfo(allowExpired = false) {
  if (!allowExpired && tokenExpiration && new Date().getTime() >= tokenExpiration) {
    console.warn('Token has expired');
    return null;
  }
  return currentToken;
}

// 刷新 JWT token
export async function refreshJWTToken() {
  if (!refreshToken) {
    console.error('No refresh token available');
    return null;
  }
  const res = await request.post('/auth/refresh', { refreshToken });
  if (res && res.accessToken && res.expiresIn) {
    setToken(res.accessToken, refreshToken, res.expiresIn);
    notifyTokenRefreshed(res.accessToken);
    return res.accessToken;
  } else {
    clearToken();
    return null;
  }
}

// 设置 token 信息
export function setToken(accessToken, rt, expiresIn) {
  currentToken = accessToken;
  refreshToken = rt;
  tokenExpiration = new Date().getTime() + expiresIn * 1000;
  localStorage.setItem('accessToken', currentToken);
  localStorage.setItem('refreshToken', refreshToken);
  localStorage.setItem('tokenExpiration', tokenExpiration);
}

// 清除 token 信息
export function clearToken() {
  currentToken = null;
  refreshToken = null;
  tokenExpiration = null;
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('tokenExpiration');
}

// 通知所有 token 监视器
export function notifyTokenRefreshed(newToken) {
  tokenRefreshListeners.forEach((listener) => listener(newToken));
}

// 添加订阅
export function onTokenRefreshed(listener) {
  tokenRefreshListeners.push(listener);
}

export async function authFetch(url, options = {}) {
  let token = getJWTInfo();
  if (!token) {
    token = await refreshJWTToken();
    if (!token) {
      throw new Error('Authentication failed: Unable to refresh token');
    }
  }
  const headers = {
    ...options.headers,
    Authorization: `Bearer ${token}`,
  };
  // 用 axios 方式请求
  const config = {
    ...options,
    headers,
    url,
    method: options.method || 'get',
  };
  let response = await request(config.method, url, config.data || config.params || {}, { headers });
  // 如果 token 无效或过期，尝试刷新
  if (response && response.status === 401) {
    token = await refreshJWTToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      response = await request(config.method, url, config.data || config.params || {}, { headers: config.headers });
    }
  }
  return response;
}

// 恢复 token（页面刷新后从 localStorage 恢复）
export function restoreToken() {
  const savedToken = localStorage.getItem('accessToken');
  const savedRefreshToken = localStorage.getItem('refreshToken');
  const savedExpiration = parseInt(localStorage.getItem('tokenExpiration'), 10);
  if (savedToken && savedRefreshToken && savedExpiration) {
    currentToken = savedToken;
    refreshToken = savedRefreshToken;
    tokenExpiration = savedExpiration;
  }
}

restoreToken();
