# BadgeWrapper 徽章包装器组件

## 组件描述

BadgeWrapper 是一个通用的徽章包装器组件，为任意内容添加徽章标记，支持动态内容变化时的自适应布局。

## 功能特性

- ✅ **自适应布局**：支持动态内容变化时的自动布局调整
- ✅ **防抖优化**：使用防抖函数避免频繁的DOM操作
- ✅ **尺寸监听**：使用ResizeObserver监听容器尺寸变化
- ✅ **Props验证**：完整的参数类型验证和边界检查
- ✅ **错误处理**：完善的错误边界处理和警告提示
- ✅ **性能优化**：批量更新状态，减少响应式触发次数

## 使用场景

- 消息提醒数量显示
- 状态标记
- 计数器显示
- 通知徽章

## Props 参数

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| count | String/Number | undefined | 否 | 徽章显示的数值 |
| showZero | Boolean | false | 否 | 当count为0时是否显示徽章 |
| offset | Object | {top: '-1px', right: '-12px'} | 否 | 徽章位置偏移量 |
| color | String | 'transparent' | 否 | 徽章背景色 |
| fontSize | String | '12px' | 否 | 徽章字体大小 |
| fontColor | String | '#fff' | 否 | 徽章字体颜色 |

### Props 详细说明

#### count
- **类型**：`String | Number`
- **验证规则**：
  - 字符串类型：非空且可转换为数字
  - 数字类型：必须大于等于0
- **示例**：`5`、`"10"`、`99`

#### showZero
- **类型**：`Boolean`
- **说明**：控制当count为0时是否显示徽章
- **示例**：`true` - 显示0，`false` - 不显示

#### offset
- **类型**：`Object`
- **格式**：`{top: string, right: string}`
- **验证规则**：必须包含top和right属性，且都为字符串类型
- **示例**：`{top: '-5px', right: '-10px'}`

#### color
- **类型**：`String`
- **说明**：徽章背景色，支持任何有效的CSS颜色值
- **示例**：`'#ff4d4f'`、`'red'`、`'rgba(255, 0, 0, 0.8)'`

#### fontSize
- **类型**：`String`
- **验证规则**：必须是有效的CSS字体大小格式
- **支持单位**：`px`、`em`、`rem`、`%`
- **示例**：`'14px'`、`'1.2em'`、`'0.8rem'`

#### fontColor
- **类型**：`String`
- **说明**：徽章字体颜色，支持任何有效的CSS颜色值
- **示例**：`'#ffffff'`、`'white'`、`'rgba(255, 255, 255, 0.9)'`

## 使用示例

### 基础用法

```vue
<template>
  <BadgeWrapper :count="5" color="#ff4d4f">
    <a-button>消息</a-button>
  </BadgeWrapper>
</template>

<script setup>
import BadgeWrapper from '@/components/Common/BadgeWrapper/index.vue'
</script>
```

### 动态内容

```vue
<template>
  <div>
    <BadgeWrapper :count="messageCount" color="#52c41a">
      <a-button>{{ buttonText }}</a-button>
    </BadgeWrapper>
    
    <a-button @click="toggleText">切换文本长度</a-button>
    <a-button @click="incrementCount">增加计数</a-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BadgeWrapper from '@/components/Common/BadgeWrapper/index.vue'

const messageCount = ref(5)
const buttonText = ref('消息')

const toggleText = () => {
  buttonText.value = buttonText.value === '消息' ? '这是一个很长的按钮文本' : '消息'
}

const incrementCount = () => {
  messageCount.value++
}
</script>
```

### 自定义样式

```vue
<template>
  <BadgeWrapper 
    :count="99" 
    color="#722ed1"
    fontSize="14px"
    fontColor="#ffffff"
    :offset="{top: '-2px', right: '-15px'}"
  >
    <a-button type="primary">通知</a-button>
  </BadgeWrapper>
</template>
```

### 显示零值

```vue
<template>
  <BadgeWrapper 
    :count="0" 
    :showZero="true"
    color="#faad14"
  >
    <a-button>待处理</a-button>
  </BadgeWrapper>
</template>
```

## 技术实现

### 核心特性

1. **防抖优化**：使用项目内置的防抖函数，避免频繁的DOM操作
2. **ResizeObserver**：监听容器尺寸变化，自动调整徽章布局
3. **批量更新**：一次性完成所有布局计算，减少响应式触发
4. **错误处理**：完善的边界检查和错误提示

### 性能优化

- 使用100ms防抖延迟，平衡响应性和性能
- 批量更新响应式状态，减少重渲染
- 智能的DOM元素验证，避免无效操作
- 组件卸载时自动清理ResizeObserver

## 注意事项

1. **DOM就绪性**：组件会自动检查DOM元素是否就绪，如果未就绪会输出警告
2. **容器宽度**：组件会自动调整容器的paddingRight以避免徽章被截断
3. **动态内容**：支持内容动态变化，会自动重新计算布局
4. **浏览器兼容性**：ResizeObserver需要现代浏览器支持，不支持时会降级处理

## 常见问题

### Q: 徽章位置不正确？
A: 检查offset参数是否正确，确保容器有足够的空间显示徽章。

### Q: 动态内容变化时徽章位置没有更新？
A: 组件已自动监听count和offset变化，如果仍有问题，请检查是否有CSS样式冲突。

### Q: 性能问题？
A: 组件已使用防抖和批量更新优化，如果仍有性能问题，可以调整防抖延迟时间。

## 更新日志

### v1.1.0 (2025-07-16)
- ✅ 添加Props验证和类型检查
- ✅ 重构核心逻辑，使用防抖优化
- ✅ 添加ResizeObserver支持
- ✅ 完善错误处理和边界检查
- ✅ 优化性能，减少DOM操作
- ✅ 完善组件文档

### v1.0.0 (2025-07-16)
- ✅ 初始版本发布
- ✅ 基础徽章功能
- ✅ 动态内容支持
