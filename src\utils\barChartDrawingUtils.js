/**
 * 柱状图绘制工具函数集合
 * 提供可复用的柱状图绘制功能
 */
import { toFixedDecimal } from '@/utils/tool';
import { createBaseRect, createBaseText, createBaseImage, BAR_CHART_COLORS } from '@/utils/chartUtils';

/**
 * 绘制标准的柱状图背景和前景
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {Object} 包含背景和前景矩形的对象
 */
export const drawStandardBar = (container, config) => {
  const {
    className = 'bar',
    x = 0,
    width = 32,
    score = 1,
    altitudeHeight = 100,
    backgroundFill = BAR_CHART_COLORS.BACKGROUND,
    foregroundFill = BAR_CHART_COLORS.GRADIENTS.GREEN,
    dataId = null,
    isInverted = false, // 是否为倒置柱状图（如文本匹配）
  } = config;

  let backgroundRect, foregroundRect;

  if (isInverted) {
    // 倒置柱状图（从底部向上）
    foregroundRect = createBaseRect(container, {
      className,
      x,
      y: 1,
      width,
      height: score * altitudeHeight,
      fill: foregroundFill,
      dataId,
    });

    backgroundRect = createBaseRect(container, {
      className,
      x,
      y: score * altitudeHeight,
      width,
      height: Math.abs(altitudeHeight - score * altitudeHeight),
      fill: backgroundFill,
      dataId,
    });
  } else {
    // 标准柱状图（从顶部向下）
    backgroundRect = createBaseRect(container, {
      className,
      x,
      y: -altitudeHeight,
      width,
      height: Math.abs(altitudeHeight - score * altitudeHeight),
      fill: backgroundFill,
      dataId,
    });

    foregroundRect = createBaseRect(container, {
      className,
      x,
      y: -(score * altitudeHeight),
      width,
      height: score * altitudeHeight - 1,
      fill: foregroundFill,
      dataId,
    });
  }

  return { backgroundRect, foregroundRect };
};

/**
 * 绘制柱状图分数文本
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {d3.Selection} 文本选择器
 */
export const drawBarScoreText = (container, config) => {
  const {
    className = 'score-text',
    x = 0,
    y = 0,
    score = 0,
    text = null,
    fontSize = '12px',
    fill = BAR_CHART_COLORS.TEXT_PRIMARY,
    dataId = null,
    precision = 2,
  } = config;

  const displayText = text !== null ? text : toFixedDecimal(score, precision);

  return createBaseText(container, {
    className,
    x,
    y,
    text: displayText,
    fontSize,
    fill,
    dataId,
    dy: '1em',
  });
};

/**
 * 绘制补充分数文本
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {d3.Selection} 文本选择器
 */
export const drawCompensationScoreText = (container, config) => {
  const { className = 'compensation-score', x = 0, y = 0, score = 0, shouldShow = true, fontSize = '12px', dataId = null, precision = 2 } = config;

  const compensationScore = 1 - parseFloat(toFixedDecimal(score, precision));
  const fill = shouldShow ? BAR_CHART_COLORS.TEXT_SECONDARY : BAR_CHART_COLORS.TRANSPARENT;
  if (!compensationScore) return;

  return createBaseText(container, {
    className,
    x,
    y,
    text: toFixedDecimal(compensationScore, precision),
    fontSize,
    fill,
    dataId,
    dy: '1em',
  });
};

/**
 * 绘制算法标识矩形
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {Object} 包含矩形和文本的对象
 */
export const drawAlgorithmBadge = (container, config) => {
  const {
    className = 'algorithm',
    x = 0,
    y = -25,
    width = 22,
    height = 17,
    algorithmText = '',
    isExactMatch = false,
    shouldShowBorder = false,
    dataId = null,
    rx = 4,
    ry = 4,
  } = config;

  // 算法矩形
  const rect = createBaseRect(container, {
    className: `${className}-rect`,
    x: x + 5,
    y,
    width: width - 10,
    height,
    fill: isExactMatch ? BAR_CHART_COLORS.GRADIENTS.GREEN_AND_BLUE : 'none',
    stroke: isExactMatch ? 'none' : shouldShowBorder ? BAR_CHART_COLORS.WHITE : 'none',
    'stroke-width': 1,
    rx,
    ry,
    dataId,
  });

  // 算法文本
  const text = createBaseText(container, {
    className: `${className}-text`,
    x: x + width / 2,
    y,
    text: algorithmText,
    fill: getAlgorithmTextColor(isExactMatch, shouldShowBorder),
    dataId,
    dy: '1em',
  });

  return { rect, text };
};

/**
 * 绘制图标
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {d3.Selection} 图像选择器
 */
export const drawIcon = (container, config) => {
  const { className = 'icon', x = 0, y = 0, width = 16, height = 16, iconSrc = '', dataId = null } = config;

  return createBaseImage(container, {
    className,
    x,
    y,
    width,
    height,
    href: iconSrc,
    dataId,
  });
};

/**
 * 绘制操作区域（透明的交互区域）
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {d3.Selection} 矩形选择器
 */
export const drawOperationArea = (container, config) => {
  const { className = 'operation-area', x = 0, y = 0, width = 32, height = 100, dataId = null, onClick = null } = config;

  const rect = createBaseRect(container, {
    className,
    x,
    y,
    width,
    height,
    fill: BAR_CHART_COLORS.TRANSPARENT,
    dataId,
  }).style('cursor', 'pointer');

  if (onClick) {
    rect.on('click', onClick);
  }

  return rect;
};

/**
 * 获取算法文本颜色
 * @param {boolean} isExactMatch - 是否完全匹配
 * @param {boolean} shouldShowBorder - 是否显示边框
 * @returns {string} 颜色值
 */
const getAlgorithmTextColor = (isExactMatch, shouldShowBorder) => {
  if (isExactMatch) {
    return 'black';
  } else if (shouldShowBorder) {
    return BAR_CHART_COLORS.WHITE;
  } else {
    return BAR_CHART_COLORS.TRANSPARENT;
  }
};

/**
 * 计算柱状图的标准尺寸
 * @param {Object} config - 配置对象
 * @returns {Object} 尺寸信息
 */
export const calculateBarDimensions = (config) => {
  const { score = 0, altitudeHeight = 100, isInverted = false } = config;

  if (isInverted) {
    return {
      foregroundHeight: score * altitudeHeight,
      backgroundHeight: Math.abs(altitudeHeight - score * altitudeHeight),
      foregroundY: 1,
      backgroundY: score * altitudeHeight,
    };
  } else {
    return {
      foregroundHeight: score * altitudeHeight - 1,
      backgroundHeight: Math.abs(altitudeHeight - score * altitudeHeight),
      foregroundY: -(score * altitudeHeight),
      backgroundY: -altitudeHeight,
    };
  }
};

/**
 * 创建标准的柱状图绘制配置
 * @param {Object} data - 数据项
 * @param {Object} baseConfig - 基础配置
 * @returns {Object} 完整的绘制配置
 */
export const createStandardBarConfig = (data, baseConfig) => {
  const { x = 0, rectWidth = 32, altitudeHeight = 100, className = 'bar' } = baseConfig;

  return {
    x,
    width: rectWidth,
    altitudeHeight,
    className,
    dataId: `${data.algorithmCode}-${data.portrait.portraitId}`,
    score: data.score || 0,
  };
};
