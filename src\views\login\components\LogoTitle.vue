<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-14 10:57:40
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-14 11:01:05
 * @FilePath: \platform-face-web\src\views\login\components\LogoTitle.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="logo-container">
    <div v-if="showTop" class="logo-title">
      <img src="@/assets/svg/common/logo.svg" alt="平台logo" />
      <div class="title">数智AI-人脸多模态-系统v1.0</div>
    </div>
    <div v-if="showBottom" class="logo-img">
      <img src="@/assets/svg/common/home-face.svg" :style="logoStyle" alt="人脸图标" />
    </div>
    <slot />
  </div>
</template>
<script setup>
const props = defineProps({
  showTop: {
    type: Boolean,
    default: true,
  },
  showBottom: {
    type: Boolean,
    default: true,
  },
  logoStyle: {
    type: Object,
    default: () => ({})
  }
});
</script>

<style lang="scss" scoped>
.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.logo-title {
  display: flex;
  align-items: center;
  padding-bottom: 15px;
  img {
    width: 60px;
    height: 60px;
  }
  .title {
    color: #fff;
    font-size: 29px;
    margin-left: 12px;
  }
}
.logo-img {
    width: 100%;
    text-align: center;
}
.logo-title, .logo-img {
    opacity: 0;
    animation: fadeIn 1s ease-in-out forwards;
  }

  .logo-img {
    animation-delay: 0.3s;
  }
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>