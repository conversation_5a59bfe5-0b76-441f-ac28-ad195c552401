// 此文件用于全局覆盖 Ant Design Vue 的样式。
.ant-slider {
    .ant-slider-track {
        background-image: linear-gradient(90deg,
                #4e7ab4 0%,
                #4dbfff 96%) !important;
    }

    .ant-slider-handle::after {
        box-shadow: 0 0 0 2px #4dbfff;
    }

    .ant-slider-handle:hover::after {
        box-shadow: 0 0 0 4px #4dbfff;
    }

    .ant-slider-handle:focus::after {
        box-shadow: 0 0 0 4px #4dbfff;
    }

    .ant-slider-dot-active {
        border-color: #4dbfff80;
    }
}

.ant-slider:hover {
    .ant-slider-handle::after {
        box-shadow: 0 0 0 2px #4dbfff;
    }

    .ant-slider-dot-active {
        border-color: #4dbfff33;
    }
}

.ant-slider.ant-slider-disabled {
    opacity: 0.4;
}
