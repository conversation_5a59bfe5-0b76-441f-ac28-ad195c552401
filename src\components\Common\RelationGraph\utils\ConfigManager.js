/**
 * RelationGraph 配置管理器
 * 统一管理所有渲染配置，支持配置继承和覆盖
 */

// 默认配置结构
const defaultConfig = {
  // 节点配置
  nodes: {
    // 中心节点配置
    center: {
      shape: 'circle', // 'circle' | 'rounded-rect'
      size: {
        radius: 90, // 对应原来的 centerRadius
      },
      style: {
        fill: '#16171c',
        stroke: '#4DBFFF',
        strokeWidth: 1,
      },
      image: {
        clipPath: 'circle', // 'circle' | 'rounded-rect'
        borderRadius: 12, // 圆角矩形时使用
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      },
      // 中心节点圆圈配置
      circles: {
        enabled: true, // 是否显示外圈边框
        showOuterRing: true, // 是否显示外圈装饰环
        outerRing: {
          enabled: false, // 默认不显示装饰环
          radius: 100, // 装饰环半径
          style: {
            fill: 'none',
            stroke: '#4DBFFF',
            strokeWidth: 2,
            strokeDasharray: 'none', // 可以设置为 '5,5' 等虚线样式
          },
        },
      },
    },

    // 其他节点配置
    other: {
      shape: 'circle', // 'circle' | 'rounded-rect'
      style: {
        fill: '#16171c',
        strokeWidth: 1,
      },
      image: {
        clipPath: 'circle',
        borderRadius: 12,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      },
      // 算法圆环配置
      algorithmRings: {
        enabled: true, // 是否显示算法圆环（多个圈）
        mode: 'multiple', // 'multiple' |  'none'
        // multiple: 显示所有算法的圆环
        // none: 不显示圆环，只显示图片
        singleRingAlgorithm: 'highest', // 'highest' | 'ci_an' | 'ci' | 'an'
      },
      // TOP标签配置
      topLabel: {
        enabled: true,
        fontSize: '12px',
        fill: '#fff',
        offsetY: 20,
        format: 'TOP{rank}', // {rank} 会被替换为实际排名
        showCondition: (rank) => rank < 4, // 只显示前3名
      },
      // 分数缩放配置
      scoreScale: {
        normalScale: 1.0,
        smallScale: 0.7, // 排名>11时使用
        threshold: 11,
      },
    },
  },

  // 连线配置
  links: {
    // 普通连线
    default: {
      stroke: '#8593A7',
      strokeWidth: 1,
      opacity: {
        visible: 0.6, // 圈内连线透明度
        hidden: 0, // 圈外连线透明度（可配置）
        highlight: 0.9, // 高亮时透明度
        dimmed: 0.1, // 非高亮时透明度
      },
    },

    // 多中心图连线配置
    multiCenter: {
      centerLinks: {
        stroke: '#4DBFFF',
        strokeWidth: 2,
        opacity: 1,
      },
      interGroup: {
        stroke: '#768191',
        strokeWidth: 1,
        opacity: 0.74,
      },
      intraGroup: {
        stroke: '#8593A7',
        strokeWidth: 1,
        opacity: 1,
      },
    },
  },

  // Tooltip配置
  tooltip: {
    enabled: true,
    // 渲染模式: 'html' | 'vue-component' | 'function'
    mode: 'html',
    // Vue组件配置（当mode为'vue-component'时使用）
    vueComponent: {
      component: null, // Vue组件定义
      props: {}, // 传递给组件的props
      showExtraInfo: true,
      showActions: false,
    },
    // 自定义渲染函数（当mode为'function'时使用）
    renderFunction: null,
    style: {
      position: 'absolute',
      visibility: 'hidden',
      background: 'rgba(0,0,0,0.7)',
      color: 'white',
      padding: '8px',
      borderRadius: '4px',
      pointerEvents: 'none',
    },
    // 特殊tooltip模式（对应原来的specialTooltip参数）
    specialMode: {
      enabled: false, // false对应specialTooltip=0, true对应specialTooltip=1
      background: 'transparent',
    },
    // 内容配置（用于HTML模式）
    content: {
      nameStyle: 'font-weight: bold;',
      listStyle: 'margin-top: 5px;',
      itemStyle: 'margin: 3px 0; display: flex; align-items: center;',
      indicatorStyle: 'display: inline-block; width: 3px; height: 12px; margin-right: 5px; background: #FFFFFF4D;',
    },
  },

  // 背景配置
  background: {
    // 参考圆配置
    referenceCircles: {
      enabled: true,
      style: {
        fill: 'none',
        stroke: '#8593A7',
        strokeWidth: 1,
      },
    },

    // 虚线圆配置
    dashedCircle: {
      enabled: true,
      style: {
        fill: 'none',
        stroke: '#8593A7',
        strokeDasharray: '5,5',
        strokeWidth: 1,
      },
    },

    // 区域标记配置
    zoneMarkers: {
      enabled: false, // 默认关闭
      zones: [
        // 示例区域配置
        // {
        //   type: 'circle',
        //   radius: 100,
        //   color: '#ff6b6b',
        //   label: '核心区域',
        //   opacity: 0.1
        // }
      ],
    },
  },

  // 算法颜色配置（从constants.js迁移）
  algorithms: {
    colors: {
      ci_an: '#90FBC9',
      ci: '#4DBFFF',
      an: '#8F8CF3',
    },
    shortNames: {
      ci_an: '融',
      an: '正',
      ci: '蒙',
    },
    defaultColor: '#999',
  },

  // 布局配置
  layout: {
    margin: { top: 20, right: 30, bottom: 40, left: 50 },
    // 螺旋布局配置
    spiral: {
      // 螺旋排列的节点数量（默认前11个节点按螺旋排列）
      spiralNodeCount: 11,
      // 内圈半径配置
      innerRadius: 120,
      // 外圈半径配置
      outerRadius: 300,
      // 周边节点半径范围
      sideMinRadius: 400,
      sideMaxRadius: 800,
      // 基础角度步长
      baseAngleStep: 45,
      // 内圈排名范围
      innerRanks: [1, 2, 3],
      // 外圈开始排名
      outerStartRank: 4,
      // 节点间最小间距
      minGap: 5,
    },
  },
};

/**
 * 配置管理器类
 */
class ConfigManager {
  constructor(customConfig = {}) {
    this.config = this.mergeConfig(defaultConfig, customConfig);
  }

  /**
   * 深度合并配置对象
   */
  mergeConfig(defaultConfig, customConfig) {
    const result = { ...defaultConfig };

    for (const key in customConfig) {
      if (customConfig[key] && typeof customConfig[key] === 'object' && !Array.isArray(customConfig[key])) {
        result[key] = this.mergeConfig(result[key] || {}, customConfig[key]);
      } else {
        result[key] = customConfig[key];
      }
    }

    return result;
  }

  /**
   * 获取配置值
   */
  get(path) {
    return this.getNestedValue(this.config, path);
  }

  /**
   * 设置配置值
   */
  set(path, value) {
    this.setNestedValue(this.config, path, value);
  }

  /**
   * 获取嵌套对象的值
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * 设置嵌套对象的值
   */
  setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * 获取算法颜色
   */
  getAlgorithmColor(algorithmCode) {
    return this.get(`algorithms.colors.${algorithmCode}`) || this.get('algorithms.defaultColor');
  }

  /**
   * 获取算法简称
   */
  getAlgorithmShortName(algorithmCode) {
    return this.get(`algorithms.shortNames.${algorithmCode}`) || algorithmCode;
  }

  /**
   * 获取完整配置对象（用于调试）
   */
  getFullConfig() {
    return { ...this.config };
  }
}

export default ConfigManager;
export { defaultConfig };
