/*
 * @Author: CaiXiaomin
 * @Date: 2025-08-01 10:51:37
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 15:36:57
 * @FilePath: \platform-face-web\src\api\library\library.js
 * @Description:
 *
 *
 */
import request from '@/utils/service';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

// 图片检测
export const imageDetection = (data) => {
    return request.post(`${BASE_URL}/api/detections/detect`, data);
};

// 图片比对
export const imageComparison = (data) => {
    return request.post(`${BASE_URL}/api/comparisons/images-compare-images`, data);
};
