<!--
 * @Author: <PERSON>ai<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-23 17:30:51
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 10:04:15
 * @FilePath: \platform-face-web\src\components\Common\FormSection\index.vue
 * @Description: 表单区域组件
 * 通用的表单区域组件，包含图标、标题和内容区域
 * 
-->
<template>
  <div class="form-section-wrapper">
    <!-- 标题区域 -->
    <div class="d-flex align-items-center g-1">
      <SvgIcon :icon-class="iconClass" :size="iconSize" />
      <span>{{ title }}</span>
    </div>

    <!-- 内容区域 -->
    <div class="form-section">
      <slot />
    </div>
  </div>
</template>

<script setup>
defineProps({
  /**
   * 区域标题
   */
  title: {
    type: String,
    required: true
  },

  /**
   * 图标类名
   */
  iconClass: {
    type: String,
    required: true
  },

  /**
   * 图标尺寸
   */
  iconSize: {
    type: String,
    default: '18px'
  }
});
</script>

<style scoped>
.form-section-wrapper {
  margin-bottom: 16px;
}

.form-section {
  border: 1px solid #3c4554;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}
</style>
