# ThumbnailDecorator 缩略图装饰器组件

## 组件描述

ThumbnailDecorator 是一个缩略图装饰器组件，用于在主内容上叠加显示缩略图，支持点击预览功能。该组件通过绝对定位将缩略图显示在主内容的指定位置，点击缩略图可以打开图片预览器进行大图查看。

## 功能特性

- ✅ **叠加显示**：在主内容上叠加显示缩略图
- ✅ **位置控制**：支持自定义缩略图的位置（bottom、right）
- ✅ **尺寸控制**：支持自定义缩略图的宽度和高度
- ✅ **点击预览**：点击缩略图可打开图片预览器
- ✅ **防抖处理**：防止快速点击导致的重复操作
- ✅ **懒加载支持**：支持图片懒加载优化性能
- ✅ **事件回调**：支持图片加载、错误等事件回调

## 使用场景

- 视频播放器缩略图显示
- 图片对比展示
- 产品图片预览
- 内容卡片缩略图
- 任何需要在主内容上叠加缩略图的场景

## Props 参数

| 参数名            | 类型    | 默认值   | 必填 | 说明                      |
| ----------------- | ------- | -------- | ---- | ------------------------- |
| showOriginalPhoto | Boolean | true     | 否   | 是否显示缩略图            |
| thumbnailSrc      | String  | -        | 是   | 缩略图源地址              |
| alt               | String  | '缩略图' | 否   | 图片alt属性               |
| imageWidth        | String  | -        | 是   | 缩略图宽度（如：'100px'） |
| imageHeight       | String  | -        | 是   | 缩略图高度（如：'100px'） |
| bottom            | String  | '0px'    | 否   | 距离底部的距离            |
| right             | String  | '0px'    | 否   | 距离右侧的距离            |

### Props 验证规则

- `thumbnailSrc`: 必须是非空字符串
- `imageWidth`: 必须是非空字符串
- `imageHeight`: 必须是非空字符串
- `bottom`: 必须是字符串类型
- `right`: 必须是字符串类型

## Events 事件

| 事件名      | 参数  | 说明                 |
| ----------- | ----- | -------------------- |
| image-click | Event | 缩略图点击时触发     |
| image-load  | -     | 缩略图加载完成时触发 |
| image-error | -     | 缩略图加载失败时触发 |

## 使用示例

### 基础用法

```vue
<template>
  <ThumbnailDecorator :thumbnail-src="thumbnailUrl" image-width="100px" image-height="100px" bottom="10px" right="10px" alt="产品缩略图">
    <img :src="mainImageUrl" alt="主图" />
  </ThumbnailDecorator>
</template>

<script setup>
import ThumbnailDecorator from '@/components/Common/ThumbnailDecorator/index.vue';

const thumbnailUrl = 'https://example.com/thumbnail.jpg';
const mainImageUrl = 'https://example.com/main-image.jpg';
</script>
```

### 视频播放器中使用

```vue
<template>
  <ThumbnailDecorator
    :thumbnail-src="videoThumbnail"
    image-width="120px"
    image-height="80px"
    bottom="20px"
    right="20px"
    alt="视频缩略图"
    @image-click="handleThumbnailClick"
    @image-load="handleImageLoad"
    @image-error="handleImageError">
    <video :src="videoUrl" controls />
  </ThumbnailDecorator>
</template>

<script setup>
import ThumbnailDecorator from '@/components/Common/ThumbnailDecorator/index.vue';

const videoUrl = 'https://example.com/video.mp4';
const videoThumbnail = 'https://example.com/video-thumbnail.jpg';

const handleThumbnailClick = (event) => {
  console.log('缩略图被点击:', event);
};

const handleImageLoad = () => {
  console.log('缩略图加载完成');
};

const handleImageError = () => {
  console.log('缩略图加载失败');
};
</script>
```

### 条件显示

```vue
<template>
  <ThumbnailDecorator
    :show-original-photo="showThumbnail"
    :thumbnail-src="thumbnailUrl"
    image-width="80px"
    image-height="80px"
    bottom="5px"
    right="5px">
    <div class="content">
      <h3>内容标题</h3>
      <p>内容描述...</p>
    </div>
  </ThumbnailDecorator>
</template>

<script setup>
import { ref } from 'vue';
import ThumbnailDecorator from '@/components/Common/ThumbnailDecorator/index.vue';

const showThumbnail = ref(true);
const thumbnailUrl = 'https://example.com/thumbnail.jpg';
</script>
```

### 不同位置示例

```vue
<template>
  <!-- 左上角 -->
  <ThumbnailDecorator
    :thumbnail-src="thumbnailUrl"
    image-width="60px"
    image-height="60px"
    bottom="auto"
    right="auto"
    style="--thumbnail-top: 10px; --thumbnail-left: 10px;">
    <div class="content">内容区域</div>
  </ThumbnailDecorator>
</template>

<style scoped>
.thumbnail-decorator .thumbnail-wrapper {
  top: var(--thumbnail-top);
  left: var(--thumbnail-left);
  bottom: unset;
  right: unset;
}
</style>
```

## 样式定制

组件使用SCSS编写，支持以下样式定制：

```scss
.thumbnail-decorator {
  .thumbnail-wrapper {
    // 自定义边框样式
    border: 2px solid #4dbfff;
    border-radius: 8px;

    // 自定义阴影
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .thumbnail {
    // 自定义hover效果
    &:hover {
      opacity: 0.9;
      transform: scale(1.05);
    }
  }
}
```

## 性能优化

- 使用防抖处理点击事件，避免重复操作
- 支持图片懒加载，提升页面性能
- 使用IntersectionObserver API进行视口检测
- 组件卸载时自动清理观察器

## 注意事项

1. **图片URL有效性**：确保传入的thumbnailSrc是有效的图片URL
2. **尺寸单位**：imageWidth和imageHeight需要包含单位（如'100px'、'50%'）
3. **位置计算**：bottom和right是相对于父容器的绝对定位
4. **z-index层级**：缩略图的z-index为3，确保不被其他元素遮挡
5. **浏览器兼容性**：懒加载功能需要IntersectionObserver API支持

## 更新日志

### v1.1.0 (2025-07-17)

- 修复ref导入问题
- 添加完整的JSDoc注释
- 优化Props验证和默认值设置
- 添加防抖处理点击事件
- 支持图片懒加载
- 优化DOM结构和样式
- 添加事件回调支持
- 完善组件文档
