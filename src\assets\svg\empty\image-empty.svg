<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="139.451" height="122.754" viewBox="0 0 139.451 122.754">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="0.328" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2e475c"/>
      <stop offset="1" stop-color="#17171c" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5593b7"/>
      <stop offset="1" stop-color="#202b37"/>
    </linearGradient>
    <filter id="减去_152" x="56.779" y="5.499" width="68.994" height="74.465" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2e475c"/>
      <stop offset="1" stop-color="#2e475c" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#5696ba"/>
      <stop offset="1" stop-color="#202b37"/>
    </linearGradient>
    <filter id="减去_152-2" x="11.223" y="0" width="79.677" height="85.463" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-2"/>
      <feFlood flood-opacity="0.651"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-5" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2e475c"/>
      <stop offset="1" stop-color="#222f3d" stop-opacity="0.161"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" y2="1.665" xlink:href="#linear-gradient-5"/>
  </defs>
  <g id="组_23003" data-name="组 23003" transform="translate(-388 -788.337)">
    <g id="组_22995" data-name="组 22995" transform="translate(388 799.836)">
      <ellipse id="椭圆_887" data-name="椭圆 887" cx="69.726" cy="34.026" rx="69.726" ry="34.026" transform="translate(0 43.203)" fill="url(#linear-gradient)"/>
      <g id="组_22945" data-name="组 22945" transform="translate(65.779 0)">
        <g id="组_22944" data-name="组 22944" transform="translate(11.631 0) rotate(14)">
          <g transform="matrix(0.97, -0.24, 0.24, 0.97, -77.89, 7.57)" filter="url(#减去_152)">
            <path id="减去_152-3" data-name="减去 152" d="M36.567,48.079H4a4,4,0,0,1-4-4V4A4,4,0,0,1,4,0H36.567a4,4,0,0,1,4,4v40.08A4,4,0,0,1,36.567,48.079ZM4.387,2.709a2,2,0,0,0-2,2V34.567a2,2,0,0,0,2,2H36.181a2,2,0,0,0,2-2V4.709a2,2,0,0,0-2-2Z" transform="translate(77.41 11.5) rotate(14)" fill="url(#linear-gradient-2)"/>
          </g>
        </g>
        <path id="路径_5005" data-name="路径 5005" d="M0,1.176V11.441a1.4,1.4,0,0,0,2.243.906l7.25-5.132a1.072,1.072,0,0,0,0-1.81L2.243.272A1.4,1.4,0,0,0,0,1.176" transform="matrix(0.966, 0.259, -0.259, 0.966, 24.946, 16.656)" fill="url(#linear-gradient-3)"/>
      </g>
      <g id="组_22943" data-name="组 22943" transform="matrix(0.978, -0.208, 0.208, 0.978, 26.223, 8.935)">
        <g transform="matrix(0.98, 0.21, -0.21, 0.98, -21.4, -25.44)" filter="url(#减去_152-2)">
          <path id="减去_152-4" data-name="减去 152" d="M36.567,48.079H4a4,4,0,0,1-4-4V4A4,4,0,0,1,4,0H36.567a4,4,0,0,1,4,4v40.08A4,4,0,0,1,36.567,48.079ZM4.387,2.709a2,2,0,0,0-2,2V34.567a2,2,0,0,0,2,2H36.181a2,2,0,0,0,2-2V4.709a2,2,0,0,0-2-2Z" transform="matrix(0.98, -0.21, 0.21, 0.98, 26.22, 20.43)" fill="url(#linear-gradient-4)"/>
        </g>
        <path id="路径_5003" data-name="路径 5003" d="M22.358,4.989,19.505,8.4a.96.96,0,0,1-1.434,0L11.224.319A.965.965,0,0,0,9.851.257L0,9.72l29.383-.044L23.689,4.886A.966.966,0,0,0,22.358,4.989Z" transform="translate(4.92 19.789)" fill="url(#linear-gradient-5)"/>
        <path id="路径_5004" data-name="路径 5004" d="M9.9,4.95A4.95,4.95,0,1,0,4.95,9.9,4.958,4.958,0,0,0,9.9,4.95Zm-8.212,0A3.259,3.259,0,1,1,4.95,8.213,3.266,3.266,0,0,1,1.688,4.95Z" transform="translate(23.3 9.889)" fill="url(#linear-gradient-6)"/>
      </g>
    </g>
  </g>
</svg>
