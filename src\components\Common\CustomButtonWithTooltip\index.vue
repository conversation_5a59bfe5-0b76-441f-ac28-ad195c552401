<!--
 * @Author: yuzhouisme
 * @Date: 2025-03-17 
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-29 15:35:11
 * @FilePath: \platform-face-web\src\components\Common\CustomButtonWithTooltip\index.vue
 * @Description:自定义按钮且支持提示
 * 功能描述：用于统一处理按钮
-->
<template>
  <a-tooltip v-if="tooltip && !disabled" v-model:visible="tooltipVisible" :title="tooltip" :color="tooltipColor"
    :overlay-inner-style="overlayStyle">
    <template #default>
      <a-button v-bind="$attrs" :icon="computedIcon" :loading="loading"
        :class="['position-relative', { 'custom-override': overrideStyle }, $attrs.class]"
        :style="[customStyle, $attrs.style]" :disabled="disabled" @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave" @click="handleClick">
        <slot></slot>
      </a-button>
    </template>
  </a-tooltip>
  <template v-else>
    <a-button v-bind="$attrs" :icon="computedIcon" :loading="loading"
      :class="['position-relative', { 'custom-override': overrideStyle }, $attrs.class]"
      :style="[customStyle, $attrs.style]" :disabled="disabled" @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave" @click="handleClick">
      <slot></slot>
    </a-button>
  </template>
</template>

<script setup>
import { ref, computed, h } from 'vue';
import SvgIcon from '@/components/Common/SvgIcon/index.vue';


// 定义本组件的 props
const props = defineProps({
  // 提示
  tooltip: { type: String, default: '' },
  manualTooltip: { type: Boolean, default: false },
  showTooltip: { type: Boolean, default: false },
  tooltipColor: { type: String, default: '#D8E9FE' },
  tooltipFontColor: { type: String, default: '#16171C' },
  // 自定义背景色
  buttonBg: { type: String, default: '#4975ac' },
  hoverBg: { type: String, default: '#4dbfff' },
  fontColor: { type: String, default: '#F1F6F8' },
  hoverFontColor: { type: String, default: '#F1F6F8' },
  // 尺寸
  width: { type: String, default: 'auto' },
  height: { type: String, default: 'auto' },
  // 图标相关
  iconName: { type: String, default: '' },
  iconSize: { type: String, default: '18px' },
  iconWidth: { type: String, default: '' },
  iconHeight: { type: String, default: '' },
  hoverIconName: { type: String, default: '' },
  hoverIconSize: { type: String, default: '20px' },
  hoverIconWidth: { type: String, default: '' },
  hoverIconHeight: { type: String, default: '' },
  // 同时允许通过 icon 传入 VNode，但若 iconName 存在，则优先使用 iconName
  icon: { type: [String, Object], default: null },
  hoverIcon: { type: [String, Object], default: null },
  // 间隔
  gap: { type: String, default: '8px' },
  // 圆角
  borderRadius: { type: String, default: '20px' },
  // 覆盖样式
  overrideStyle: { type: Boolean, default: true },
  // 禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 加载中状态
  loading: {
    type: Boolean,
    default: false
  },
  // 字体大小
  fontSize: {
    type: String,
    default: '16px'
  }
});

// 允许父组件传入的属性都转发给 a-button
// 使用 $attrs 获取 a-button 以外的所有传入属性

const emit = defineEmits(['click']);

const isHovered = ref(false);

function handleMouseEnter() {
  isHovered.value = true;
}
function handleMouseLeave() {
  isHovered.value = false;
}
function handleClick(e) {
  emit('click', e);
}

const computedIcon = computed(() => {
  if (isHovered.value && props.hoverIconName) {
    if (props.hoverIconWidth && props.hoverIconHeight) {
      return h(SvgIcon, { iconClass: props.hoverIconName, width: props.hoverIconWidth, height: props.hoverIconHeight });
    } else {
      return h(SvgIcon, { iconClass: props.hoverIconName, size: props.hoverIconSize });
    }
  } else if (props.iconName) {
    if (props.iconWidth && props.iconHeight) {
      return h(SvgIcon, { iconClass: props.iconName, width: props.iconWidth, height: props.iconHeight });
    } else {
      return h(SvgIcon, { iconClass: props.iconName, size: props.iconSize });
    }
  } else if (props.icon) {
    return props.icon;
  } else {
    return null;
  }
});

const customStyle = computed(() => {
  if (!props.overrideStyle) return {};
  return {
    background: isHovered.value ? `${props.hoverBg}` : `${props.buttonBg}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: props.gap,
    width: props.width,
    height: props.height,
    borderRadius: props.borderRadius,
    border: 'none',
    color: isHovered.value ? props.hoverFontColor : props.fontColor,
    fontSize: props.fontSize
  };
});

// tooltip 显示逻辑
const tooltipVisible = computed(() => {
  return props.manualTooltip ? props.showTooltip : isHovered.value;
});

const overlayStyle = computed(() => {
  return { color: props.tooltipFontColor }
})
</script>

<style>
.custom-override {}
</style>