<!--
 * @Author: yuz<PERSON><PERSON><PERSON>
 * @Date: 2025-07-24 10:39:04
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 14:55:27
 * @FilePath: \platform-face-web\src\components\Business\TagSelector\index.vue
 * @Description: TagSelector 标签选择器组件
 * 支持搜索、添加、删除标签的通用组件
 *
-->
<template>
  <div class="tag-selector" :class="{ 'tag-selector--disabled': disabled }">
    <!-- 输入框区域 -->
    <div class="tag-selector__input-wrapper">
      <TagInput ref="inputRef" :search-text="searchText" :placeholder="placeholder" :disabled="disabled"
        :loading="loading" @input="handleInput" @add-tag="handleAddTag" @focus="handleInputFocus"
        @blur="handleInputBlur" />

      <!-- 建议下拉框 -->
      <SuggestionList :suggestions="suggestions" :show-suggestions="showSuggestions"
        :active-suggestion-index="activeSuggestionIndex" :search-text="searchText" :loading="loading"
        @select-suggestion="handleSelectSuggestion" @mouse-enter="handleMouseEnter" />
    </div>

    <!-- 已选标签区域 -->
    <div v-if="modelValue.length > 0">
      <a-tag :color="tagType" v-for="tag in modelValue" :key="tag.key || tag.id" :closable="!disabled"
        @close="handleRemoveTag(tag)">
        {{ tag.name }}
      </a-tag>

      <!-- 标签数量限制提示 -->
      <div v-if="maxTags && modelValue.length >= maxTags" class="tag-selector__limit-tip">
        已达到最大标签数量限制 ({{ maxTags }})
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import TagInput from './components/TagInput.vue';
import SuggestionList from './components/SuggestionList.vue';
import { useTagOperations } from './composables/useTagOperations.js';
import {
  DEFAULT_CONFIG,
  validateTagArray,
  validateFunction,
  validateTagType,
  validateNonNegativeNumber
} from './utils/index.js';

// Props 定义
const props = defineProps({
  // v-model 绑定的标签数组
  modelValue: {
    type: Array,
    default: () => [],
    validator: validateTagArray
  },

  // 搜索标签的API函数
  onSearch: {
    type: Function,
    required: true,
    validator: validateFunction
  },

  // 创建新标签的API函数
  onCreate: {
    type: Function,
    default: null,
    validator: (value) => value === null || validateFunction(value)
  },

  // 输入框占位符
  placeholder: {
    type: String,
    default: DEFAULT_CONFIG.placeholder
  },

  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },

  // 最大标签数量
  maxTags: {
    type: Number,
    default: DEFAULT_CONFIG.maxTags,
    validator: validateNonNegativeNumber
  },

  // 防抖延迟时间（毫秒）
  debounceDelay: {
    type: Number,
    default: DEFAULT_CONFIG.debounceDelay,
    validator: validateNonNegativeNumber
  },

  // 标签样式类型
  tagType: {
    type: String,
    default: DEFAULT_CONFIG.tagType,
    validator: validateTagType
  },

  // 是否允许创建新标签
  allowCreate: {
    type: Boolean,
    default: DEFAULT_CONFIG.allowCreate
  },

  // 标签去重字段
  uniqueKey: {
    type: String,
    default: DEFAULT_CONFIG.uniqueKey
  }
});

// 事件定义
const emit = defineEmits([
  'update:modelValue',
  'search',
  'create',
  'remove',
  'change'
]);

// 使用标签操作逻辑
const {
  searchText,
  suggestions,
  loading,
  showSuggestions,
  activeSuggestionIndex,
  handleInput,
  handleInputFocus,
  handleInputBlur,
  handleSelectSuggestion,
  handleAddTag,
  handleRemoveTag,
  handleKeydown,
  clearInput,
  cleanup
} = useTagOperations(props, emit);

// 输入框引用
const inputRef = ref(null);

// 处理鼠标进入建议项
const handleMouseEnter = (index) => {
  activeSuggestionIndex.value = index;
};

// 生命周期
onMounted(() => {
  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  // 清理事件监听
  document.removeEventListener('keydown', handleKeydown);
  // 清理组合式函数
  cleanup();
});

// 暴露给父组件的方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  clear: clearInput
});
</script>

<style lang="scss" scoped>
.tag-selector {
  position: relative;
  width: 100%;

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &__input-wrapper {
    position: relative;
    width: 100%;
  }

  &__limit-tip {
    font-size: 12px;
    color: #faad14;
    padding: 4px 8px;
    background: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 4px;
    margin-top: 8px;
  }
}
</style>
