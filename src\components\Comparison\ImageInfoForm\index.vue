<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-24 15:11:22
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 15:24:46
 * @FilePath: \platform-face-web\src\components\Comparison\ImageInfoForm\index.vue
 * @Description: 图像信息表单组件
-->
<template>
  <div class="d-flex">
    <div class="image-info-form">
      <a-form ref="formRef" :model="formData" :colon="false" :label-col="{ style: { width: '80px' } }">
        <div class="w-100 d-flex g-2">
          <div class="w-100 d-flex flex-column g-2">
            <div v-if="infoDictionaries" class="form-section">
              <a-form-item label="获取时间" name="captureTime">
                <a-date-picker v-model:value="formData.captureTime" class="form-wrapper" value-format='YYYY-MM-DD' />
              </a-form-item>

              <a-form-item label="获取方式" name="obtainMethod">
                <a-select v-model:value="formData.obtainMethod.dictionaryItemId" placeholder="请选择获取方式"
                  class="form-wrapper">
                  <a-select-option v-for="(data, index) in infoDictionaries['ACQUISITION_METHOD']" :key="index"
                    :value="data.id">{{
                      data.label }}</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="照片来源" name="source">
                <a-select v-model:value="formData.source.dictionaryItemId" placeholder="请选择照片来源" class="form-wrapper">
                  <a-select-option v-for="(data, index) in infoDictionaries['PHOTO_SOURCE']" :key="index"
                    :value="data.id">{{
                      data.label }}</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="获取途径" name="obtainWay">
                <a-select v-model:value="formData.obtainWay.dictionaryItemId" placeholder="请选择获取途径"
                  class="form-wrapper">
                  <a-select-option v-for="(data, index) in infoDictionaries['ACQUISITION_CHANNEL']" :key="index"
                    :value="data.id">{{
                      data.label }}</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="照片类型" name="type">
                <a-select v-model:value="formData.type.dictionaryItemId" placeholder="请选择照片类型" class="form-wrapper">
                  <a-select-option v-for="(data, index) in infoDictionaries['PHOTO_TYPE']" :key="index"
                    :value="data.id">{{
                      data.label }}</a-select-option>
                </a-select>
              </a-form-item>
            </div>

            <div v-if="infoDictionaries" class="form-section">
              <a-form-item label="图片归属" name="obtainScene">
                <a-select v-model:value="formData.obtainScene.dictionaryItemId" placeholder="请选择图片归属"
                  class="form-wrapper">
                  <a-select-option v-for="(data, index) in infoDictionaries['IMAGE_OWNERSHIP']" :key="index"
                    :value="data.id">{{
                      data.label }}</a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="备注" name="description">
                <a-input v-model:value="formData.description" placeholder="请输入备注" />
              </a-form-item>
            </div>
          </div>
        </div>
      </a-form>
    </div>
    <div class="image-metadata">
      <div class="image-metadata-content">
        <!-- TODO:图像信息 -->
      </div>
    </div>
  </div>
  <div class="form-footer">
    <a-form-item>
      <CustomButtonWithTooltip width="220px" height="34px" @click="handleSubmit">
        保存编辑
      </CustomButtonWithTooltip>
    </a-form-item>
  </div>

</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { getDictionary } from '@/api/dictionary.js';
import { cloneDeep } from 'lodash';

import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';

const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({})
  },
  materialId: {
    type: String,
    default: ''
  }
});

// 表单引用
const formRef = ref(null);
const infoDictionaries = ref(null);//字典数据

const emits = defineEmits('submit-success');

// 表单数据
const formData = ref({
  captureTime: '',        // [开始时间, 结束时间]
  obtainMethod: {
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },      // 获取方式
  source: {
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },        // 照片来源
  obtainWay: {
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },        // 获取途径
  type: {
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },          // 照片类型
  obtainScene: {
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  },   // 图片归属
  description: ''              // 备注
});

// 回显数据函数
const setFormData = (data) => {
  const item = {
    businessId: "",
    businessValue: "",
    dictionaryId: null,
    dictionaryItemId: null,
  }
  formData.value = {
    captureTime: data.captureTime || '',
    obtainMethod: data.obtainMethod || cloneDeep(item),
    source: data.source || cloneDeep(item),
    obtainWay: data.obtainWay || cloneDeep(item),
    type: data.type || cloneDeep(item),
    obtainScene: data.obtainScene || cloneDeep(item),
    description: data.description || ''
  };
};

// 组件初始化或 props 变化时，将数据写入 formData
watch(
  () => props.initialData,
  (newVal) => {
    setFormData(newVal);
  },
  { deep: true, immediate: true }
);

const verifyObj = (obj, dictName) => {
  if (!obj) return null
  if (obj.dictionaryItemId) {
    obj.businessId = props.materialId;
    obj.dictionaryId = infoDictionaries.value[dictName][0].dictionaryId;
    return obj
  }
  return null
}

// 点击“保存编辑”时的处理逻辑
const handleSubmit = async () => {
  try {
    // 如果需要校验，可在此处使用 formRef.value.validate()
    // await formRef.value.validate();
    const formDataCopy = cloneDeep(formData.value)
    formDataCopy.obtainMethod = verifyObj(formDataCopy.obtainMethod, 'ACQUISITION_METHOD')
    formDataCopy.source = verifyObj(formDataCopy.source, 'PHOTO_SOURCE')
    formDataCopy.obtainWay = verifyObj(formDataCopy.obtainWay, 'ACQUISITION_CHANNEL')
    formDataCopy.type = verifyObj(formDataCopy.type, 'PHOTO_TYPE')
    formDataCopy.obtainScene = verifyObj(formDataCopy.obtainScene, 'IMAGE_OWNERSHIP')
    // console.log('提交的表单数据:', formDataCopy);
    emits('submit-success', formDataCopy);
  } catch (error) {
    console.error('提交出错:', error);
    message.error('提交失败，请检查表单信息');
  }
};

onMounted(() => {
  getDictionary('	ACQUISITION_METHOD,PHOTO_SOURCE,ACQUISITION_CHANNEL,PHOTO_TYPE,	IMAGE_OWNERSHIP').then(data => {
    infoDictionaries.value = data;
  })
})
</script>

<style scoped>
.image-info-form {
  display: flex;
  flex: 1;
  background: #1f242b;
  padding: 16px;
  border-radius: 8px;
  color: #fff;
}

.form-section {
  border: 1px solid #3C4554;
  border-radius: 8px;
  padding: 16px;
}

.form-wrapper {
  width: 300px;
}

.form-footer {
  align-items: center;
  display: flex;
  justify-content: center;
  margin-top: 24px;
  width: 100%;
}

.image-metadata {
  padding: 16px 0;
  border-radius: 8px;
  color: #fff;
}

.image-metadata-content {
  width: 240px;
  height: 100%;
  border: 1px solid #3C4554;
  border-radius: 8px;
  padding: 16px;
}
</style>