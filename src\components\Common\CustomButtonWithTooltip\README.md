# CustomButtonWithTooltip 自定义按钮组件

## 组件描述

CustomButtonWithTooltip 是一个高度可定制的按钮组件，支持提示信息显示、图标配置、样式自定义等功能。基于 Ant Design Vue 的 `a-button` 和 `a-tooltip` 组件封装，提供了丰富的配置选项以满足各种业务场景需求。

## 功能特性

- ✅ **提示信息支持**：支持 hover 显示提示信息，可自定义提示样式
- ✅ **图标支持**：支持普通图标和 hover 状态图标，可配置图标尺寸
- ✅ **样式自定义**：支持背景色、字体颜色、尺寸、圆角等样式自定义
- ✅ **状态管理**：支持禁用、加载中等状态
- ✅ **智能提示**：disabled 状态下自动禁用 tooltip，避免样式冲突
- ✅ **手动控制**：支持手动控制提示信息的显示/隐藏
- ✅ **完全兼容**：完全兼容 Ant Design Vue Button 的所有属性和事件

## 使用场景

- 需要显示操作提示的按钮
- 需要自定义样式的按钮
- 需要图标支持的按钮
- 需要特殊交互效果的按钮

## Props 参数

### 提示相关

| 参数名           | 类型    | 默认值    | 说明                                      |
| ---------------- | ------- | --------- | ----------------------------------------- |
| tooltip          | String  | ''        | 提示文本内容，为空时不显示提示            |
| manualTooltip    | Boolean | false     | 是否手动控制提示显示                      |
| showTooltip      | Boolean | false     | 配合 manualTooltip 使用，控制提示显示状态 |
| tooltipColor     | String  | '#D8E9FE' | 提示背景颜色                              |
| tooltipFontColor | String  | '#16171C' | 提示文字颜色                              |

### 样式相关

| 参数名         | 类型   | 默认值    | 说明               |
| -------------- | ------ | --------- | ------------------ |
| buttonBg       | String | '#4975ac' | 按钮背景色         |
| hoverBg        | String | '#4dbfff' | 鼠标悬停时背景色   |
| fontColor      | String | '#F1F6F8' | 字体颜色           |
| hoverFontColor | String | '#F1F6F8' | 鼠标悬停时字体颜色 |
| width          | String | 'auto'    | 按钮宽度           |
| height         | String | 'auto'    | 按钮高度           |
| borderRadius   | String | '20px'    | 圆角大小           |
| gap            | String | '8px'     | 图标与文字间距     |

### 图标相关

| 参数名          | 类型          | 默认值 | 说明                                             |
| --------------- | ------------- | ------ | ------------------------------------------------ |
| iconName        | String        | ''     | 图标名称（SvgIcon 组件的 iconClass）             |
| iconSize        | String        | '18px' | 图标尺寸（当未设置 iconWidth/iconHeight 时使用） |
| iconWidth       | String        | ''     | 图标宽度                                         |
| iconHeight      | String        | ''     | 图标高度                                         |
| hoverIconName   | String        | ''     | 鼠标悬停时的图标名称                             |
| hoverIconSize   | String        | '20px' | 悬停图标尺寸                                     |
| hoverIconWidth  | String        | ''     | 悬停图标宽度                                     |
| hoverIconHeight | String        | ''     | 悬停图标高度                                     |
| icon            | String/Object | null   | 直接传入的图标（VNode 或字符串）                 |
| hoverIcon       | String/Object | null   | 悬停时的图标（VNode 或字符串）                   |

### 其他配置

| 参数名        | 类型    | 默认值 | 说明                                 |
| ------------- | ------- | ------ | ------------------------------------ |
| overrideStyle | Boolean | true   | 是否覆盖默认样式，true 时覆盖        |
| disabled      | Boolean | false  | 是否禁用按钮，禁用时自动禁用 tooltip |
| loading       | Boolean | false  | 是否显示加载状态                     |
| fontSize      | String  | '16px' | 字体大小                             |

## Events 事件

| 事件名 | 参数  | 说明         |
| ------ | ----- | ------------ |
| click  | event | 按钮点击事件 |

## 使用示例

### 基础用法

```vue
<template>
  <CustomButtonWithTooltip tooltip="这是一个基础按钮"> 基础按钮 </CustomButtonWithTooltip>
</template>

<script setup>
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';
</script>
```

### 带图标的按钮

```vue
<template>
  <CustomButtonWithTooltip tooltip="保存文件" iconName="save" iconSize="16px" @click="handleSave"> 保存 </CustomButtonWithTooltip>
</template>
```

### 自定义样式

```vue
<template>
  <CustomButtonWithTooltip
    tooltip="自定义样式按钮"
    buttonBg="linear-gradient(45deg, #ff6b6b, #4ecdc4)"
    hoverBg="linear-gradient(45deg, #4ecdc4, #ff6b6b)"
    fontColor="#ffffff"
    borderRadius="12px"
    width="120px"
    height="40px">
    自定义按钮
  </CustomButtonWithTooltip>
</template>
```

### 悬停图标切换

```vue
<template>
  <CustomButtonWithTooltip tooltip="点击收藏" iconName="star-outline" hoverIconName="star-filled" iconSize="18px" hoverIconSize="20px">
    收藏
  </CustomButtonWithTooltip>
</template>
```

### 手动控制提示

```vue
<template>
  <CustomButtonWithTooltip tooltip="手动控制的提示" :manualTooltip="true" :showTooltip="showTip" @click="toggleTooltip">
    点击控制提示
  </CustomButtonWithTooltip>
</template>

<script setup>
import { ref } from 'vue';

const showTip = ref(false);

const toggleTooltip = () => {
  showTip.value = !showTip.value;
};
</script>
```

### 禁用状态

```vue
<template>
  <!-- disabled 为 true 时，tooltip 会自动禁用，避免样式冲突 -->
  <CustomButtonWithTooltip tooltip="这个提示不会显示" :disabled="true"> 禁用按钮 </CustomButtonWithTooltip>
</template>
```

### 加载状态

```vue
<template>
  <CustomButtonWithTooltip tooltip="正在处理中..." :loading="isLoading" @click="handleSubmit"> 提交 </CustomButtonWithTooltip>
</template>

<script setup>
import { ref } from 'vue';

const isLoading = ref(false);

const handleSubmit = async () => {
  isLoading.value = true;
  try {
    // 处理提交逻辑
    await submitData();
  } finally {
    isLoading.value = false;
  }
};
</script>
```

## 注意事项

1. **图标优先级**：如果同时设置了 `iconName` 和 `icon`，优先使用 `iconName`
2. **禁用状态**：当 `disabled` 为 `true` 时，组件会自动禁用 tooltip 显示，避免样式冲突
3. **样式覆盖**：`overrideStyle` 为 `true` 时使用自定义样式，为 `false` 时使用 Ant Design 原生样式
4. **事件透传**：组件支持 Ant Design Button 的所有原生事件和属性
5. **图标组件**：图标功能依赖项目中的 `SvgIcon` 组件

## 版本信息

- **作者**: yuzhouisme
- **版本**: 1.0.0
- **最后修改**: 2025/07/29
- **依赖**: Vue 3.x, Ant Design Vue 4.x

## 更新日志

### v1.0.1 (2025/07/29)

- 新增 `fontSize` prop，支持自定义字体大小
- 修正 `overrideStyle` prop 的说明
- 修正 `fontColor` 和 `hoverFontColor` prop 的默认值

### v1.0.0 (2025/03/25)

- 初始版本发布
- 支持基础按钮功能
- 支持 tooltip 显示
- 支持图标配置
- 支持样式自定义
- 新增 disabled 状态下自动禁用 tooltip 功能
