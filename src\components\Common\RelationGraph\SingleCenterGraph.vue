<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-18 10:11:17
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-21 14:54:48
 * @FilePath: \platform-face-web\src\components\Common\RelationGraph\SingleCenterGraph.vue
 * @Description: 单节点的关系图谱组件
-->
<template>
    <div ref="container" class="single-center-graph"></div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue';
import * as d3 from 'd3';
import { layoutNodesStrict, preprocessLinks } from './utils/layout/index';
import { renderSingleCenterGraph, addZoomBehavior, addResetButton } from './utils/render';
import ConfigManager from './utils/ConfigManager';
import { defaultTooltipManager } from './utils/VueTooltipManager';

const props = defineProps({
    data: { type: Object, required: true },
    // 新的配置系统（可选，如果提供则优先使用）
    config: {
        type: Object,
        default: null
    }
});

const container = ref(null);
const svgRef = ref(null);
const width = ref(0);
const height = ref(0);
const margin = { top: 20, right: 30, bottom: 40, left: 50 };

onMounted(() => {
    nextTick(() => {
        if (container.value) {
            width.value = container.value.clientWidth;
            height.value = container.value.clientHeight;
            initSVG();
            renderGraph(props.data);
        }
    })
});

watch(() => props.data, (newData) => {
    renderGraph(newData);
});

// 组件销毁时清理tooltip资源
onBeforeUnmount(() => {
    defaultTooltipManager.cleanup();
});

function initSVG() {
    if (!container.value) return;

    // 清除旧的SVG
    if (svgRef.value) {
        container.value.removeChild(svgRef.value);
    }

    // 创建新的SVG和主容器group（用于居中）
    const svg = d3.create("svg")
        .attr("width", width.value)
        .attr("height", height.value)
        .attr("viewBox", [0, 0, width.value, height.value]);

    // 添加主容器group用于整体居中
    const mainGroup = svg.append("g")
        .attr("class", "main-group")
        .attr("transform", `translate(${margin.left},${margin.top})`);;

    container.value.appendChild(svg.node());
    svgRef.value = svg.node();

    const zoom = addZoomBehavior(svg, mainGroup)
    // TODO:暂时隐藏，后续加上的时候应该同时加上关闭vue的时候删掉按钮
    // addResetButton(svg, zoom)
}

async function renderGraph(data) {
    if (!svgRef.value || !data?.nodes) return;

    // 预处理数据
    const { nodeLinkMap, linkKeyArr } = preprocessLinks(data.links);

    // 创建配置管理器
    let configManager;
    if (props.config) {
        // 如果提供了新的配置格式，直接使用
        configManager = new ConfigManager(props.config);
    } else {
        // 如果没有传入config，使用默认配置
        configManager = new ConfigManager();
    }

    // 计算布局 - 传递螺旋布局配置
    const layoutOptions = {
        width: width.value,
        height: height.value
    };

    // 传递螺旋布局配置（现在总是有configManager）
    const spiralConfig = configManager.get('layout.spiral');
    if (spiralConfig) {
        layoutOptions.spiralNodeCount = spiralConfig.spiralNodeCount;
        layoutOptions.innerRadius = spiralConfig.innerRadius;
        layoutOptions.outerRadius = spiralConfig.outerRadius;
        layoutOptions.sideMinRadius = spiralConfig.sideMinRadius;
        layoutOptions.sideMaxRadius = spiralConfig.sideMaxRadius;
        layoutOptions.baseAngleStep = spiralConfig.baseAngleStep;
        layoutOptions.innerRanks = spiralConfig.innerRanks;
        layoutOptions.outerStartRank = spiralConfig.outerStartRank;
        layoutOptions.minGap = spiralConfig.minGap;
    }

    const nodes = await layoutNodesStrict(data.nodes, layoutOptions);

    // 渲染图形 - 直接传递配置管理器
    renderSingleCenterGraph({
        svg: svgRef.value,
        nodes,
        linkKeyArr,
        nodeLinkMap,
        configManager // 直接传递配置管理器
    });
}


</script>

<style>
.relation-tooltip {
    position: absolute;
    pointer-events: none;
    top: 300px;
}
</style>

<style lang="scss" scoped>
.single-center-graph {
    width: 100%;
    height: 100%;
}
</style>