# FaceBoxEditor 人脸框编辑器组件

## 📖 组件概述

FaceBoxEditor 是一个高级人脸框编辑器组件，基于 FaceCanvas 组件构建，提供了完整的人脸框编辑工作流，包括绘制确认、验证弹窗等功能。主要用于人脸标注和编辑的业务场景。

## ✨ 主要功能

- 🎯 **人脸框编辑**：集成 FaceCanvas 的所有功能
- ✅ **绘制确认**：新绘制人脸框需要用户确认
- 🖼️ **预览功能**：确认弹窗中显示裁剪后的人脸图像
- 🔄 **状态管理**：管理编辑过程中的各种状态
- 🎨 **自定义样式**：专用的编辑器背景和样式
- 📱 **响应式布局**：适配不同屏幕尺寸

## 🏗️ 组件架构

```
FaceBoxEditor/
├── index.vue          # 主组件
└── README.md          # 文档
```

### 依赖组件
- `FaceCanvas` - 核心画布组件
- `CustomButtonWithTooltip` - 自定义按钮组件

## 📋 Props 属性

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `imageSrc` | String | ✅ | - | 图像源地址 |
| `faceBoxes` | Array | ❌ | `[]` | 自动检测的人脸框数组 |
| `manualFaceBoxes` | Array | ❌ | `[]` | 手动绘制的人脸框数组 |
| `selectedItems` | Array | ❌ | `[]` | 当前选中的人脸框数组 |
| `editable` | Boolean | ❌ | `true` | 是否允许编辑 |

### 数据结构
继承 FaceCanvas 组件的所有数据结构规范。

## 🎪 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `boxDrawn` | `{ face, croppedImage }` | 用户确认添加人脸框后触发 |
| `boxSelected` | `key` | 选择人脸框时触发 |

### 事件流程
1. 用户在画布上绘制人脸框
2. 显示确认弹窗，展示裁剪后的人脸图像
3. 用户点击"确认"后触发 `boxDrawn` 事件
4. 用户点击"取消"则丢弃绘制结果

## 🎨 样式系统

### 主要样式类
- `.face-box-editor` - 编辑器容器
- `.confirmation-overlay` - 确认弹窗遮罩
- `.confirmation-dialog` - 确认弹窗主体
- `.face-preview` - 人脸预览图像

### 背景样式
使用专用的人脸编辑背景图：`/src/assets/svg/common/face_bg.svg`

## 🚀 使用示例

### 基础用法
```vue
<template>
  <FaceBoxEditor
    :image-src="imageUrl"
    :face-boxes="detectedFaces"
    :manual-face-boxes="manualFaces"
    :selected-items="selectedFaces"
    @box-drawn="handleBoxDrawn"
    @box-selected="handleBoxSelected"
  />
</template>

<script setup>
import FaceBoxEditor from '@/components/Common/FaceBoxEditor/index.vue';

const imageUrl = ref('path/to/image.jpg');
const detectedFaces = ref([
  { x: 100, y: 100, width: 80, height: 80, key: 'face1' }
]);
const manualFaces = ref([]);
const selectedFaces = ref([]);

const handleBoxDrawn = ({ face, croppedImage }) => {
  // 用户确认添加新的人脸框
  manualFaces.value.push({
    ...face,
    key: `manual_${Date.now()}`,
    manualSelection: true
  });
  
  console.log('新增人脸框:', face);
  console.log('人脸图像:', croppedImage);
};

const handleBoxSelected = (key) => {
  // 处理人脸框选择
  const index = selectedFaces.value.findIndex(f => f.key === key);
  if (index > -1) {
    selectedFaces.value.splice(index, 1);
  } else {
    const face = [...detectedFaces.value, ...manualFaces.value]
      .find(f => f.key === key);
    if (face) {
      selectedFaces.value.push(face);
    }
  }
};
</script>
```

### 只读模式
```vue
<FaceBoxEditor
  :image-src="imageUrl"
  :face-boxes="faces"
  :editable="false"
/>
```

### 完整的编辑工作流
```vue
<template>
  <div class="face-editor-container">
    <FaceBoxEditor
      :image-src="currentImage"
      :face-boxes="detectedFaces"
      :manual-face-boxes="manualFaces"
      :selected-items="selectedFaces"
      @box-drawn="addManualFace"
      @box-selected="toggleFaceSelection"
    />
    
    <div class="editor-controls">
      <button @click="saveChanges">保存修改</button>
      <button @click="resetEditor">重置</button>
    </div>
  </div>
</template>

<script setup>
const currentImage = ref('');
const detectedFaces = ref([]);
const manualFaces = ref([]);
const selectedFaces = ref([]);

const addManualFace = ({ face, croppedImage }) => {
  const newFace = {
    ...face,
    key: `manual_${Date.now()}`,
    manualSelection: true,
    croppedImage
  };
  
  manualFaces.value.push(newFace);
  
  // 可以在这里上传裁剪后的人脸图像
  uploadFaceImage(croppedImage);
};

const toggleFaceSelection = (key) => {
  // 切换选择状态的逻辑
};

const saveChanges = () => {
  // 保存所有修改
  const allFaces = [...detectedFaces.value, ...manualFaces.value];
  console.log('保存人脸框数据:', allFaces);
};

const resetEditor = () => {
  manualFaces.value = [];
  selectedFaces.value = [];
};
</script>
```

## 🎮 用户交互流程

### 绘制确认流程
1. 用户在画布上拖拽绘制人脸框
2. 松开鼠标后，显示确认弹窗
3. 弹窗中显示裁剪后的人脸图像预览
4. 用户可以选择"确认"或"取消"
5. 确认后触发 `boxDrawn` 事件，取消则丢弃结果

### 选择操作
- 点击已有人脸框可以选择/取消选择
- 选中的人脸框会有特殊的视觉样式
- 触发 `boxSelected` 事件通知父组件

## ⚠️ 注意事项

1. **确认机制**：所有新绘制的人脸框都需要用户确认
2. **图像处理**：确认弹窗会显示裁剪后的人脸图像
3. **状态管理**：组件内部管理确认弹窗的显示状态
4. **事件传递**：只有确认后才会触发 `boxDrawn` 事件
5. **样式继承**：继承 FaceCanvas 的所有样式规范

## 🔧 开发指南

### 自定义确认弹窗
修改 `confirmation-dialog` 相关样式和结构：
```vue
<div class="confirmation-dialog">
  <p>确认添加此人脸框吗？</p>
  <img :src="pendingFaceImage" alt="face preview" />
  <div class="dialog-buttons">
    <button @click="handleCancelFace">取消</button>
    <button @click="handleConfirmFace">确认</button>
  </div>
</div>
```

### 添加验证逻辑
在 `onBoxDrawn` 方法中添加自定义验证：
```javascript
const onBoxDrawn = ({ face, croppedImage }) => {
  // 添加自定义验证逻辑
  if (face.width < 50 || face.height < 50) {
    console.warn('人脸框尺寸过小');
    return;
  }
  
  // 显示确认弹窗
  pendingFace.value = face;
  pendingFaceImage.value = croppedImage;
  showConfirmation.value = true;
};
```

## 🎯 最佳实践

1. **数据管理**：建议使用 Vuex 或 Pinia 管理人脸框数据
2. **图像优化**：大图像建议先压缩再传入组件
3. **错误处理**：添加图像加载失败的错误处理
4. **用户体验**：提供加载状态和操作反馈
5. **数据持久化**：及时保存用户的编辑结果

## 📝 更新日志

- **v1.2.0** - 添加确认弹窗功能，优化用户体验
- **v1.1.0** - 集成 FaceCanvas 组件，支持完整编辑流程
- **v1.0.0** - 初始版本，基础编辑器功能
