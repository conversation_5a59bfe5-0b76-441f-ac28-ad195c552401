/**
 * GalleryFilterDropdown 组件的默认数据示例
 * 这些数据可以作为参考，展示组件支持的数据结构
 */

// 图册筛选的默认数据结构
export const galleryFilterOptions = [
    {
        name: '人物类别',
        items: [
            {
                label: '所有涉案人员',
                value: 'all-involved',
                number: 12,
                children: [
                    { iconName: 'filter-processing', label: '关键人物', value: 'key-involved' },
                    { iconName: 'filter-processing', label: '一般涉案人', value: 'general-involved' },
                    { iconName: 'filter-processing', label: '关系人', value: 'mass-involved', number: 12 }
                ]
            },
            {
                label: '相关人员',
                value: 'related-person',
            }
        ]
    },
    {
        name: '比对情况',
        items: [
            {
                label: '已比对',
                value: 'compared',
                children: [
                    {
                        iconName: 'filter-processing',
                        label: '已匹配目标',
                        value: 'matched-targets',
                        children: [
                            { iconName: 'filter-processing', label: '人工校验', value: 'manual-verification' },
                            { iconName: 'filter-processing', label: '人工存疑', value: 'manual-doubt' },
                            { iconName: 'filter-processing', label: '提交核查', value: 'submit-check' },
                            { iconName: 'filter-processing', label: '核查归档', value: 'check-archive' }
                        ]
                    },
                    {
                        iconName: 'filter-processing',
                        label: '未匹配目标',
                        value: 'unmatched-targets',
                    },
                ]
            }
        ]
    },
    {
        name: '人物信息',
        items: [
            { label: '人工录入关键信息', value: 'manual-key-info' },
            { label: '人工录入图片信息', value: 'manual-image-info' }
        ]
    }
]
