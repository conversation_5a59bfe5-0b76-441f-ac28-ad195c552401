# VideoPlayer 视频播放器组件

一个功能完整的视频播放器组件，支持弹窗展示播放视频，具备多视频切换、缩略图预览等功能。

## 功能特性

- 🎬 **视频播放**: 支持多种视频格式的播放
- 🔄 **多视频切换**: 支持多个视频文件的切换播放
- 🖼️ **缩略图预览**: 底部显示所有视频的缩略图，点击切换
- 🎮 **导航控制**: 左右箭头按钮快速切换视频
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **美观界面**: 现代化的UI设计，支持加载状态和错误处理

## 基础用法

### 单视频播放

```vue
<template>
  <VideoPlayer 
    v-model:visible="showVideo" 
    :video-src="videoUrl"
    :video-info="{ title: '示例视频' }"
  />
</template>

<script setup>
import { ref } from 'vue'

const showVideo = ref(false)
const videoUrl = ref('https://example.com/video.mp4')
</script>
```

### 多视频切换

```vue
<template>
  <VideoPlayer 
    v-model:visible="showVideo" 
    :videos="videoList"
    :initial-index="0"
  />
</template>

<script setup>
import { ref } from 'vue'

const showVideo = ref(false)
const videoList = ref([
  {
    url: 'https://example.com/video1.mp4',
    title: '视频1',
    size: 1024000,
    type: 'video/mp4'
  },
  {
    url: 'https://example.com/video2.mp4',
    title: '视频2',
    size: 2048000,
    type: 'video/mp4'
  }
])
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| `visible` | `Boolean` | `false` | 是 | 是否显示模态框 |
| `videoSrc` | `String` | - | 否 | 单个视频源地址 |
| `videoInfo` | `Object` | `{}` | 否 | 单个视频信息 |
| `videos` | `Array` | `[]` | 否 | 视频列表数组 |
| `initialIndex` | `Number` | `0` | 否 | 初始显示的视频索引 |
| `showNavigation` | `Boolean` | `true` | 否 | 是否显示导航按钮 |
| `showThumbnails` | `Boolean` | `true` | 否 | 是否显示缩略图 |
| `maskClosable` | `Boolean` | `true` | 否 | 是否允许点击遮罩关闭 |

### videoInfo 对象结构

```javascript
{
  title: '视频标题',
  size: 1024000, // 文件大小（字节）
  type: 'video/mp4' // 文件类型
}
```

### videos 数组结构

```javascript
[
  {
    url: '视频地址',
    title: '视频标题',
    size: 1024000,
    type: 'video/mp4'
  }
]
```

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:visible` | `(visible: boolean)` | 模态框显示状态变化 |
| `play` | - | 视频开始播放 |
| `pause` | - | 视频暂停 |
| `ended` | - | 视频播放结束 |
| `error` | - | 视频加载错误 |

## 样式定制

组件使用 SCSS 编写样式，支持以下 CSS 变量定制：

```scss
:root {
  --spacing-small: 8px;
  --spacing-medium: 16px;
  --spacing-large: 24px;
  --border-radius-small: 4px;
  --border-radius-medium: 8px;
  --color-primary: #1890ff;
  --color-text: #ffffff;
  --color-text-secondary: #666666;
  --color-error: #ff4d4f;
  --font-size-medium: 14px;
}
```

## 使用示例

### 在 AiInputArea 组件中的使用

```vue
<template>
  <VideoPlayer 
    v-model:visible="showVideoPreview" 
    :videos="showVideoList"
    :initial-index="currentVideoIndex"
  />
</template>

<script setup>
import { ref, computed } from 'vue'

const showVideoPreview = ref(false)
const currentVideoIndex = ref(0)
const videoList = ref([])

const showVideoList = computed(() => {
  return videoList.value.map(item => ({
    url: item.url,
    title: item.name,
    size: item.size,
    type: item.type
  }))
})

function handleVideoClick(video) {
  const index = videoList.value.findIndex(item => item.id === video.id)
  currentVideoIndex.value = index >= 0 ? index : 0
  showVideoPreview.value = true
}
</script>
```

## 技术实现

### 核心功能

1. **视频加载管理**
   - 使用 `FileReader` 创建本地预览URL
   - 支持 `data:video/...` 格式的本地视频
   - 自动处理视频加载状态

2. **多视频切换**
   - 响应式数据管理当前视频索引
   - 计算属性自动更新当前视频源和信息
   - 支持循环切换

3. **用户体验优化**
   - 加载状态显示
   - 错误处理和重试机制
   - 平滑的过渡动画
   - 键盘导航支持

### 组件结构

```
VideoPlayer/
├── index.vue          # 主组件文件
├── README.md          # 文档说明
└── styles/            # 样式文件（可选）
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **视频格式支持**: 组件依赖浏览器原生视频播放能力，支持的格式取决于浏览器
2. **文件大小**: 大文件可能影响加载性能，建议进行适当压缩
3. **内存管理**: 组件会自动清理视频资源，避免内存泄漏
4. **移动端适配**: 组件已针对移动设备进行优化

## 更新日志

### v1.0.0 (2025-07-29)
- ✨ 初始版本发布
- 🎬 支持单视频播放
- 🔄 支持多视频切换
- 🖼️ 添加缩略图预览功能
- 🎮 添加导航控制按钮
- �� 响应式设计
- 🎨 现代化UI设计 