<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-17 10:10:45
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-17 10:29:41
 * @FilePath: \platform-face-web\src\components\Charts\ProgressItems\components\TriangleProgress.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="triangle-progress-item">
    <div class="progress-text">
      <div class="text-value">{{ (value * 100).toFixed(0) }}%</div>
      <div class="text-label" :class="{ 'text-label-active': value > 0 }">{{ label }}</div>
    </div>
    <svg :width="size" :height="size">
      <polygon
        :fill="bgColor"
        stroke-linejoin="round"
        :points="points"
        :stroke="trackColor"
        :stroke-width="strokeWidth"
        :transform="svgTransform"
      />
      <polygon
        v-if="value > 0 && value < 1"
        fill="none"
        stroke-linejoin="round"
        :points="points"
        :stroke="color"
        :stroke-width="strokeWidth"
        :stroke-dasharray="dasharray"
        stroke-linecap="round"
        :transform="svgTransform"
      />
      <polygon
        v-else-if="value >= 1"
        fill="none"
        stroke-linejoin="round"
        :points="points"
        :stroke="color"
        :stroke-width="strokeWidth"
        :transform="svgTransform"
      />
    </svg>
  </div>
</template>

<script setup>
import { computed } from 'vue';
/**
 * 三角形进度条组件
 * @property {number} value - 进度值，0~1
 * @property {string} label - 进度标签
 * @property {string} color - 进度条颜色
 * @property {string} trackColor - 轨道颜色
 * @property {string} bgColor - 底色
 * @property {number} size - 组件尺寸
 * @property {number} strokeWidth - 进度条宽度
 */
const props = defineProps({
  value: { type: Number, default: 0 }, // 0~1
  label: { type: String, default: '' },
  color: { type: String, default: '#4079AA' },
  trackColor: { type: String, default: '#2F3C50' },
  bgColor: { type: String, default: 'none' },
  size: { type: Number, default: 80 },
  strokeWidth: { type: Number, default: 8 },
});
// 三角形边长
const sideLength = computed(() => props.size * 0.79); // 80*0.79≈63
// 三角形高度
const height = computed(() => (Math.sqrt(3) / 2) * (sideLength.value + 5));
// 三角形顶点坐标
const points = computed(() => `
  ${sideLength.value / 2},0 
  ${sideLength.value},${height.value}
  0,${height.value}
`);
// 三角形周长
const perimeter = computed(() => 3 * sideLength.value);
// 进度条虚线数组
const dasharray = computed(() => `${props.value * perimeter.value} ${perimeter.value}`);
// SVG平移变换
const svgTransform = computed(() => `translate(${props.size * 0.125},${props.size * 0.13})`);
</script>

<style scoped>
.triangle-progress-item {
  width: 80px;
  height: 80px;
  position: relative;
}
.progress-text {
  width: max-content;
  position: absolute;
  top: calc(50% + 12px);
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 11px;
  text-align: center;
  color: #b0bbcc;
}
.text-label-active {
  color: #3792ce;
}
</style> 