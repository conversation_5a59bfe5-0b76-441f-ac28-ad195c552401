# FormArrayFieldGroup 数组字段组组件

## 📖 组件概述

FormArrayFieldGroup 是一个通用的数组字段组件，支持动态增删操作，适用于曾用名、别名、证件号、居住地等需要多条数据录入的场景。该组件通过插槽机制提供了极高的灵活性，可以适配各种不同的表单字段类型。

## ✨ 主要功能

- 🔄 **动态增删**：支持动态添加和删除数组项
- 🎯 **插槽机制**：通过插槽自定义每个数组项的内容
- 🔒 **最小限制**：支持设置最小项目数量，防止误删
- 🎨 **灵活配置**：支持自定义默认值和字段标识
- 📱 **响应式**：完全支持Vue 3的响应式系统
- 🎪 **双向绑定**：使用v-model进行数据绑定

## 🏗️ 组件结构

```
src/components/Common/FormArrayFieldGroup/
├── index.vue                    # 主组件 (124行)
└── README.md                    # 组件文档
```

## 📋 Props 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| label | String | - | ✅ | 字段标签，显示在表单项左侧 |
| modelValue | Array | - | ✅ | 双向绑定的数组值 |
| fieldKey | String | - | ✅ | 字段唯一标识，用于生成key |
| minItems | Number | 1 | ❌ | 最小项目数量，防止删除到空 |
| defaultValue | String/Object | '' | ❌ | 新增项的默认值 |

### Props 详细说明

#### label
- **类型**: `String`
- **必填**: 是
- **说明**: 表单项的标签文本，会显示在左侧

#### modelValue
- **类型**: `Array`
- **必填**: 是
- **说明**: 数组数据，支持v-model双向绑定

#### fieldKey
- **类型**: `String`
- **必填**: 是
- **说明**: 用于生成每个数组项的唯一key，格式为`${fieldKey}-${index}`

#### minItems
- **类型**: `Number`
- **默认值**: `1`
- **说明**: 最小项目数量，当数组长度等于此值时，删除按钮不显示

#### defaultValue
- **类型**: `String | Object`
- **默认值**: `''`
- **说明**: 点击"新增一条"时添加的默认值，可以是字符串或对象

## 🎪 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | `Array` | 数组值变化时触发，用于v-model双向绑定 |

## 🎨 插槽 Slots

### default 默认插槽

**作用域插槽参数**:
- `item`: 当前数组项的值
- `index`: 当前项的索引
- `updateItem`: 更新当前项的方法，签名为`(index, value) => void`

## 🚀 使用方式

### 1. 基础文本输入

```vue
<template>
  <!-- 曾用名输入 -->
  <FormArrayFieldGroup 
    label="曾用名" 
    v-model="formData.usedNames" 
    field-key="oldName" 
    default-value="">
    <template #default="{ item, index, updateItem }">
      <a-input 
        :value="item" 
        @update:value="updateItem(index, $event)" 
        placeholder="请填写" 
        class="form-wrapper" />
    </template>
  </FormArrayFieldGroup>
</template>

<script setup>
import { ref } from 'vue';
import FormArrayFieldGroup from '@/components/Common/FormArrayFieldGroup/index.vue';

const formData = ref({
  usedNames: [''] // 初始化一个空字符串
});
</script>
```

### 2. 复杂对象输入（证件信息）

```vue
<template>
  <!-- 证件号输入 -->
  <FormArrayFieldGroup 
    label="证件号" 
    v-model="formData.identifications" 
    field-key="certificate"
    :default-value="defaultCertificate">
    <template #default="{ item, index, updateItem }">
      <a-select 
        :value="item.dictionaryItemId" 
        @update:value="updateCertificateType(index, $event)"
        placeholder="证件类型" 
        class="form-wrapper">
        <a-select-option 
          v-for="option in certificateOptions" 
          :key="option.id"
          :value="option.id">
          {{ option.label }}
        </a-select-option>
      </a-select>
      <a-input 
        :value="item.businessValue" 
        @update:value="updateCertificateValue(index, $event)"
        placeholder="请输入证件号" 
        style="width: 50%; margin-left: 8px;" />
    </template>
  </FormArrayFieldGroup>
</template>

<script setup>
import { ref, computed } from 'vue';
import FormArrayFieldGroup from '@/components/Common/FormArrayFieldGroup/index.vue';

const formData = ref({
  identifications: [
    {
      businessId: "",
      businessValue: "",
      dictionaryId: null,
      dictionaryItemId: null,
    }
  ]
});

// 默认证件对象
const defaultCertificate = computed(() => ({
  businessId: "",
  businessValue: "",
  dictionaryId: null,
  dictionaryItemId: null,
}));

// 更新证件类型
const updateCertificateType = (index, value) => {
  formData.value.identifications[index].dictionaryItemId = value;
};

// 更新证件号码
const updateCertificateValue = (index, value) => {
  formData.value.identifications[index].businessValue = value;
};
</script>
```

### 3. 设置最小项目数量

```vue
<template>
  <!-- 至少保留2条记录 -->
  <FormArrayFieldGroup 
    label="联系方式" 
    v-model="formData.contacts" 
    field-key="contact"
    :min-items="2"
    default-value="">
    <template #default="{ item, index, updateItem }">
      <a-input 
        :value="item" 
        @update:value="updateItem(index, $event)" 
        placeholder="请输入联系方式" />
    </template>
  </FormArrayFieldGroup>
</template>
```

## 🎯 核心方法

### addItem()
添加新的数组项到末尾。

```javascript
const addItem = () => {
  const newArray = [...props.modelValue, props.defaultValue];
  emit('update:modelValue', newArray);
};
```

### removeItem(index)
删除指定索引的数组项。

```javascript
const removeItem = (index) => {
  const newArray = props.modelValue.filter((_, i) => i !== index);
  emit('update:modelValue', newArray);
};
```

### updateItem(index, value)
更新指定索引的数组项值。

```javascript
const updateItem = (index, value) => {
  const newArray = [...props.modelValue];
  newArray[index] = value;
  emit('update:modelValue', newArray);
};
```

## 🎨 样式定制

组件提供了基础的样式类，可以通过CSS进行定制：

```scss
.flex-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.button-container {
  display: flex;
  justify-content: flex-start;
  margin-left: 8px;
  gap: 8px;
  width: 21%;
}

.link {
  color: #00bfff;
  cursor: pointer;
  
  &:hover {
    text-decoration: underline;
  }
}
```

### 自定义样式示例

```scss
// 自定义按钮容器宽度
.custom-array-field {
  .button-container {
    width: 30%; // 调整按钮区域宽度
  }
  
  // 自定义链接颜色
  .link {
    color: #1890ff;
    font-weight: 500;
  }
}
```

## 🔧 与其他组件配合

### 配合FormSection使用

```vue
<template>
  <FormSection title="身份信息" icon-class="identity-info-icon">
    <FormArrayFieldGroup 
      label="曾用名" 
      v-model="formData.usedNames" 
      field-key="oldName">
      <template #default="{ item, index, updateItem }">
        <a-input 
          :value="item" 
          @update:value="updateItem(index, $event)" 
          placeholder="请填写" />
      </template>
    </FormArrayFieldGroup>
  </FormSection>
</template>
```

### 配合useArrayFields composable

```vue
<script setup>
import { useArrayFields } from '@/composables/useArrayFields.js';

// 使用composable管理数组逻辑
const {
  items: usedNames,
  addItem,
  removeItem,
  updateItem,
  validItems
} = useArrayFields([''], '', 1);
</script>
```

## ⚠️ 注意事项

1. **数据格式一致性**：确保defaultValue的类型与数组项的类型一致
2. **唯一fieldKey**：在同一个表单中，每个FormArrayFieldGroup的fieldKey必须唯一
3. **最小项目限制**：当数组长度等于minItems时，删除按钮会自动隐藏
4. **插槽作用域**：正确使用插槽的作用域参数，特别是updateItem方法
5. **响应式更新**：使用updateItem方法而不是直接修改数组项，确保响应式更新

## 🚀 扩展建议

1. **添加验证支持**：集成表单验证逻辑
2. **拖拽排序**：支持数组项的拖拽重排
3. **批量操作**：添加批量删除、批量导入等功能
4. **动画效果**：为增删操作添加过渡动画
5. **最大项目限制**：添加maxItems属性限制最大项目数量
