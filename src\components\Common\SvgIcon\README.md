# SvgIcon 组件

一个基于 `vite-plugin-svg-icons` 的 SVG 图标组件，用于在 Vue 3 项目中高效地使用 SVG 图标。

## 功能特性

- 🚀 基于 `vite-plugin-svg-icons` 插件，构建时预处理 SVG 图标
- 📦 支持灵活的尺寸配置（size、width、height）
- 🎨 自动继承父元素的文字颜色（`fill: currentColor`）
- 🔧 支持自定义 CSS 类名
- ♿ 内置无障碍支持（`aria-hidden="true"`）
- 🏗️ TypeScript 友好

## 组件 API

### Props

| 参数名      | 类型   | 默认值 | 必填 | 说明                      |
| ----------- | ------ | ------ | ---- | ------------------------- |
| iconClass   | String | -      | ✅   | 图标文件名（不含扩展名）  |
| size        | String | '14px' | ❌   | 图标尺寸，同时设置宽高    |
| width       | String | ''     | ❌   | 图标宽度，优先级高于 size |
| height      | String | ''     | ❌   | 图标高度，优先级高于 size |
| customClass | String | ''     | ❌   | 自定义 CSS 类名           |

### 尺寸优先级

1. 如果设置了 `width`，则宽度使用 `width`，否则使用 `size`
2. 如果设置了 `height`，则高度使用 `height`，否则使用 `size`

## 使用示例

### 基础用法

```vue
<template>
  <!-- 使用默认尺寸 -->
  <SvgIcon icon-class="home" />

  <!-- 设置统一尺寸 -->
  <SvgIcon icon-class="user" size="20px" />

  <!-- 分别设置宽高 -->
  <SvgIcon icon-class="logo" width="100px" height="40px" />

  <!-- 添加自定义类名 -->
  <SvgIcon icon-class="edit" custom-class="my-icon" />
</template>

<script setup>
import SvgIcon from '@/components/Common/SvgIcon/index.vue';
</script>
```

### 在按钮中使用

```vue
<template>
  <a-button>
    <SvgIcon icon-class="plus" size="16px" />
    添加
  </a-button>
</template>
```

### 响应式尺寸

```vue
<template>
  <SvgIcon icon-class="menu" :size="isMobile ? '18px' : '24px'" />
</template>

<script setup>
import { ref } from 'vue';

const isMobile = ref(window.innerWidth < 768);
</script>
```

## 图标文件组织

### 目录结构

根据 vite 配置，SVG 图标文件应放置在 `src/assets/svg` 目录下：

```
src/assets/svg/
├── common/
│   ├── home.svg
│   ├── user.svg
│   └── logo.svg
├── login/
│   ├── account.svg
│   ├── key.svg
│   └── pass.svg
└── menus/
    ├── line.svg
    └── line-active.svg
```

### Symbol ID 规则

基于配置 `symbolId: 'icon-[dir]-[name]'`，图标的引用规则为：

- `src/assets/images/common/home.svg` → `icon-common-home`
- `src/assets/images/login/account.svg` → `icon-login-account`
- `src/assets/images/pass.svg` → `icon-pass`

### 使用对应关系

```vue
<!-- 引用 src/assets/images/common/home.svg -->
<SvgIcon icon-class="common-home" />

<!-- 引用 src/assets/images/login/account.svg -->
<SvgIcon icon-class="login-account" />

<!-- 引用 src/assets/images/pass.svg -->
<SvgIcon icon-class="pass" />
```

## 样式定制

### 颜色控制

组件使用 `fill: currentColor`，会自动继承父元素的文字颜色：

```vue
<template>
  <!-- 继承父元素颜色 -->
  <div style="color: #1890ff;">
    <SvgIcon icon-class="home" />
  </div>

  <!-- 通过 CSS 变量控制 -->
  <SvgIcon icon-class="user" class="custom-color" />
</template>

<style scoped>
.custom-color {
  color: #52c41a;
}
</style>
```

### 自定义样式

```vue
<template>
  <SvgIcon icon-class="star" custom-class="rotating-icon" size="24px" />
</template>

<style scoped>
.rotating-icon {
  animation: rotate 2s linear infinite;
  color: #faad14;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
```

## 注意事项

1. **图标文件格式**：确保 SVG 文件是有效的，建议使用优化过的 SVG
2. **文件命名**：使用小写字母和连字符，避免特殊字符
3. **尺寸单位**：建议使用 `px`、`em`、`rem` 等 CSS 单位
4. **性能优化**：`vite-plugin-svg-icons` 会在构建时将所有 SVG 合并为 sprite，提高加载性能
5. **开发调试**：如果图标不显示，检查文件路径和 vite 配置是否正确

## 版本信息

- **Vue 版本**: 3.x
- **依赖插件**: vite-plugin-svg-icons
- **浏览器支持**: 现代浏览器（支持 SVG `<use>` 元素）

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基础图标显示功能
- 支持尺寸和样式自定义
- 集成 vite-plugin-svg-icons 插件
