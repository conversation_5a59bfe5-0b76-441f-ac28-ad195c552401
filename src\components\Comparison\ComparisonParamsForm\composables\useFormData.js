import { ref, computed, watch } from 'vue';
import { isEqual } from 'lodash';

/**
 * 表单数据管理 composable
 * 管理表单数据的响应式状态和验证逻辑
 */
export function useFormData(props, emits) {
  // 表单数据
  const formData = ref({
    portraitLibraryIds: [],
    algorithmCodes: ['ci_an'],
    numberOfReturns: 50,
    matchingThreshold: 0.7,
    strategyId: '',
    portraitLibraryId: ''
  });

  // 更新表单数据
  const setFormData = (data) => {
    formData.value = {
      portraitLibraryIds: data.portraitLibraryIds || [],
      algorithmCodes: data.algorithmCodes || ['ci_an'],
      numberOfReturns: data.numberOfReturns || 50,
      matchingThreshold: data.matchingThreshold || 0.7,
      strategyId: data.strategyId || '',
      portraitLibraryId: data.portraitLibraryIds?.[0] || '',
    };
  };

  // 监听默认参数变化
  watch(
    () => props.defaultParams,
    (newVal) => {
      setFormData(newVal);
    },
    { deep: true, immediate: true }
  );

  // 人像组选项处理
  const portraitGroupOptionsCopy = computed(() => {
    return props.portraitGroupOptions.map(group => ({
      ...group,
      label: group.name,
      value: group.id
    }));
  });

  // 当前选中的人像组
  const hoveredGroup = computed(() => {
    return props.portraitGroupOptions.find(group => group.id === formData.value?.portraitLibraryId);
  });

  // 选中的人像组名称
  const selectedGroupNames = computed(() => {
    return props.portraitGroupOptions
      .filter(g => formData.value?.portraitLibraryId === g.id)
      .map(g => g.name);
  });

  // 判断表单是否变更
  const isFormChanged = computed(() => {
    return !isEqual(formData.value, props.defaultParams);
  });

  // 字段变更处理
  const handleFieldChange = () => {
    let groupName;
    let libraries;

    if (formData.value.portraitLibraryId) {
      const group = props.portraitGroupOptions.find(g => g.id === formData.value.portraitLibraryId);
      if (group) {
        groupName = group.name;
        libraries = group.libraries?.map(item => item.name).join(',');
      }
    }

    emits('comparison-change', {
      ...formData.value,
      groupName,
      libraries
    });
  };

  // 提交处理
  const handleSubmit = () => {
    console.log('提交的表单数据:', formData.value);
    console.log('isFormChanged =', isFormChanged.value);
    if (isFormChanged.value) {
      // 如果修改过，那就删除原来的strategyId
      formData.value.strategyId = '';
    }
    emits('submit-success', {
      params: formData.value,
      changed: isFormChanged.value,
    });
  };

  // 获取表单数据
  const getFormData = () => {
    return formData.value;
  };

  return {
    formData,
    setFormData,
    portraitGroupOptionsCopy,
    hoveredGroup,
    selectedGroupNames,
    isFormChanged,
    handleFieldChange,
    handleSubmit,
    getFormData
  };
}
