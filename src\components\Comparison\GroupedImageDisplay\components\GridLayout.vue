<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 15:50:00
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 15:50:00
 * @FilePath: \platform-face-web\src\components\Business\GroupedImageDisplay\components\GridLayout.vue
 * @Description: 网格布局组件 - 支持1x2和2x2布局
 -->
<template>
  <div class="grid-layout" :class="gridClass">
    <div 
      v-for="(image, index) in images" 
      :key="index" 
      class="grid-item"
    >
      <ImageItem
        :image="image"
        :selected-items="selectedItems"
        :ring-color="ringColor"
        :selected-ring-color="selectedRingColor"
        :ring-thickness="ringThickness"
        :thumbnail-size="thumbnailSize"
        @dragstart="handleDragStart"
        @click="handleClick"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import ImageItem from './ImageItem.vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 图片数据数组 */
  images: {
    type: Array,
    required: true
  },
  /** 网格类型：'1x2' 或 '2x2' */
  gridType: {
    type: String,
    required: true,
    validator: (value) => ['1x2', '2x2'].includes(value)
  },
  /** 已选中的图片ID数组 */
  selectedItems: {
    type: Array,
    default: () => []
  },
  /** 默认边框颜色 */
  ringColor: {
    type: String,
    default: '#4c5f78'
  },
  /** 选中状态边框颜色 */
  selectedRingColor: {
    type: String,
    default: '#4DBFFF'
  },
  /** 边框厚度 */
  ringThickness: {
    type: Number,
    default: 0
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['dragstart', 'click']);

/**
 * 计算属性
 */
// 网格CSS类名
const gridClass = computed(() => {
  return `grid-${props.gridType}`;
});

// 缩略图尺寸配置
const thumbnailSize = computed(() => {
  if (props.gridType === '1x2') {
    return {
      width: '75px',
      height: '48px',
      bottom: '2px',
      right: '2px'
    };
  } else {
    return {
      width: '54px',
      height: '36px',
      bottom: '2px',
      right: '2px'
    };
  }
});

/**
 * 事件处理方法
 */
const handleDragStart = (event, image) => {
  emit('dragstart', event, image);
};

const handleClick = (event, image) => {
  emit('click', event, image);
};
</script>

<style lang="scss" scoped>
.grid-layout {
  width: 100%;
  height: 100%;
  display: grid;
  gap: 4px;
  
  &.grid-1x2 {
    grid-template-columns: 1fr 1fr;
  }
  
  &.grid-2x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }
}

.grid-item {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
