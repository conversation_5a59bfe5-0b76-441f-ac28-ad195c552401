<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 15:30:00
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 15:30:00
 * @FilePath: \platform-face-web\src\components\Business\GroupedImageDisplay\components\ImageItem.vue
 * @Description: 图片项基础组件 - 封装ThumbnailDecorator + AvatarImage的组合
 -->
<template>
  <div 
    class="image-item" 
    :draggable="draggable"
    @dragstart="handleDragStart"
    @click="handleClick"
  >
    <ThumbnailDecorator 
      :thumbnail-src="thumbnailSrc"
      :image-height="thumbnailSize.height"
      :image-width="thumbnailSize.width"
      :bottom="thumbnailSize.bottom"
      :right="thumbnailSize.right"
      :show-original-photo="showOriginalPhoto"
    >
      <AvatarImage 
        border-radius="8px"
        :width="'100%'"
        :height="'100%'"
        :src="imageSrc"
        :ring-color="isSelected ? selectedRingColor : ringColor"
        :ring-thickness="ringThickness"
        :gap="0"
        :face-x="faceCoordinate.x"
        :face-y="faceCoordinate.y"
        :face-width="faceCoordinate.width"
        :face-height="faceCoordinate.height"
        container-width="100%"
        container-height="100%"
      />
    </ThumbnailDecorator>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { DEFAULT_CONFIG } from '@/constants';
import AvatarImage from '@/components/Common/AvatarImage/index.vue';
import ThumbnailDecorator from '@/components/Common/ThumbnailDecorator/index.vue';

const { getImageWebURL } = DEFAULT_CONFIG || {};

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 图片数据对象 */
  image: {
    type: Object,
    required: true
  },
  /** 已选中的图片ID数组 */
  selectedItems: {
    type: Array,
    default: () => []
  },
  /** 默认边框颜色 */
  ringColor: {
    type: String,
    default: '#4c5f78'
  },
  /** 选中状态边框颜色 */
  selectedRingColor: {
    type: String,
    default: '#4DBFFF'
  },
  /** 边框厚度 */
  ringThickness: {
    type: Number,
    default: 0
  },
  /** 缩略图尺寸配置 */
  thumbnailSize: {
    type: Object,
    default: () => ({
      width: '75px',
      height: '48px',
      bottom: '2px',
      right: '2px'
    })
  },
  /** 是否可拖拽 */
  draggable: {
    type: Boolean,
    default: true
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['dragstart', 'click']);

/**
 * 计算属性
 */
// 图片源地址
const imageSrc = computed(() => {
  return getImageWebURL(props.image?.multiFaceData?.sourceImageId);
});

// 缩略图源地址
const thumbnailSrc = computed(() => {
  return getImageWebURL(props.image?.multiFaceData?.sourceImageId);
});

// 是否显示原图缩略图
const showOriginalPhoto = computed(() => {
  return props.image?.location !== props.image?.multiFaceData?.sourceImageId;
});

// 是否被选中
const isSelected = computed(() => {
  return props.selectedItems.includes(props.image?.involvedFaceId);
});

// 人脸坐标
const faceCoordinate = computed(() => {
  const coordinate = props.image?.multiFaceData?.items?.[0]?.coordinate || [0, 0, 0, 0];
  return {
    x: coordinate[0],
    y: coordinate[1],
    width: coordinate[2],
    height: coordinate[3]
  };
});

/**
 * 事件处理方法
 */
const handleDragStart = (event) => {
  emit('dragstart', event, props.image);
};

const handleClick = (event) => {
  emit('click', event, props.image);
};
</script>

<style lang="scss" scoped>
.image-item {
  width: 100%;
  height: 100%;
  cursor: pointer;
  
  &[draggable="true"] {
    cursor: grab;
    
    &:active {
      cursor: grabbing;
    }
  }
}
</style>
