# 为了防止意外地将一些环境变量泄漏到客户端，只有以 VITE_ 为前缀的变量才会暴露给经过 vite 处理的代码。
# js中通过`import.meta.env.VITE_API_BASE_URL`取值

#端口号
VITE_APP_PORT = 9999

# 接口前缀
VITE_API_BASE_URL = '/pro-api'

# 后端服务地址
# VITE_APP_SERVICE_API = 'http://localhost:888'

# 后端文件上传地址
#VITE_APP_BASE_FILE_API = '/pro-api/web/api/system/file/upload'

# 是否删除debugger
VITE_DROP_DEBUGGER = false

# 是否删除console.log
VITE_DROP_CONSOLE = false

# 打包路径
VITE_BASE_PATH = ./

# 输出路径
VITE_OUT_DIR = dist-pro

# 标题
VITE_APP_TITLE = 数智AI-人脸多模态-系统v1.0

VITE_API_BASE_URL=''
VITE_ENABLE_DEVTOOLS=true
VITE_DEEPSEEK_API_KEY='***********************************'
VITE_DEEPSEEK_BASE_URL='/deepseek-api'
VITE_EXCEL_DOWNLOAD='/excel'
VITE_OBSOLETED_SYSTEM_URL=''
VITE_PORTRAIT_SERVICE_URL=''
VITE_KNOWLEDGE_SERVICE_URL=''