<template>
  <!-- 左侧会话栏主容器 -->
  <div :class="['sidebar', { collapsed: isCollapsed }]">
    <div class="sidebar-header">
      <!-- 返回按钮，可选显示 -->
      <a-button v-if="showBack" class="back-btn" @click="onBack">
        <SvgIcon icon-class="return-gray" size="17px" />
      </a-button>
      <Divider direction="vertical" color="#40516A" height="10px" />

      <!-- 专题名称展示，带图标 -->
      <div class="topic-name">
        <img src="@/assets/svg/common/ai-icon-high.svg" alt="" />
        <BadgeWrapper :key="`${new Date().getTime()}-0`" :count="50" show-zero>
          {{ currentTopic || '未选择专题' }}
        </BadgeWrapper>
        <SvgIcon v-if="isCanChange" icon-class="down-arrow-high" size="10px" style="margin-left: 10px;cursor: pointer;" />
      </div>
      <slot name="header">
      </slot>
    </div>
    <!-- 会话内容插槽 -->
    <div class="session-list">
      <slot name="content"></slot>
    </div>
    <!-- 收缩/展开按钮 -->
    <a-button :class="['collapsed-btn', { 'is-collapsed': isCollapsed }]" @click="toggleCollapse">
      <img src="@/assets/svg/common/arrow-r.svg" alt="" />
    </a-button>
  </div>
</template>

<script setup>
// 引入Vue相关API
import { ref, defineProps } from 'vue'
// 组件props定义
const props = defineProps({
  topics: {
    type: Array,
    default: () => []
  },
  currentTopic: {
    type: String,
    default: ''
  },
  showBack: {
    type: Boolean,
    default: true
  },
  // 返回按钮点击事件
  onBack: {
    type: Function,
    default: () => { }
  },
  isCanChange: {
    type: Boolean,
    default: true
  }
})
// 收缩状态
const isCollapsed = ref(false)
// 切换收缩展开
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

</script>

<style lang="scss" scoped>
// 左侧栏主样式
.sidebar {
  min-width: 300px;
  width: 18%;
  height: 100%;
  transition: all 0.1s;
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  margin-left: 10px;
  padding: 10px;
  position: relative;

  &.collapsed {
    width: 16px;
    min-width: 16px;
    overflow: hidden;
    padding: 10px 0;
    position: relative;

    .sidebar-header {
      visibility: hidden;
    }

    &::before {
      content: '';
      position: absolute;
      top: 18px;
      left: 0;
      width: 100%;
      height: calc(100% - 30px);
      background: #202531;
      z-index: 3;
    }
  }

  // 会话内容区
  .session-list {
    flex: 1;
    overflow-y: auto;
    background: #202531;
    margin-top: 5px;
    border-radius: 10px;
    position: relative;
  }

  // 顶部header区
  .sidebar-header {
    overflow: hidden;
    max-height: 32px;
    display: flex;
    align-items: center;
    gap: 2px;

    // 返回按钮样式
    .back-btn {
      background: transparent;
      font-size: 18px;
      border: none;
      position: relative;
      padding: 0;
      padding-top: 3px;
    }

    // 专题名称样式
    .topic-name {
      display: flex;
      align-items: center;
      font-size: 14px;
      min-width: max-content;

      img {
        width: 18px;
        height: 18px;
        margin-right: 5px;
      }
    }
  }

  // 收缩按钮样式
  .collapsed-btn {
    width: 10px;
    padding: 5px 2px;
    position: absolute;
    box-sizing: content-box;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    z-index: 3;
    background: #121318;
    border: none;
    color: #77decd;
    box-shadow: none;
    border-radius: 10px 0 0 10px;
  }

  // 收缩时按钮样式
  .is-collapsed {
    right: 0;

    img {
      transform: rotate(180deg);
    }
  }
}

// 通用按钮样式
.ant-btn {
  border-radius: 2px;
  padding: 0 10px;
}
</style>