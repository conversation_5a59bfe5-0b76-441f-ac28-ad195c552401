# Error 403 & 404 组件说明

本目录下包含两个高颜值、暗色系风格的错误页组件：
- `403.vue` —— 无权限访问页
- `404.vue` —— 页面未找到

## 组件简介
- **403.vue**：用于用户无权限访问时的提示页面，内含幽灵抱锁SVG插画。
- **404.vue**：用于路由未匹配时的提示页面，内含对称幽灵SVG插画。

## 主要特性
- 暗色渐变背景，现代感强
- SVG插画，支持高分屏，视觉友好
- 动画效果（浮动、淡入、按钮高亮）
- 响应式布局，移动端适配
- 一键返回首页按钮
- 代码结构清晰，易于二次开发

## 使用方法

### 1. 路由配置中引入
```js
// 以404为例
{
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: () => import('@/components/Error/404.vue'),
}
// 403页可在权限校验失败时跳转
{
  path: '/403',
  name: 'NoAuth',
  component: () => import('@/components/Error/403.vue'),
}
```

### 2. 页面/组件中直接使用
```vue
<template>
  <Error403 />
  <Error404 />
</template>

<script setup>
import Error403 from '@/components/Error/403.vue'
import Error404 from '@/components/Error/404.vue'
</script>
```

## 参数说明
- 当前组件无props参数，所有内容均可直接在文件内自定义。
- 返回首页按钮跳转逻辑可在 `<script setup>` 中修改。

## 自定义建议
- **插画**：SVG代码可替换为你喜欢的风格或品牌色插画。
- **文案**：直接修改 `.error-desc`、`.error-code` 等内容即可。
- **跳转路径**：修改 `goHome` 方法中的 `router.push()` 跳转目标。
- **按钮样式**：可调整 `.back-btn` 的样式变量，适配品牌色。

## 设计风格说明
- 暗色系渐变背景，适合现代后台、数据平台等场景
- 插画采用幽灵、锁等元素，科技感、趣味性兼具
- 动画细节丰富（浮动、按钮流光、淡入）
- 适配高分屏和移动端

## 适用场景
- 路由未匹配（404）
- 权限校验失败（403）
- 业务自定义错误页

## 示例代码
```js
// 路由守卫中使用403
router.beforeEach((to, from, next) => {
  if (!hasPermission(to)) {
    next('/403');
  } else {
    next();
  }
});

// 404兜底
{
  path: '/:pathMatch(.*)*',
  name: 'NotFound',
  component: () => import('@/components/Error/404.vue'),
}
```

---
