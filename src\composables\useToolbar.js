// src/composables/useToolbar.js
import { ref, computed } from 'vue';
import request from '@/utils/service';
import { DEFAULT_CONFIG } from '@/constants';
import { cloneDeep } from 'lodash';
import { Message } from '@/utils/message';
import { useStore } from 'vuex';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;
const { getImageWebURL } = DEFAULT_CONFIG || {};

export function useToolbar({ moduleName }) {
  const store = useStore();

  // 状态变量：工具栏显示与钉住状态
  const isToolbarVisible = ref(false);
  const isToolbarPinned = ref(false);

  // 当前查看的对象列表（用于展示）
  const viewedObjects = ref([]);

  // 当前选择模式，取值 'group' 或 'image'
  const currentSelectionMode = ref('group');

  // 当前选中的对象（根据模式：选中组或选中图像）
  const activeGroup = ref(null);
  const activeImage = ref(null);

  // 当前对象数据（请求得到的详细数据）
  const currentObjectData = ref(null);

  // 加载状态与错误信息
  const isLoading = ref(false);
  const error = ref(null);

  // 模态框类型及内容
  const modalType = ref(null);
  const modalInfo = ref({});

  const subjectId = computed(() => store.state[moduleName]?.sharedData?.subjectId || '');

  /**
   * 更新当前查看对象列表
   * @param {Array} selectedItems
   */
  const updateViewedObjects = (selectedItems) => {
    if (selectedItems.length === 0) {
      viewedObjects.value = [];
    } else {
      viewedObjects.value = cloneDeep(selectedItems);
    }
  };

  /**
   * 设置选择模式（'group' 或 'image'），同时清空当前选中
   * @param {string} mode
   */
  const setSelectionMode = (mode) => {
    currentSelectionMode.value = mode;
  };

  /**
   * 根据当前选择模式选中对象
   * @param {Object} item
   */
  const selectItem = async (item) => {
    console.log('currentSelectionMode.value =', currentSelectionMode.value);
    if (currentSelectionMode.value === 'group') {
      console.log('group =', item);
      activeGroup.value = item;
      isToolbarVisible.value = true;
      await loadObjectData(item.groupId); //加载对应的人脸数据
    } else if (currentSelectionMode.value === 'image') {
      console.log('image =', item);
      if (item?.groupImages?.length > 0) {
        activeImage.value = item.groupImages[0];
      } else {
        activeImage.value = item;
      }
      isToolbarVisible.value = true;
      await loadObjectData(activeImage.value.involvedFaceId); //加载对应的人脸数据
    } else {
      // nothing
    }
  };

  /**
   * 清空选中的对象
   */
  const clearSelection = (clearGroup = true) => {
    if (clearGroup) {
      activeGroup.value = null;
    }
    activeImage.value = null;
  };

  /**
   * 切换工具栏钉住状态
   */
  const togglePinStatus = () => {
    isToolbarPinned.value = !isToolbarPinned.value;
  };

  /**
   * 根据对象 ID 加载详细数据
   * @param {string} objectId
   */
  const loadObjectData = async (objectId) => {
    try {
      const response = await request.get(`${BASE_URL}/api/subjects/faces/${objectId}`);
      currentObjectData.value = response.data;
      store.dispatch(`${moduleName}/saveSharedData`, { key: 'sourceComparisonDetail', value: currentObjectData.value });
    } catch (err) {
      error.value = err.message || '请求失败';
    }
  };

  // 按钮配置常量
  const BASE_BUTTONS = [
    { type: 'toolbar-selected-source', imageSrc: '', multiFaceData: null },
    { type: 'button', iconName: 'keyman', iconWidth: '20px', iconHeight: '23px', label: '设置关键人物', actionKey: 'commandSetKeyman' },
    { type: 'divider', dividerType: 'solid' },
    { type: 'toolbar-man-info-item' },
    {
      type: 'button',
      iconName: 'man-info',
      iconWidth: '20px',
      iconHeight: '20px',
      label: '关键信息',
      actionKey: 'modalEditManInfo',
      copyAndParseSupport: true,
    },
    { type: 'divider', dividerType: 'dashed' },
    {
      type: 'button',
      iconName: 'image-info',
      iconWidth: '24px',
      iconHeight: '22px',
      label: '图像信息',
      actionKey: 'modalEditImageInfo',
      copyAndParseSupport: true,
    },
    {
      type: 'button',
      iconName: 'comparison-strategy',
      iconWidth: '22px',
      iconHeight: '22px',
      label: '比对策略',
      actionKey: 'modalEditComparisonStrategy',
      copyAndParseSupport: true,
    },
    { type: 'divider', dividerType: 'dashed' },
    {
      type: 'button',
      iconName: 'image-to-library',
      iconWidth: '21px',
      iconHeight: '21px',
      label: '大库检索',
      actionKey: 'commandRequestImageToLibraryComparison',
    },
    {
      type: 'button',
      iconName: 'image-to-library-results',
      iconWidth: '39px',
      iconHeight: '22px',
      label: '大库检索结果',
      actionKey: 'commandViewImageToLibraryComparisonResults',
    },
    { type: 'divider', dividerType: 'dashed' },
    { type: 'button', iconName: 'add-image', iconWidth: '22px', iconHeight: '22px', label: '添加照片', actionKey: 'commandAddImage' },
    { type: 'button', iconName: 'remove-portrait-gray', iconWidth: '22px', iconHeight: '22px', label: '移除照片', actionKey: 'commandRemoveImage' },
    { type: 'divider', dividerType: 'dashed' },
    { type: 'button', iconName: 'more', iconWidth: '3px', iconHeight: '14px', label: '更多', actionKey: 'selfViewMore' },
  ];

  const IMAGE_TO_IMAGE_BUTTONS = {
    1: { iconName: 'target', label: '设置关注人', actionKey: 'commandSetTargetMan' },
    9: { iconName: 'image-to-image', label: '同人预判', actionKey: 'commandRequestImageToImageComparison' },
    10: { iconName: 'image-to-image-results', label: '同人预判结果', actionKey: 'commandViewImageToImageComparisonResults' },
  };

  // 处理多人脸数据，只保留当前选择的人脸
  function processMultiFaceData(currentData) {
    if (!currentData?.multiFaceData) return null;

    const multiFaceData = cloneDeep(currentData.multiFaceData);
    if (multiFaceData.items.length > 1) {
      const currentFace = multiFaceData.items.find((item) => item.imageId === currentData.location);
      multiFaceData.items = currentFace ? [currentFace] : [];
    }
    return multiFaceData;
  }

  // 获取当前对象的状态信息
  function getCurrentObjectStatus(viewedObjects, currentData) {
    if (!currentData?.involvedFaceId) return null;
    return viewedObjects.find((obj) => obj.groupId === currentData.involvedFaceId);
  }

  // 根据模块和状态更新按钮配置
  function updateButtonsByStatus(buttons, moduleName, currentStatus) {
    const updatedButtons = [...buttons];

    if (moduleName === 'ImageToImagePage_default') {
      // 更新特定于ImageToImagePage的按钮
      Object.entries(IMAGE_TO_IMAGE_BUTTONS).forEach(([index, config]) => {
        updatedButtons[index] = { ...updatedButtons[index], ...config };
      });

      // 处理关注人状态
      if (currentStatus?.isTargetMan) {
        updatedButtons[1] = {
          ...updatedButtons[1],
          label: '取消关注人',
          actionKey: 'commandCancelTargetMan',
        };
      }
    } else {
      // 处理关键人物状态
      if (currentStatus?.isInvolvedMan) {
        updatedButtons[1] = {
          ...updatedButtons[1],
          label: '取消关键人物',
          actionKey: 'commandCancelKeyman',
        };
      }
    }

    return updatedButtons;
  }

  // 计算属性：工具栏内容，根据当前对象数据生成按钮等配置
  const toolbarContent = computed(() => {
    if (!currentObjectData.value) return null;

    // 处理多人脸数据
    const multiFaceData = processMultiFaceData(currentObjectData.value);

    // 2.根据人脸不同状态设置不同的按钮
    const buttons = [...BASE_BUTTONS];

    buttons[0] = {
      ...buttons[0],
      imageSrc: getImageWebURL(currentObjectData.value.multiFaceData.sourceImageId),
      multiFaceData,
    };

    // 获取当前对象状态
    const currentStatus = getCurrentObjectStatus(viewedObjects.value, currentObjectData.value);

    // 根据模块和状态更新按钮
    const finalButtons = updateButtonsByStatus(buttons, moduleName, currentStatus);

    return {
      name: '',
      buttons: finalButtons,
    };
  });

  /**
   * 关闭工具栏（同时重置模态框类型）
   */
  const closeToolbar = () => {
    isToolbarVisible.value = false;
    modalType.value = null;
  };

  /**
   * 关闭模态框（只重置模态框类型和选择类型）
   */
  const closeModal = () => {
    modalType.value = null;
    currentSelectionMode.value = 'group';
  };

  // 关键信息或者比对策略的保存编辑操作
  const updateInformation = async (formData, type = 'information') => {
    try {
      const refreshAllFacesInformation = currentObjectData.value?.involvedId ? true : false; //如果是有分组的就会存在这个值，否则就不会存在
      const payload = {
        refreshAllFacesInformation,
      };
      if (type === 'information') {
        payload['involvedInformation'] = {
          ...formData,
        };
      } else if (type === 'strategy') {
        payload['comparisonStrategy'] = {
          ...formData,
        };
      }
      await request.put(`${BASE_URL}/api/subjects/faces/${currentObjectData.value.involvedFaceId}`, payload);
      Message.success('编辑成功！');
      // 编辑成功后重新获取一下人脸的信息
      await loadObjectData(currentObjectData.value.involvedFaceId);
    } catch (error) {
      console.error('更新图片信息出错', error);
      Message.error('编辑失败！');
    }
  };

  // 图像信息的保存编辑操作
  const updataImageInfo = async (formData) => {
    try {
      const subjectMaterialId = activeImage.value?.subjectMaterialId || currentObjectData.value?.subjectMaterialId;
      await request.put(`${BASE_URL}/api/subjects/${subjectId.value}/materials/${subjectMaterialId}`, formData);
      Message.success('编辑成功！');
      await loadObjectData(currentObjectData.value.involvedFaceId);
    } catch (error) {
      console.error('更新图片信息出错', error);
      Message.error('编辑失败！');
    }
  };

  // 返回状态和操作方法，状态和方法分开处理便于调用
  return {
    // 状态
    isToolbarVisible,
    isToolbarPinned,
    viewedObjects,
    currentSelectionMode,
    activeGroup,
    activeImage,
    currentObjectData,
    isLoading,
    error,
    modalType,
    modalInfo,
    toolbarContent,
    moduleName,

    // 方法
    updateViewedObjects,
    setSelectionMode,
    selectItem,
    clearSelection,
    togglePinStatus,
    loadObjectData,
    closeToolbar,
    closeModal,
    updateInformation,
    updataImageInfo,
  };
}
