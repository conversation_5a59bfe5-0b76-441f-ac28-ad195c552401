<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 17:36:18
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 10:54:39
 * @FilePath: \platform-face-web\src\components\Comparison\ManInfoForm\components\IdentityFields.vue
 * @Description: 身份信息字段组件
 * 包含姓名、性别、曾用名、年龄、别名、籍贯、证件号、居住地等身份相关字段
 * 
-->
<template>
  <div class="identity-fields">
    <!-- 第一行：姓名、性别 -->
    <div class="form-row">
      <a-form-item label="姓名">
        <div class="flex-row">
          <a-input v-model:value="formData.name" placeholder="请输入姓名" class="form-wrapper" />
          <div class="button-container"></div>
        </div>
      </a-form-item>

      <a-form-item label="性别">
        <a-radio-group v-model:value="formData.sex">
          <a-radio value="male">男</a-radio>
          <a-radio value="female">女</a-radio>
          <a-radio value="unknown">未知</a-radio>
        </a-radio-group>
      </a-form-item>
    </div>

    <!-- 第二行：曾用名、年龄 -->
    <div class="form-row">
      <ArrayFieldGroup label="曾用名" v-model="formData.usedNames" field-key="oldName" default-value="">
        <template #default="{ item, index, updateItem }">
          <a-input :value="item" @update:value="updateItem(index, $event)" placeholder="请填写" class="form-wrapper" />
        </template>
      </ArrayFieldGroup>

      <a-form-item label="年龄">
        <div class="flex-row align-items-center">
          <a-input-number v-model:value="formData.age" :min="0" :max="120" placeholder="请填写" />
          <a-slider v-model:value="formData.ageRange" range :max="120" style="width: 50%" />
          <span style="margin-left: 8px">
            {{ `${formData.ageRange[0]}-${formData.ageRange[1]}` }}
          </span>
        </div>
      </a-form-item>
    </div>

    <!-- 第三行：别名、籍贯 -->
    <div class="form-row">
      <ArrayFieldGroup label="别名" v-model="formData.aliasNames" field-key="alias" default-value="">
        <template #default="{ item, index, updateItem }">
          <a-input :value="item" @update:value="updateItem(index, $event)" placeholder="请填写" class="form-wrapper" />
        </template>
      </ArrayFieldGroup>

      <a-form-item label="籍贯" name="nativePlace">
        <a-input v-model:value="formData.nativePlace" placeholder="请填写" style="width: 140px;" />
      </a-form-item>
    </div>

    <!-- 第四行：证件号 -->
    <div class="form-row">
      <ArrayFieldGroup v-if="infoDictionaries" label="证件号" v-model="formData.identifications" field-key="certificate"
        :default-value="defaultCertificate">
        <template #default="{ item, index, updateItem }">
          <a-select :value="item.dictionaryItemId" @update:value="updateCertificateType(index, $event)"
            placeholder="证件类型" class="form-wrapper">
            <a-select-option v-for="(data, dictIndex) in infoDictionaries['ID_CARD_NO']" :key="dictIndex"
              :value="data.id">
              {{ data.label }}
            </a-select-option>
          </a-select>
          <a-input :value="item.businessValue" @update:value="updateCertificateValue(index, $event)"
            placeholder="请输入证件号" style="width: 50%; margin-left: 8px;" />
        </template>
      </ArrayFieldGroup>
    </div>

    <!-- 第五行：居住地 -->
    <div class="form-row">
      <ArrayFieldGroup v-if="infoDictionaries" label="居住地" v-model="formData.addresses" field-key="address"
        :default-value="defaultAddress">
        <template #default="{ item, index, updateItem }">
          <a-select :value="item.dictionaryItemId" @update:value="updateAddressType(index, $event)" placeholder="地址类型"
            class="form-wrapper">
            <a-select-option v-for="(data, dictIndex) in infoDictionaries['RESIDENTIAL_ADDRESS']" :key="dictIndex"
              :value="data.id">
              {{ data.label }}
            </a-select-option>
          </a-select>
          <a-input :value="item.businessValue" @update:value="updateAddressValue(index, $event)" placeholder="请输入地址"
            style="width: 50%; margin-left: 8px;" />
        </template>
      </ArrayFieldGroup>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ArrayFieldGroup from '../../../Common/FormArrayFieldGroup/index.vue';

const props = defineProps({
  /**
   * 表单数据
   */
  formData: {
    type: Object,
    required: true
  },

  /**
   * 字典数据
   */
  infoDictionaries: {
    type: Object,
    default: null
  }
});

// 默认值
const defaultCertificate = ref({
  businessId: "",
  businessValue: "",
  dictionaryId: null,
  dictionaryItemId: null,
});
const defaultAddress = ref({
  businessId: "",
  businessValue: "",
  dictionaryId: null,
  dictionaryItemId: null,
});

/**
 * 更新证件类型
 */
const updateCertificateType = (index, value) => {
  props.formData.identifications[index].dictionaryItemId = value;
};

/**
 * 更新证件号
 */
const updateCertificateValue = (index, value) => {
  props.formData.identifications[index].businessValue = value;
};

/**
 * 更新地址类型
 */
const updateAddressType = (index, value) => {
  props.formData.addresses[index].dictionaryItemId = value;
};

/**
 * 更新地址值
 */
const updateAddressValue = (index, value) => {
  props.formData.addresses[index].businessValue = value;
};
</script>

<style scoped>
.identity-fields {
  /* 继承父组件样式 */
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row>* {
  flex: 1;
}

.flex-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.form-wrapper {
  width: 220px;
}

.button-container {
  width: 100px;
  display: flex;
  justify-content: flex-start;
  margin-left: 8px;
  gap: 8px;
}
</style>
