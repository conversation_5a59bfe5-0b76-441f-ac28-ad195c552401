<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-28 17:34:03
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 15:58:58
 * @FilePath: \platform-face-web\src\views\library\components\DualColumnLayout.vue
 * @Description: 用来处理人脸检测和人脸比对的布局，上中下，中部为左右两部分
 * 
 * 
-->
<template>
  <div class="dual-column-layout-container">
    <!-- 上部分：标题 -->
    <div class="header">
      <div class="d-flex align-items-center">
        <SvgIcon :iconClass="headerIcon" size="14px"></SvgIcon>
        <span class="box-title ml-2">{{ headerTitle }}</span>
      </div>
      <Divider marginSize="22px" color="#2E475C"></Divider>
    </div>

    <!-- 中部分：左右两个框 -->
    <div class="content">
      <div v-if="showStatus" class="icon-content">
        <template v-if="loading">
          <SvgIcon icon-class="loading" class="spin-icon" size="24px"></SvgIcon>
        </template>
        <template v-else>
          <slot name="completed-status">
            <SvgIcon icon-class="completed" size="24px"></SvgIcon>
          </slot>
        </template>
      </div>
      <div v-if="showStatus" class="result-status-content">
        <template v-if="loading">
          <span class="loading-title">{{ loadingText }}</span>
        </template>
        <template v-else>
          <slot name="completed-status-text">
            <span class="completed-title">{{ completedText }}</span>
          </slot>
        </template>
      </div>
      <div class="box left-box">
        <span class="box-title">{{ leftBoxTitle }}</span>
        <div class="box-content">
          <slot name="left-content"></slot>
        </div>
      </div>
      <div class="box right-box">
        <span class="box-title">{{ rightBoxTitle }}</span>
        <div class="box-content">
          <slot name="right-content"></slot>
        </div>
      </div>
    </div>

    <!-- 下部分：居中按钮 -->
    <div class="footer">
      <slot name="action-button"></slot>
    </div>
  </div>
</template>

<script setup>
import Divider from "@/components/Common/Divider/index.vue";

const props = defineProps({
  headerIcon: {
    type: String,
    default: "comparison-detect-image-high"
  },
  headerTitle: {
    type: String,
    default: "人脸检测"
  },
  leftBoxTitle: {
    type: String,
    default: "上传源图"
  },
  rightBoxTitle: {
    type: String,
    default: "输出结果"
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: "人脸检出中"
  },
  completedText: {
    type: String,
    default: "人脸检出成功！"
  },
  showStatus: {
    type: Boolean,
    default: true
  }
});
</script>

<style lang="scss" scoped>
.dual-column-layout-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}

.box-title {
  font-size: 14px;
  color: #F1F6F8;
}

.header {
  margin-bottom: 64px;
}

/* 中部分样式 */
.content {
  display: flex;
  justify-content: space-between;
  gap: 53px;
  margin-bottom: 110px;
  padding: 0 95px;
  position: relative;

  .box {
    border-radius: 4px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    gap: 26px;

    &.left-box {
      align-items: start;
    }

    &.right-box {
      align-items: end;
    }

    .box-content {
      width: 100%;
      height: 460px;
      border: solid 1px #2E475C;
      padding: 10px 9px;
      border-radius: 14px;
    }
  }

  // 自适应更大屏幕或中屏
  @media (min-width: 1600px) {
    .box {
      width: calc(50% - 10px);
      height: 500px;
    }
  }

  @media (max-width: 1599px) and (min-width: 1200px) {
    .box {
      width: calc(50% - 10px);
      height: 500px;
    }
  }

  @media (max-width: 1199px) {
    flex-direction: column;
    align-items: center;

    .box {
      width: 778px;
      height: 500px;
    }
  }
}

/* 下部分样式 */
.footer {
  display: flex;
  justify-content: center;
}

.result-status-content {
  width: 280px;
  height: 51px;
  border-radius: 51px;
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  border: solid 1px #4975AC;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 18px;
  z-index: 1;

  .loading-title {
    color: #B0BBCC;
  }

  .completed-title {
    color: #4DBFFF;
  }
}

.icon-content {
  position: absolute;
  top: -64px;
  left: 50%;
  transform: translateX(-50%);
  background: #16171C;
  padding: 0 14px;
  z-index: 2;
}

.spin-icon {
  animation: spin 2s infinite linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>