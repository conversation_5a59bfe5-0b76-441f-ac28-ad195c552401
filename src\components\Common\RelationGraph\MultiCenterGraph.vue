<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-18 10:11:17
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-21 17:21:08
 * @FilePath: \platform-face-web\src\components\Common\RelationGraph\MultiCenterGraph.vue
 * @Description: 多个中心节点的关系图谱组件
 * 
 * 
-->
<template>
    <div ref="container" class="w-100 h-100"></div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import * as d3 from 'd3';
import {
    layoutMultiCenterGraph,
    preprocessMultiLinks
} from './utils/layout/index';
import { renderMultiCenterGraph, addZoomBehavior, addResetButton } from './utils/render';
import ConfigManager from './utils/ConfigManager';

const props = defineProps({
    data: {
        type: Object,
        required: true,
        validator: value => value.nodes && value.links
    },
    linkThreshold: { type: Number, default: 0.7 }
});

const container = ref(null);
const svg = ref(null);
const width = ref(0);
const height = ref(0);

onMounted(() => {
    nextTick(() => {
        if (container.value) {
            width.value = container.value.clientWidth;
            height.value = container.value.clientHeight;
            initSVG();
            renderGraph(props.data);
        }
    })
});

watch(() => props.data, (newData) => {
    renderGraph(newData);
}, { deep: true });

function initSVG() {
    if (!container.value) return;

    if (svg.value) {
        container.value.removeChild(svg.value);
    }

    const svgEl = d3.create("svg")
        .attr("width", width.value)
        .attr("height", height.value)
        .attr("viewBox", [0, 0, width.value, height.value]);

    // 添加主容器group用于整体居中
    const mainGroup = svgEl.append("g")
        .attr("class", "main-group");

    container.value.appendChild(svgEl.node());
    svg.value = svgEl.node();

    const zoom = addZoomBehavior(svgEl, mainGroup)
    // TODO:暂时隐藏，后续加上的时候应该同时加上关闭vue的时候删掉按钮
    // addResetButton(svgEl, zoom)
}

async function renderGraph(data) {
    if (!svg.value || !data?.nodes) return;

    const centerNodesId = data.nodes.filter(node => node.isCenter).map(node => { return node.photoId })
    // 预处理连线数据
    const { nodeLinkMap, linkGroups } = preprocessMultiLinks(data.links, centerNodesId);

    // 计算多中心布局
    const layout = await layoutMultiCenterGraph({
        nodes: data.nodes,
        centers: centerNodesId,
        container: container.value,
        linkThreshold: props.linkThreshold
    });

    // 创建配置管理器
    const configManager = new ConfigManager({});

    // 渲染多中心图形
    renderMultiCenterGraph({
        svg: svg.value,
        nodes: layout.nodes,
        centers: layout.centers,
        nodeGroups: layout.nodeGroups,
        linkGroups,
        configManager
    });
}
</script>