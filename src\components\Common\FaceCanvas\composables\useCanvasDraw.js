/*
 * @Author: CaiXiaomin
 * @Date: 2025-07-17 15:51:48
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-18 09:08:59
 * @FilePath: \platform-face-web\src\composables\useCanvasDraw.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import { ref } from 'vue';

export function useCanvasDraw() {
  const isDrawing = ref(false);
  const startPoint = ref({ x: 0, y: 0 });
  const currentRect = ref({ x: 0, y: 0, width: 0, height: 0 });

  // 鼠标按下：记录起点并进入绘制状态
  const onMouseDown = (event) => {
    const rect = event.currentTarget.getBoundingClientRect();
    startPoint.value = { x: event.clientX - rect.left, y: event.clientY - rect.top };
    isDrawing.value = true;
  };

  // 鼠标移动：更新当前矩形尺寸
  const onMouseMove = (event) => {
    if (!isDrawing.value) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const currentX = event.clientX - rect.left;
    const currentY = event.clientY - rect.top;
    const x = Math.min(startPoint.value.x, currentX);
    const y = Math.min(startPoint.value.y, currentY);
    const width = Math.abs(currentX - startPoint.value.x);
    const height = Math.abs(currentY - startPoint.value.y);

    currentRect.value = { x, y, width, height };
  };

  // 鼠标松开：结束绘制，返回最终框信息
  const onMouseUp = () => {
    if (!isDrawing.value) return null;

    isDrawing.value = false;

    const newRect = { ...currentRect.value };
    // 设置阈值 5 像素
    const minSize = 5;
    if (Math.abs(newRect.width) < minSize || Math.abs(newRect.height) < minSize) {
      // 尺寸太小，认为是无效的绘制，直接返回 null 或重置状态
      currentRect.value = null;
      return null;
    }

    // 重置 currentRect
    currentRect.value = { x: 0, y: 0, width: 0, height: 0 };

    return newRect;
  };

  return {
    isDrawing,
    currentRect,
    onMouseDown,
    onMouseMove,
    onMouseUp,
  };
}
