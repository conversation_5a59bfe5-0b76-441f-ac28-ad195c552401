/**
 * @Author: yuzhouisme
 * @Date: 2025-07-24 10:39:04
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 14:40:37
 * @FilePath: \platform-face-web\src\components\Business\TagSelector\utils\index.js
 * @Description: TagSelector 工具函数
 */

/**
 * 标签类型常量
 */
export const TAG_TYPES = ['default', 'primary', 'success', 'warning', 'error'];

/**
 * 默认配置
 */
export const DEFAULT_CONFIG = {
  placeholder: '请输入标签名称',
  debounceDelay: 300,
  tagType: 'default',
  uniqueKey: 'id',
  allowCreate: true,
  maxTags: 0
};

/**
 * 检查标签是否已存在
 * @param {Array} tags - 已选标签数组
 * @param {Object} tag - 要检查的标签
 * @param {String} uniqueKey - 唯一标识字段
 * @returns {Boolean} 是否已存在
 */
export function isTagExists(tags, tag, uniqueKey = 'id') {
  return tags.some(item => item[uniqueKey] === tag[uniqueKey]);
}

/**
 * 检查标签名称是否已存在
 * @param {Array} tags - 已选标签数组
 * @param {String} tagName - 标签名称
 * @returns {Boolean} 是否已存在
 */
export function isTagNameExists(tags, tagName) {
  return tags.some(item => item?.name === tagName);
}

/**
 * 过滤已选择的标签
 * @param {Array} suggestions - 建议标签数组
 * @param {Array} selectedTags - 已选标签数组
 * @param {String} uniqueKey - 唯一标识字段
 * @returns {Array} 过滤后的建议标签
 */
export function filterSelectedTags(suggestions, selectedTags, uniqueKey = 'id') {
  if (!Array.isArray(suggestions) || !Array.isArray(selectedTags)) {
    return [];
  }
  
  const selectedIds = selectedTags.map(tag => tag[uniqueKey]);
  return suggestions.filter(item => !selectedIds.includes(item[uniqueKey]));
}

/**
 * 创建临时标签对象
 * @param {String} tagName - 标签名称
 * @param {String} uniqueKey - 唯一标识字段
 * @returns {Object} 临时标签对象
 */
export function createTempTag(tagName, uniqueKey = 'id') {
  return {
    [uniqueKey]: `temp_${Date.now()}`,
    name: tagName,
    isTemp: true
  };
}

/**
 * 验证标签数组
 * @param {*} value - 要验证的值
 * @returns {Boolean} 是否为有效的标签数组
 */
export function validateTagArray(value) {
  return Array.isArray(value);
}

/**
 * 验证函数类型
 * @param {*} value - 要验证的值
 * @returns {Boolean} 是否为函数
 */
export function validateFunction(value) {
  return typeof value === 'function';
}

/**
 * 验证标签类型
 * @param {String} value - 标签类型
 * @returns {Boolean} 是否为有效的标签类型
 */
export function validateTagType(value) {
  return TAG_TYPES.includes(value);
}

/**
 * 验证非负数
 * @param {Number} value - 要验证的数值
 * @returns {Boolean} 是否为非负数
 */
export function validateNonNegativeNumber(value) {
  return typeof value === 'number' && value >= 0;
}

/**
 * 检查是否可以添加更多标签
 * @param {Array} currentTags - 当前标签数组
 * @param {Number} maxTags - 最大标签数量
 * @returns {Boolean} 是否可以添加更多标签
 */
export function canAddMoreTags(currentTags, maxTags) {
  return !maxTags || currentTags.length < maxTags;
}
