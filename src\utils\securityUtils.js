import CryptoJS from 'crypto-js';
import DOMPurify from 'dompurify';

export const AES = {
  encrypt: (data, key, iv) => {
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const ivHex = CryptoJS.enc.Utf8.parse(iv);
    const encrypted = CryptoJS.AES.encrypt(data, keyHex, {
      iv: ivHex,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
  },

  decrypt: (encryptedData, key, iv) => {
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const ivHex = CryptoJS.enc.Utf8.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(
      { ciphertext: CryptoJS.enc.Base64.parse(encryptedData) },
      keyHex,
      {
        iv: ivHex,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      }
    );
    return decrypted.toString(CryptoJS.enc.Utf8);
  },
};

/** SHA1 加密工具 */
export const SHA1 = {
  encrypt: (data) =>
    CryptoJS.SHA1(data).toString(CryptoJS.enc.Hex).substring(0, 16),
};

/** Token 生成工具 */
export const TokenContext = {
  createToken: (accessKey, secretKey, iv) => {
    const timestamp = `${new Date().getTime()}`; // 使用 ISO 时间戳
    const encryptedSign = AES.encrypt(timestamp, secretKey, iv);
    return `${accessKey}${encryptedSign}`;
  },
};

/** Base64 编解码工具 */
export const Base64 = {
  encode: (data) =>
    CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data)),
  decode: (base64Str) =>
    CryptoJS.enc.Base64.parse(base64Str).toString(CryptoJS.enc.Utf8),
};


// v-html防止被XSS攻击经过sanitizeHtml处理
export const sanitizeHtml = (dirtyHtml) => {
  return DOMPurify.sanitize(dirtyHtml, {
    ALLOWED_TAGS: [
      'br', // 允许换行标签
      'strong', 'em', 'code', 'a', 'img', // 其他允许的标签
      'div', 'span', 'p', 'think', // 结构标签
    ],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'class', 'target'],
    FORBID_TAGS: ['script', 'style', 'iframe'],
    FORBID_ATTR: ['onclick', 'onload'],
  });
}