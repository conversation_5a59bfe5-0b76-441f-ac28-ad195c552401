# 通用上传组件（Index.vue）

## 组件简介

该组件用于统一平台内的文件/图片上传，支持自定义上传接口、参数、回调、回显及上传区域内容，满足多场景需求。

---

## 基本用法

```vue
<Upload
  :action="'/api/upload'"
  :headers="{ Authorization: 'Bearer token' }"
  :data="{ type: 'avatar' }"
  type="image"
  :multiple="false"
  :beforeUpload="beforeUploadFn"
  :onSuccess="handleSuccess"
  :onError="handleError"
  :onChange="handleChange"
  :previewList="fileList"
>
  <!-- 可自定义上传区域内容 -->
  <template #default>
    <button>自定义上传按钮</button>
  </template>
  <!-- 可自定义回显区域内容 -->
  <template #preview="{ list, type }">
    <div v-for="item in list" :key="item.url">
      <span>{{ item.name }}</span>
    </div>
  </template>
</Upload>
```

---

## Props 参数

| 参数         | 说明                                   | 类型     | 默认值    |
| ------------ | -------------------------------------- | -------- | --------- |
| action       | 上传接口地址，若不传需用beforeUpload自定义 | String   | -         |
| headers      | 上传请求头                             | Object   | {}        |
| data         | 额外上传参数                           | Object   | {}        |
| accept       | 允许上传的文件类型，优先于type          | String   | ''        |
| type         | 上传类型：'all'（全部）、'image'（图片）、'file'（文件） | String   | 'all'     |
| multiple     | 是否多文件上传                         | Boolean  | false     |
| beforeUpload | 上传前钩子/自定义上传方法               | Function | -         |
| onSuccess    | 上传成功回调                           | Function | -         |
| onError      | 上传失败回调                           | Function | -         |
| onChange     | 上传状态变化回调                       | Function | -         |
| previewList  | 回显文件/图片列表，建议含url和name      | Array    | []        |

---

## 插槽 Slots

| 名称      | 说明                         | 参数                |
| --------- | ---------------------------- | ------------------- |
| default   | 上传区域内容（可自定义按钮/框）| -                   |
| preview   | 回显区域内容（自定义回显样式）| { list, type }      |

- 默认上传区域：
  - type='image' 时为带+号的方框
  - 其他为按钮
- 默认回显区域：
  - type='image' 时为图片缩略图
  - 其他为文件名及下载链接

---

## 回显说明

- 通过 `previewList` 传入已上传文件/图片列表，数组元素建议包含：
  - `url`：文件/图片地址
  - `name`：文件/图片名称
- 可通过 `#preview` 插槽自定义回显内容

---

## beforeUpload 用法

- 若未传 action，必须用 beforeUpload 完成自定义上传（如手动调用API并处理回调）
- beforeUpload 返回 false 可中断上传
- beforeUpload 可返回 Promise，resolve false 也会中断

---

## 示例

### 1. 图片上传（带回显）
```vue
<Upload
  type="image"
  :previewList="imgList"
  :beforeUpload="customUpload"
>
  <template #default>
    <div style="width:100px;height:100px;border:1px dashed #aaa;display:flex;align-items:center;justify-content:center;">自定义+</div>
  </template>
</Upload>
```

### 2. 文件上传（自定义按钮）
```vue
<Upload
  type="file"
  :beforeUpload="customUpload"
>
  <template #default>
    <button>上传文件</button>
  </template>
</Upload>
```

---

## 样式说明

- 默认样式简洁，支持自定义。可通过插槽完全自定义上传入口和回显区域。
- 可根据实际需求自行扩展样式。

---

## 其他说明

- 支持多文件上传（multiple=true）
- 支持自定义 accept 类型
- 支持 action 或 beforeUpload 两种上传方式
- 支持回显和自定义回显

如有更多需求请补充 props 或插槽。 