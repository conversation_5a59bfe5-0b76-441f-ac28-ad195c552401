<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaceCanvas 自定义样式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .code-block {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FaceCanvas 自定义样式功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">✅ 实现完成的功能</div>
            <div class="description">
                我们已经成功为 FaceCanvas 组件添加了自定义样式功能，具体包括：
            </div>
            
            <h3>1. FaceCanvas 组件新增 Props</h3>
            <div class="code-block">
/** 自定义人脸框样式配置 */
customFaceBoxStyle: {
  type: Object,
  default: () => null,
  validator: (value) => {
    if (!value) return true;
    const validKeys = ['borderColor', 'borderWidth', 'borderStyle', 'showSelection', 'showIndex', 'backgroundColor'];
    return Object.keys(value).every(key => validKeys.includes(key));
  }
}
            </div>
            
            <h3>2. FaceBox 组件支持的自定义样式选项</h3>
            <div class="code-block">
{
  borderColor: '#00E875',        // 边框颜色（绿色）
  borderWidth: '2px',            // 边框宽度
  borderStyle: 'solid',          // 边框样式（实线）
  showSelection: false,          // 不显示选择状态图标
  showIndex: false,             // 不显示索引号
  backgroundColor: 'transparent' // 背景颜色（透明）
}
            </div>
            
            <h3>3. 在 libraryFace/Index.vue 中的使用方式</h3>
            <div class="code-block">
&lt;FaceCanvas 
  :imageSrc="getImageWebURL(imageId)" 
  :faceBoxes="faceBoxes" 
  :editable="false"
  <span class="highlight">:customFaceBoxStyle="customFaceBoxStyle"</span>
&gt;
&lt;/FaceCanvas&gt;

// 在 script 中定义
const customFaceBoxStyle = ref({
  borderColor: '#00E875',      // 绿色边框
  borderWidth: '2px',          // 边框宽度
  borderStyle: 'solid',        // 实线边框
  showSelection: false,        // 不显示选择状态图标
  showIndex: false,           // 不显示索引号
  backgroundColor: 'transparent' // 透明背景
});
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🎯 实现的具体效果</div>
            <div class="description">
                根据您的需求，现在 FaceCanvas 组件将会：
                <ul>
                    <li><strong>显示绿色边框</strong>：所有人脸框都会显示绿色的实线边框</li>
                    <li><strong>隐藏选择状态</strong>：不会显示选中时的对勾图标</li>
                    <li><strong>隐藏索引号</strong>：不会显示人脸框的序号</li>
                    <li><strong>保持透明背景</strong>：人脸框内部保持透明</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 技术实现细节</div>
            <div class="description">
                <h4>1. 组件架构改进</h4>
                <ul>
                    <li>在 FaceCanvas 主组件中添加了 <code>customFaceBoxStyle</code> prop</li>
                    <li>将自定义样式传递给所有 FaceBox 子组件</li>
                    <li>在 FaceBox 组件中添加了 <code>customStyle</code> prop</li>
                </ul>
                
                <h4>2. 样式优先级</h4>
                <ul>
                    <li>自定义样式具有最高优先级</li>
                    <li>如果提供了自定义样式，将覆盖默认的自动检测和手动绘制样式</li>
                    <li>保持向后兼容性，不影响现有功能</li>
                </ul>
                
                <h4>3. 显示逻辑控制</h4>
                <ul>
                    <li>添加了 <code>shouldShowCheckIcon</code> 计算属性控制选择图标显示</li>
                    <li>添加了 <code>shouldShowIndex</code> 计算属性控制索引号显示</li>
                    <li>在模板中使用这些计算属性替代原有的直接判断</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">🚀 如何测试</div>
            <div class="description">
                要测试这个功能，请按以下步骤操作：
                <ol>
                    <li>启动开发服务器：<code>pnpm dev</code> 或 <code>npm run dev</code></li>
                    <li>导航到人脸检测页面（libraryFace）</li>
                    <li>上传一张包含人脸的图片</li>
                    <li>点击"开始检测"按钮</li>
                    <li>观察检测结果中的人脸框是否显示为绿色边框，且没有选择状态和索引号</li>
                </ol>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📝 其他自定义样式示例</div>
            <div class="description">
                您也可以尝试其他样式配置：
            </div>
            
            <div class="code-block">
// 红色虚线边框，显示索引但不显示选择状态
const redDashedStyle = {
  borderColor: '#ff4d4f',
  borderWidth: '1px',
  borderStyle: 'dashed',
  showSelection: false,
  showIndex: true,
  backgroundColor: 'rgba(255, 77, 79, 0.1)'
};

// 蓝色粗边框，完全隐藏所有指示器
const blueThickStyle = {
  borderColor: '#1890ff',
  borderWidth: '3px',
  borderStyle: 'solid',
  showSelection: false,
  showIndex: false,
  backgroundColor: 'transparent'
};
            </div>
        </div>
    </div>
</body>
</html>
