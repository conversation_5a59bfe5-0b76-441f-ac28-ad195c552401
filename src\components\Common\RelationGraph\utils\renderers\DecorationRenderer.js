/**
 * 装饰元素渲染器 - 负责渲染节点的装饰元素
 * 包括TOP标签、算法圆环、分数指示器等
 */

/**
 * TOP标签渲染器
 */
export class TopLabelRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染TOP标签
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     * @param {number} baseRadius - 基础半径
     */
    render(container, node) {
        const labelConfig = this.configManager.get('nodes.other.topLabel');

        if (!labelConfig.enabled) {
            return null;
        }

        const rank = node.scoreRank;
        const showCondition = labelConfig.showCondition || ((rank) => rank < 4);

        if (!showCondition(rank)) {
            return null;
        }

        // 计算标签位置
        const centerRadius = this.configManager.get('nodes.center.size.radius');
        const maxScore = Math.max(...node.scoreData.map(item => item.score));
        const yPosition = centerRadius * maxScore + (labelConfig.offsetY || 20);

        // 格式化标签文本
        const format = labelConfig.format || 'TOP{rank}';
        const text = format.replace('{rank}', rank);

        return container
            .append('text')
            .attr('y', yPosition)
            .attr('text-anchor', 'middle')
            .attr('font-size', labelConfig.fontSize || '12px')
            .attr('fill', labelConfig.fill || '#fff')
            .attr('font-weight', labelConfig.fontWeight || 'normal')
            .text(text);
    }
}

/**
 * 算法圆环渲染器
 */
export class AlgorithmRingRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染算法圆环
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     */
    render(container, node) {
        if (!node.scoreData || !Array.isArray(node.scoreData)) {
            return [];
        }

        const centerRadius = this.configManager.get('nodes.center.size.radius');
        const scaleConfig = this.configManager.get('nodes.other.scoreScale');
        const ringsConfig = this.configManager.get('nodes.other.algorithmRings');

        // 检查是否启用算法圆环
        if (!ringsConfig.enabled || ringsConfig.mode === 'none') {
            return [];
        }

        const sortedScoreData = [...node.scoreData].sort((a, b) => b.score - a.score);

        // 确定缩放比例
        const scale = node.scoreRank > scaleConfig.threshold ? scaleConfig.smallScale : scaleConfig.normalScale;

        const rings = [];

        // 根据模式决定渲染哪些圆环
        let dataToRender = [];

        switch (ringsConfig.mode) {
            case 'multiple':
                // 显示所有算法的圆环
                dataToRender = sortedScoreData;
                break;

            case 'single':
                // 只显示一个圆环
                if (ringsConfig.singleRingAlgorithm === 'highest') {
                    dataToRender = [sortedScoreData[0]]; // 最高分
                } else {
                    // 查找指定算法
                    const targetAlgorithm = sortedScoreData.find(
                        item => item.algorithmCode === ringsConfig.singleRingAlgorithm
                    );
                    if (targetAlgorithm) {
                        dataToRender = [targetAlgorithm];
                    }
                }
                break;

            default:
                dataToRender = sortedScoreData;
        }

        // 渲染选定的圆环
        dataToRender.forEach((scoreItem) => {
            const radius = centerRadius * scoreItem.score * scale;
            const color = this.configManager.getAlgorithmColor(scoreItem.algorithmCode);
            const styleConfig = this.configManager.get('nodes.other.style');

            const ring = container
                .append('circle')
                .attr('r', radius)
                .attr('fill', styleConfig.fill || '#16171c')
                .attr('stroke', color)
                .attr('stroke-width', styleConfig.strokeWidth || 1)
                .attr('class', `algorithm-ring ${scoreItem.algorithmCode}`)
                .datum(scoreItem); // 绑定数据以便后续使用

            rings.push(ring);
        });

        return rings;
    }
}

/**
 * 分数指示器渲染器
 */
export class ScoreIndicatorRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染分数指示器（可选的视觉元素）
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     */
    render(container, node) {
        const indicatorConfig = this.configManager.get('nodes.other.scoreIndicator');

        if (!indicatorConfig || !indicatorConfig.enabled) {
            return [];
        }

        const indicators = [];
        const centerRadius = this.configManager.get('nodes.center.size.radius');

        node.scoreData.forEach((scoreItem, index) => {
            const angle = (index * 2 * Math.PI) / node.scoreData.length;
            const radius = centerRadius * scoreItem.score * 1.1; // 稍微外一点
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;

            const indicator = container
                .append('circle')
                .attr('cx', x)
                .attr('cy', y)
                .attr('r', indicatorConfig.radius || 3)
                .attr('fill', this.configManager.getAlgorithmColor(scoreItem.algorithmCode))
                .attr('opacity', indicatorConfig.opacity || 0.8);

            indicators.push(indicator);
        });

        return indicators;
    }
}

/**
 * 节点标签渲染器
 */
export class NodeLabelRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染节点标签（姓名等）
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     */
    render(container, node) {
        const labelConfig = this.configManager.get('nodes.other.nameLabel');

        if (!labelConfig || !labelConfig.enabled) {
            return null;
        }

        const name = node.portrait?.xm || node.name || '';
        if (!name) {
            return null;
        }

        const centerRadius = this.configManager.get('nodes.center.size.radius');
        const maxScore = Math.max(...node.scoreData.map(item => item.score));
        const yPosition = -(centerRadius * maxScore + (labelConfig.offsetY || 25));

        return container
            .append('text')
            .attr('y', yPosition)
            .attr('text-anchor', 'middle')
            .attr('font-size', labelConfig.fontSize || '10px')
            .attr('fill', labelConfig.fill || '#fff')
            .attr('font-weight', labelConfig.fontWeight || 'normal')
            .text(name);
    }
}

/**
 * 装饰元素渲染器工厂
 */
export class DecorationRendererFactory {
    constructor(configManager) {
        this.configManager = configManager;
        this.renderers = {
            topLabel: new TopLabelRenderer(configManager),
            algorithmRing: new AlgorithmRingRenderer(configManager),
            scoreIndicator: new ScoreIndicatorRenderer(configManager),
            nodeLabel: new NodeLabelRenderer(configManager)
        };
    }

    /**
     * 获取指定类型的装饰渲染器
     * @param {string} type - 装饰类型
     */
    getRenderer(type) {
        return this.renderers[type];
    }

    /**
     * 渲染所有启用的装饰元素
     * @param {d3.Selection} container - D3容器
     * @param {Object} node - 节点数据
     * @param {string} nodeType - 节点类型
     */
    renderDecorations(container, node, nodeType = 'other') {
        const decorations = {};

        // 渲染算法圆环（对于其他节点）
        if (nodeType === 'other' && node.scoreData) {
            decorations.algorithmRings = this.renderers.algorithmRing.render(container, node);
        }

        // 渲染TOP标签
        if (nodeType === 'other') {
            decorations.topLabel = this.renderers.topLabel.render(container, node);
        }

        // 渲染分数指示器（如果启用）
        decorations.scoreIndicators = this.renderers.scoreIndicator.render(container, node);

        // 渲染节点标签（如果启用）
        decorations.nodeLabel = this.renderers.nodeLabel.render(container, node);

        return decorations;
    }
}
