/**
 * 结果过滤器组件配置
 * 
 * @description 定义不同类型结果过滤器的配置
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @lastModified 2025/07/23
 */

// MoreScoresResultFilter 配置
export const moreScoresConfig = {
  leftSection: {
    icon: 'portrait-icon',
    title: '张三'
  },
  trapezoidSection: {
    showPortraitGroup: true,
    portraitGroupLabel: '所在人像库'
  },
  statsSection: {
    items: [
      { label: '入库照片', key: 'stockPhotosNum', defaultValue: 0 },
      { label: '关联事件', key: 'relatedEvents', defaultValue: 0 },
      { label: '关系人', key: 'relatedPersons', defaultValue: 0 },
      { label: '亲属信息', key: 'familyInfo', defaultValue: 0 }
    ]
  },
  showFilterButton: true
};

// ImageToLibraryResultFilter 配置
export const imageToLibraryConfig = {
  leftSection: {
    text: '设定比对策略'
  },
  trapezoidSection: {
    showPortraitGroup: true,
    portraitGroupLabel: '人像组'
  },
  showAlgorithmSelect: true,
  algorithmLabel: '比对算法',
  showResultsSelect: true,
  resultsLabel: '返回结果',
  showThresholdSelect: true,
  thresholdLabel: '设定阈值'
};

// ImageToImageResultFilter 配置
export const imageToImageConfig = {
  trapezoidSection: {
    customButton: {
      buttonBg: '#1d2735',
      hoverBg: '#2a3647',
      fontColor: '#ffffff',
      hoverFontColor: '#ffffff',
      iconName: 'identification-high',
      text: '一键身份识别 | 分组'
    },
    bgColor: '#1d2735'
  },
  customButtons: [
    {
      buttonBg: '#24394e',
      hoverBg: '#2a4a5f',
      fontColor: '#B0BBCC',
      hoverFontColor: '#B0BBCC',
      iconName: 'intraocular-check-high',
      text: '人工核对 | 纠错'
    }
  ],
  showThresholdSelect: true,
  thresholdLabel: '同人预判阈值'
};
