# AlgorithmScoreItem 算法得分项组件

## 📖 组件概述

AlgorithmScoreItem 是一个用于显示算法得分信息的轻量级组件。它以简洁的方式展示算法得分、算法代码和算法名称，支持自定义样式配置，常用于比对结果展示、算法性能对比等场景。

## ✨ 主要功能

- 🎯 **得分显示**：自动格式化显示算法得分，保留指定小数位数
- 🏷️ **算法标识**：显示算法代码的简称标签，带有边框样式
- 📝 **算法名称**：显示完整的算法名称或简称
- 🎨 **样式定制**：支持字体大小、标签尺寸等样式自定义
- 🔄 **智能映射**：自动将算法代码和名称映射为简称显示

## 🏗️ 组件结构

```
AlgorithmScoreItem
├── 算法得分 (algorithm-score)
├── 算法代码标签 (algorithm-code) 
└── 算法名称 (algorithm-name)
```

## 📋 Props 参数

### 必需参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `score` | String \| Number | - | 算法得分值（必需） |
| `code` | String | - | 算法代码（必需） |
| `name` | String | - | 算法名称（必需） |

### 样式配置

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `fontSize` | String | `'13px'` | 整体字体大小 |
| `codeFontSize` | String | `'11px'` | 算法代码标签字体大小 |
| `codeHeight` | String | `'13px'` | 算法代码标签高度 |
| `codePadding` | String | `'0 4px'` | 算法代码标签内边距 |

## 🎨 算法映射配置

组件使用预定义的算法简称映射：

```javascript
const algorithmShortNames = {
  ci_an: '融',  // 融合算法
  an: '正',     // 正脸算法  
  ci: '蒙',     // 蒙脸算法
};
```

## 🚀 使用方式

### 1. 基础用法

```vue
<template>
  <!-- 显示融合算法得分 -->
  <AlgorithmScoreItem
    :score="0.8567"
    code="ci_an"
    name="融合算法" />
</template>
```

### 2. 自定义样式

```vue
<template>
  <!-- 自定义字体大小和标签样式 -->
  <AlgorithmScoreItem
    :score="0.9234"
    code="an"
    name="正脸算法"
    font-size="16px"
    code-font-size="12px"
    code-height="16px"
    code-padding="2px 6px" />
</template>
```

### 3. 批量显示多个算法得分

```vue
<template>
  <div class="algorithm-scores">
    <AlgorithmScoreItem
      v-for="item in algorithmScores"
      :key="item.code"
      :score="item.score"
      :code="item.code"
      :name="item.name" />
  </div>
</template>

<script setup>
const algorithmScores = [
  { score: 0.8567, code: 'ci_an', name: '融合算法' },
  { score: 0.7234, code: 'an', name: '正脸算法' },
  { score: 0.6789, code: 'ci', name: '蒙脸算法' }
];
</script>

<style scoped>
.algorithm-scores {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
</style>
```

### 4. 在比对结果中使用

```vue
<template>
  <div class="comparison-result">
    <div class="portrait-info">
      <img :src="portrait.imageUrl" alt="人像" />
      <h3>{{ portrait.name }}</h3>
    </div>
    
    <div class="algorithm-scores">
      <AlgorithmScoreItem
        v-for="scoreData in portrait.scores"
        :key="scoreData.algorithmCode"
        :score="scoreData.score"
        :code="scoreData.algorithmCode"
        :name="scoreData.algorithmName"
        font-size="14px" />
    </div>
  </div>
</template>
```

### 5. 响应式布局

```vue
<template>
  <div class="responsive-scores">
    <AlgorithmScoreItem
      v-for="item in scores"
      :key="item.code"
      :score="item.score"
      :code="item.code"
      :name="item.name"
      :font-size="isMobile ? '12px' : '14px'"
      :code-font-size="isMobile ? '10px' : '11px'"
      :code-height="isMobile ? '12px' : '13px'" />
  </div>
</template>

<script setup>
import { computed } from 'vue';

const isMobile = computed(() => window.innerWidth < 768);
</script>
```

## 🎯 显示效果

组件渲染后的典型显示效果：

```
0.86  [融]  融合算法
0.72  [正]  正脸算法  
0.68  [蒙]  蒙脸算法
```

其中：
- `0.86` - 格式化后的得分（保留2位小数）
- `[融]` - 带边框的算法代码简称标签
- `融合算法` - 算法名称或简称

## 🎨 样式说明

### 默认样式
- **整体布局**：水平排列，居中对齐，4px间距
- **得分文字**：白色文字，13px字体
- **代码标签**：蓝色边框和文字（#6FCBFF），圆角边框
- **算法名称**：白色文字，13px字体

### 自定义样式变量
组件使用CSS变量绑定，支持动态样式：
- `v-bind(codePadding)` - 标签内边距
- `v-bind(codeHeight)` - 标签高度
- `v-bind(codeFontSize)` - 标签字体大小

## 🔧 数据处理

### 得分格式化
使用 `toFixedDecimal` 工具函数处理得分显示：
- 自动保留2位小数
- 处理无效数值，显示为 `0.00`
- 支持字符串和数字类型输入

### 算法名称映射
- 优先使用 `algorithmShortNames` 映射的简称
- 如果映射不存在，显示原始的 `code` 或 `name`
- 支持扩展新的算法类型

## 💡 最佳实践

1. **统一数据格式**：确保传入的得分数据格式一致
2. **合理使用简称**：在空间受限时使用算法简称
3. **响应式设计**：根据屏幕尺寸调整字体大小
4. **批量展示**：使用 `v-for` 循环展示多个算法得分
5. **样式一致性**：在同一页面中保持样式参数一致

## 🔧 依赖说明

### 工具函数
- `toFixedDecimal` - 来自 `@/utils/tool`，用于格式化小数

### 配置文件  
- `algorithmShortNames` - 来自 `@/api/algorithm.js`，算法简称映射

### Vue 依赖
- `computed` - Vue 3 响应式计算属性

## 🌟 使用场景

- ✅ **比对结果展示**：显示人脸比对的算法得分
- ✅ **算法性能对比**：对比不同算法的表现
- ✅ **数据统计面板**：在仪表盘中展示算法指标
- ✅ **搜索结果列表**：在搜索结果中显示相似度得分
- ✅ **报告生成**：在报告中展示算法分析结果

## 📝 注意事项

1. **数据有效性**：确保传入的 `score`、`code`、`name` 参数有效
2. **样式兼容性**：CSS变量绑定需要现代浏览器支持
3. **算法映射**：新增算法类型需要更新 `algorithmShortNames` 配置
4. **性能考虑**：大量数据展示时注意虚拟滚动优化

## 🔄 版本信息

- **作者**: yuzhouisme, CaiXiaomin  
- **创建日期**: 2025-04-02
- **最后修改**: 2025-07-23
- **依赖**: Vue 3.x, Less
