import { message, notification } from 'ant-design-vue';

const DEFAULT_DURATION = 3; // 默认展示时间

const shownMessages = new Set();

export const Message = {
  success(content, duration = DEFAULT_DURATION) {
    if (shownMessages.has(content)) return;
    shownMessages.add(content);
    message.success(content, duration).then(() => {
      shownMessages.delete(content);
    });
  },
  error(content, duration = DEFAULT_DURATION) {
    if (shownMessages.has(content)) return;
    shownMessages.add(content);
    message.error(content, duration).then(() => {
      shownMessages.delete(content);
    });
  },
  info(content, duration = DEFAULT_DURATION) {
    if (shownMessages.has(content)) return;
    shownMessages.add(content);
    message.info(content, duration).then(() => {
      shownMessages.delete(content);
    });
  },
  warning(content, duration = DEFAULT_DURATION) {
    if (shownMessages.has(content)) return;
    shownMessages.add(content);
    message.warning(content, duration).then(() => {
      shownMessages.delete(content);
    });
  },
  loading(content, duration = DEFAULT_DURATION) {
    if (shownMessages.has(content)) return;
    shownMessages.add(content);
    message.loading(content, duration).then(() => {
      shownMessages.delete(content);
    });
  },
};

export const Notification = {
  success({ message: msg, description, duration = DEFAULT_DURATION }) {
    return notification.success({ message: msg, description, duration });
  },
  error({ message: msg, description, duration = DEFAULT_DURATION }) {
    return notification.error({ message: msg, description, duration });
  },
  info({ message: msg, description, duration = DEFAULT_DURATION }) {
    return notification.info({ message: msg, description, duration });
  },
  warning({ message: msg, description, duration = DEFAULT_DURATION }) {
    return notification.warning({ message: msg, description, duration });
  },
};
