# demo.js 使用说明

## 1. 文件简介

demo.js 是一个基于 protobuf 的 API 调用示例，演示了如何在前端项目中通过 protobuf 协议与后端进行数据交互。主要包含人像相关接口的 POST/GET 请求示例。

## 2. 依赖环境

- 需要配合 [google-protobuf](https://www.npmjs.com/package/google-protobuf) 库使用。
- 依赖 `src/proto/portrait_service_pb.js` 由 protoc 工具生成的 protobuf 消息类型定义文件。
- 依赖 `src/utils/service.js` 中封装的 protobuf 请求工具（`protobuf` 对象）。
- 推荐使用现代前端构建工具（如 Vite、Webpack）支持 ES Module 语法。

## 3. service.js 说明

`src/utils/service.js` 封装了 axios 实例和常用 HTTP 方法（get/post/put/delete/patch），并对 protobuf 请求做了专门适配，提供了 `protobuf` 工具对象，便于前端与后端进行二进制协议通信。

### 主要功能
- axios 实例统一配置（baseURL、超时、拦截器等）
- 常规 HTTP 方法（http.get/post/put/delete/patch）
- protobuf 专用方法：
  - `protobuf.post(url, fields, RespType, MessageType, config)`
  - `protobuf.get(url, params, RespType, config)`
  - 自动序列化请求体、反序列化响应体
  - 支持自定义 headers、responseType

### Protobuf 用法说明
- `protobuf.post` 会自动用 `MessageType` 构造请求对象并序列化为二进制
- `protobuf.get` 会自动将响应二进制反序列化为 `RespType` 对象
- 传参时务必传类型（如 `Portrait`），不要传实例（如 `new Portrait()`）
- **注意：post 的参数顺序为 (url, fields, RespType, MessageType, config?)**

### 代码片段
```js
import { Portrait, PortraitList } from '@/proto/portrait_service_pb.js';
import { protobuf } from '@/utils/service';

// POST 示例
protobuf.post('/api/portrait', { name: '张三' }, PortraitList, Portrait).then(resp => {
  // resp 是反序列化后的 pb 对象
});

// GET 示例
protobuf.get('/api/portrait/list', { page: 1 }, PortraitList).then(resp => {
  // resp 是反序列化后的 pb 对象
});
```

## 4. protobuf 请求用法说明

### POST 请求
```js
import { Portrait, PortraitList } from '@/proto/portrait_service_pb.js';
import { protobuf } from '@/utils/service';

protobuf.post('/api/portrait', { name: '张三' }, PortraitList, Portrait).then(resp => {
  // resp 是反序列化后的 pb 对象
});
```

### GET 请求
```js
import { PortraitList } from '@/proto/portrait_service_pb.js';
import { protobuf } from '@/utils/service';

protobuf.get('/api/portrait/list', { page: 1 }, PortraitList).then(resp => {
  // resp 是反序列化后的 pb 对象
});
```

### 说明
- `protobuf.post(url, fields, RespType, MessageType, config?)`
  - `url`：接口地址
  - `fields`：请求体字段对象
  - `RespType`：响应体的 protobuf 类型（构造函数）
  - `MessageType`：请求体的 protobuf 类型（构造函数）
  - `config`：可选 axios 配置
- `protobuf.get(url, params, RespType, config?)`
  - `url`：接口地址
  - `params`：查询参数对象
  - `RespType`：响应体的 protobuf 类型（构造函数）
  - `config`：可选 axios 配置

## 5. 代码示例

```js
import { Portrait, PortraitList } from '@/proto/portrait_service_pb.js';
import { protobuf } from '@/utils/service';

// POST 示例
protobuf.post('/api/portrait', { name: '张三' }, PortraitList, Portrait).then(resp => {
  // resp 是反序列化后的 pb 对象
});

// GET 示例
protobuf.get('/api/portrait/list', { page: 1 }, PortraitList).then(resp => {
  // resp 是反序列化后的 pb 对象
});

// 导出接口示例
export const addLibraries = protobuf.post('/api/libraries', { name: '张三' }, PortraitList, Portrait).then(resp => {
  // resp 是反序列化后的 pb 对象
});
```

## 6. 常见问题与解决方案

### Q1: `TypeError: MessageType is not a constructor`
- **原因**：传入的不是 protobuf 类型构造函数，而是实例。
- **解决**：确保传入 `Portrait`、`PortraitList` 等类型，而不是 `new Portrait()` 的实例。

### Q2: `TypeError: ... is not a constructor` 或 `undefined`
- **原因**：`portrait_service_pb.js` 没有正确导出类型。
- **解决**：确保 `portrait_service_pb.js` 末尾有如下导出：
  ```js
  export const { Portrait, PortraitList, ... } = proto.portrait;
  ```

### Q3: `require is not defined` 或 `exports is not defined`
- **原因**：模块导出/导入方式与项目构建工具不兼容。
- **解决**：统一使用 ES Module 语法（`import`/`export`），并用 Vite/Webpack 打包。

## 7. 维护者信息

- 作者：gzr (<EMAIL>)
- 最后更新时间：2025-07-16 