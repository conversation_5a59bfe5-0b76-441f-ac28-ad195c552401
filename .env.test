###
 # @Author: gzr gzrde<PERSON>@163.com
 # @Date: 2025-07-14 09:36:26
 # @LastEditors: gzr <EMAIL>
 # @LastEditTime: 2025-07-14 09:37:45
 # @FilePath: \platform-face-web\.env.test
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
# 为了防止意外地将一些环境变量泄漏到客户端，只有以 VITE_ 为前缀的变量才会暴露给经过 vite 处理的代码。
# js中通过`import.meta.env.VITE_API_BASE_URL`取值

#端口号
VITE_APP_PORT = 9999

# 接口前缀
VITE_API_BASE_URL = '/test-api'

# 后端服务地址
VITE_APP_SERVICE_API = 'http://localhost:888'

# 后端文件上传地址
#VITE_APP_BASE_FILE_API = '/test-api/web/api/system/file/upload'

# 是否删除debugger
VITE_DROP_DEBUGGER = false

# 是否删除console.log
VITE_DROP_CONSOLE = false

# 打包路径
VITE_BASE_PATH = ./

# 输出路径
VITE_OUT_DIR = dist-test

# 标题
VITE_APP_TITLE = 数智AI-人脸多模态-系统v1.0