<!--
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-30 16:55:17
 * @FilePath: \vue3-js-template-master\src\views\Home\index.vue
 * @Description: 这是一个示例页面，用于展示关系图的示例，不是正式页面
-->
<template>
  <div class="graph">
    <div class="button-group">
      <a-button @click="addGraph">添加新节点</a-button>
      <a-button @click="openInputDialog">AI输入框模块</a-button>
      <a-button @click="openBilateralDialog">查看双边组件示例</a-button>
    </div>
    <div id="container" ref="container"></div>

    <!-- InputDialog 组件展示弹窗 -->
    <a-modal v-model:open="inputDialogVisible" :title="inputDialogTitle" width="1600px" :mask-closable="true"
      :closable="true" ok-text="确定" cancel-text="取消" @ok="handleInputDialogOk" @cancel="handleInputDialogCancel">
      <AiInputArea />
    </a-modal>

    <a-modal v-model:open="bilateralDialogVisible" title="双边组件示例" width="1600px">
      <BilateralContentExample />
    </a-modal>
  </div>
</template>

<script setup>
import { Circle } from '@antv/g';
import { CubicHorizontal, ExtensionCategory, Graph, register, subStyleProps } from '@antv/g6';
import { reactive } from 'vue';
import { message } from 'ant-design-vue';
import AiInputArea from './components/AiInputArea.vue';
import BilateralContentExample from './components/BilateralContentExample.vue';

const COLOR_MAP = {
  normal: '#9DA6B4',
  success: '#2FAFA3',
  error: '#C14C4C',
  pending: '#7FB0F6',
  template: '#BBA4E2',
  other: '#DEA38A',
}

const container = ref(null);

// 输入框弹窗相关
const inputDialogVisible = ref(false)
const bilateralDialogVisible = ref(false)
const inputDialogTitle = ref('InputDialog 组件展示')

const openInputDialog = () => {
  inputDialogTitle.value = 'InputDialog 组件展示'
  inputDialogVisible.value = true
}
const openBilateralDialog = () => {
  bilateralDialogVisible.value = true
}
const handleInputDialogOk = () => {
  message.success('操作成功')
  inputDialogVisible.value = false
}
const handleInputDialogCancel = () => {
  inputDialogVisible.value = false
}
class FlyMarkerCubic extends CubicHorizontal {
  getMarkerStyle(attributes) {
    return { r: 2, fill: '#c3d5f9', offsetPath: this.shapeMap.key, ...subStyleProps(attributes, 'marker') };
  }

  onCreate() {
    super.onCreate && super.onCreate();
    const markerCount = 5;
    const markers = [];
    for (let i = 0; i < markerCount; i++) {
      const marker = this.upsert(
        `marker${i}`,
        Circle,
        {
          ...this.getMarkerStyle(this.attributes),
          offsetDistance: i / markerCount,
        },
        this
      );
      markers.push(marker);
    }
    // 全局动画
    let start = null;
    const duration = 5000;
    const animate = (timestamp) => {
      if (!start) start = timestamp;
      const progress = ((timestamp - start) % duration) / duration;
      for (let i = 0; i < markerCount; i++) {
        markers[i].attr('offsetDistance', (progress + i / markerCount) % 1);
      }
      requestAnimationFrame(animate);
    };
    requestAnimationFrame(animate);
  }
}
register(ExtensionCategory.EDGE, 'fly-marker-cubic', FlyMarkerCubic);
// nodeList：图中所有节点的数据列表，包含节点 id、类型、状态、名称等
const nodeList = reactive([
  {
    id: 'node-1',
    data: {
      location: 'TYPE-1',
      status: 'normal',
      name: '节点1',
    }
  },
  {
    id: 'node-2',
    data: {
      location: 'TYPE-2',
      status: 'success',
      name: '节点2',
      text: '展示隐藏内容',
    }
  },
  {
    id: 'node-3',
    data: {
      location: 'TYPE-3',
      status: 'error',
      name: '节点3',
      text: '展示隐藏内容',
    }
  },
  {
    id: 'node-4',
    data: {
      location: 'TYPE-4',
      status: 'template',
      name: '节点4',
      text: '展示隐藏内容',
    }
  },
  {
    id: 'node-5',
    data: {
      location: 'TYPE-5',
      status: 'other',
      name: '节点5',
      text: '展示隐藏内容',
    }
  },
])
// edgeList：图中所有边的数据列表，包含边 id、起点、终点等
const edgeList = reactive([
  { id: 'edge-1', source: 'node-1', target: 'node-2', text: '案例' },
  { id: 'edge-2', source: 'node-1', target: 'node-3', text: '案例' },
  { id: 'edge-3', source: 'node-1', target: 'node-4', text: '案例' },
  { id: 'edge-4', source: 'node-1', target: 'node-5', text: '案例' },
])
// graph：全局图实例对象
let graph = null
/**
 * 初始化关系图，配置节点、边、布局、交互等
 */
const initGraph = () => {
  const width = container.value.clientWidth;
  const height = container.value.clientHeight;
  graph = new Graph({
    container: container.value,
    width,
    height,
    animate: true,
    animateCfg: {
      duration: 500,
      easing: 'easeCubic',
    },
    data: {
      nodes: nodeList,
      edges: edgeList,
    },
    edge: {
      type: 'fly-marker-cubic',
      style: {
        lineDash: [10, 10],
        stroke: '#7B818C',
        labelText: (d) => d.text,         // 显示边的 id 作为 label
        labelFill: '#00A3FF',           // 文字颜色
        labelFontSize: 16,
        labelPadding: [20, 0, 0, 0],
        labelPlacement: 'center',
        labelFontWeight: 'bold',
        labelBackground: false,
        endArrow: false,
        badge: true,
        badgeFontFamily: 'iconfont',
        badgeBackgroundWidth: 12,
        badgeBackgroundHeight: 12,
        shadowColor: 'rgba(114, 46, 209, 0.3)',
        shadowBlur: 8,
        shadowOffsetX: 2,
        shadowOffsetY: 2,
      },
      animation: {
        update: [
          {
            fields: ["stroke"], // 更新时只对 stroke 属性进行动画
            duration: 1000, // 动画持续时间
            easing: "linear" // 缓动函数
          }
        ]
      }
    },
    node: {
      type: 'html',
      style: {
        size: [240, 80],
        dx: -100,
        dy: -40,
        innerHTML: (node) => {
          const { location, status } = node.data;

          const color = COLOR_MAP[status]
          // 进度条 HTML
          const progressBar = status === 'pending'
            ? `
              <div style="margin-top:8px;width:100%;display:flex;align-items:center;">
                <div id="progress-bar-${node.id}" style="flex:1;display:flex;gap:1px;height:10px;"></div>
                <span id="progress-text-${node.id}" style="margin-left:8px;font-size:12px;">0%</span>
              </div>
            `
            : '';
          return `
            <div style="
              width:100%; 
              height: max-content; 
              border: 1px solid ${color};
              color: #fff;
              user-select: none;
              border-radius: 8px;
              overflow: hidden;
              backdrop-filter: blur(10px);
              ">
              <div 
                style="
                  width:100%; 
                  height: 80px; 
                  background: ${color}bb; 
                  border: 1px solid ${color};
                  color: #fff;
                  user-select: none;
                  display: flex; 
                  padding: 10px;
                  transition: 0.3s all;
                  flex-direction:column;
                  justify-content:center;
                  "
                  ${status === 'pending' ? ' class="glow-text"' : ''}

                  id="click-node-${node.id}"
              >
                <div style="display: flex;flex-direction: column;flex: 1;">
                  <div style="font-weight: bold; font-size: 14px;">
                    ${location} Node
                  </div>
                  <div style="font-size: 12px; margin-top: 4px;">
                    status: ${status}
                  </div>
                  ${progressBar}
                </div>
                ${status === 'pending' ? '<span class="glow-bar"></span>' : ''}
              </div>
            </div>
            `
        }
      }
    },
    plugins: [
      {
        type: 'tooltip',
        trigger: 'click',
        enterable: true,
        getContent: (e, items) => {
          const item = items[0]
          return `
          <div>
            <h4 style="margin: 0 0 12px 0; color: #333;">用户操作</h4>
            <div style="margin-bottom: 8px; color: #666;">
              <strong>姓名:</strong> ${item.data.name}
            </div>
            <div style="margin-bottom: 12px; color: #666;">
              <strong>邮箱:</strong> ${item.data.email}
            </div>
            <div style="display: flex; gap: 8px;">
              <button onclick="alert('发送消息给 ${item.data.name}')"
                      style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                发消息
              </button>
              <button onclick="alert('查看 ${item.data.name} 的详情')"
                      style="padding: 4px 12px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                详情
              </button>
            </div>
          </div>
        `;
        },
      },
    ],
    layout: {
      type: 'dagre',
      nodeSep: 100,
      rankSep: 250,
      rankdir: 'LR'
    },
    behaviors: ['drag-canvas', 'zoom-canvas', 'drag-element'],
  });
  graph.render();
}
/**
 * 向图中动态添加一个新节点和一条边
 * 新节点 id 为 node-7，边 id 为 edge-10，连接 node-6 和 node-7
 */
const addGraph = async () => {
  addPendingNode()
  setTimeout(() => {
    addNodes()
  }, 4000)
};
const addPendingNode = async () => {
  graph.addData({
    nodes: [
      {
        id: 'node-6',
        data: {
          location: 'TYPE-6',
          status: 'pending',
          name: '正在思考中...',
        }
      },
    ],
    edges: [
      { id: 'edge-6', source: 'node-2', target: 'node-6', text: '汇总' },
      { id: 'edge-7', source: 'node-3', target: 'node-6', text: '汇总' },
      { id: 'edge-8', source: 'node-4', target: 'node-6', text: '汇总' },
      { id: 'edge-9', source: 'node-5', target: 'node-6', text: '汇总' },
    ]
  })
  setTimeout(() => {
    startProgressBar('node-6');
    startGlowBarAnimation('node-6');
  }, 500)
  await graph.layout(); // 重新布局
  await graph.draw();   // 重新绘制
}
const addNodes = async () => {
  graph.removeData({
    nodes: ['node-6'],
    edges: ['edge-6', 'edge-7', 'edge-8', 'edge-9'],
  })
  graph.addData({
    nodes: [
      {
        id: 'node-7',
        data: {
          location: 'TYPE-7',
          status: 'normal',
          name: '节点7',
        },
      },
      {
        id: 'node-8',
        data: {
          location: 'TYPE-8',
          status: 'normal',
          name: '节点8',
        },
      },
      {
        id: 'node-9',
        data: {
          location: 'TYPE-9',
          status: 'normal',
          name: '节点9',
        },
      },
      {
        id: 'node-10',
        data: {
          location: 'TYPE-10',
          status: 'normal',
          name: '节点10',
        },
      },
    ],
    edges: [
      {
        id: 'edge-10',
        source: 'node-3',
        target: 'node-7',
        text: '结论',
      },
      {
        id: 'edge-11',
        source: 'node-3',
        target: 'node-8',
        text: '结论',
      },
      {
        id: 'edge-12',
        source: 'node-3',
        target: 'node-9',
        text: '结论',
      },
      {
        id: 'edge-13',
        source: 'node-3',
        target: 'node-10',
        text: '结论',
      },

    ]
  })
  // await graph.layout();
  // await graph.draw();
  await graph.render()
}
// progressIntervals：用于存储每个节点进度条的定时器 id，便于后续清理
const progressIntervals = {};
/**
 * 启动节点进度条动画
 * @param {string} nodeId - 节点 id
 */
function startProgressBar(nodeId) {
  const totalSteps = 30;
  let current = 0;
  const bar = document.getElementById(`progress-bar-${nodeId}`);
  const text = document.getElementById(`progress-text-${nodeId}`);
  if (!bar || !text) return;
  // 初始化格子
  bar.innerHTML = '';
  for (let i = 0; i < totalSteps; i++) {
    const seg = document.createElement('div');
    seg.style.flex = '1';
    seg.style.height = '100%';
    seg.style.background = '#333';
    bar.appendChild(seg);
  }
  // 动画
  progressIntervals[nodeId] = setInterval(() => {
    if (current < totalSteps) {
      bar.children[current].style.background = '#0060F2'; // pending 颜色
      current++;
      text.textContent = `${Math.floor((current / totalSteps) * 100)}%`;
    } else {
      clearInterval(progressIntervals[nodeId]);
      text.textContent = '100%';
    }
  }, 100); // 2秒走满
}
/**
 * 启动节点高光条动画，实现无缝衔接
 * @param {string} nodeId - 节点 id
 */
function startGlowBarAnimation(nodeId) {
  const bar = document.querySelector(`#click-node-${nodeId} .glow-bar`);
  if (!bar) return;
  bar.style.position = 'absolute';
  bar.style.top = '0';
  bar.style.left = '0';
  bar.style.width = '200%';
  bar.style.height = '100%';
  bar.style.background = 'repeating-linear-gradient(120deg, \
  rgba(255,255,255,0) 0%, \
  rgba(255,255,255,0) 10%, \
  rgba(255,255,255,0) 20%, \
  rgba(255,255,255,0) 30%, \
  rgba(255,255,255,0) 50%, \
  rgba(255,255,255,0.2) 60%, \
  rgba(255,255,255,0.5) 70%, \
  rgba(255,255,255,0) 80%, \
  rgba(255,255,255,0) 100%)';
  bar.style.pointerEvents = 'none';
  bar.style.willChange = 'transform, opacity';
  const duration = 1500; // 动画时长（毫秒）

  // 1. 计算当前动画进度（0~1），用于无缝衔接
  // 如果之前有动画，bar._glowStart 记录了动画起始时间
  let prevProgress = 0;
  if (bar._glowStart) {
    const now = performance.now();
    prevProgress = ((now - bar._glowStart) % duration) / duration;
  }
  // 2. 立即设置 transform，保证视觉上停在上次动画的位置，防止闪跳
  bar.style.transform = `translateX(${(-100 + 100 * prevProgress)}%)`;

  // 3. 取消旧的动画帧，防止多重动画叠加
  if (bar._glowAnimId) cancelAnimationFrame(bar._glowAnimId);

  // 4. 新动画从当前进度继续
  let start = null;
  function animateBar(ts) {
    if (!start) {
      // 让动画从上次的进度继续（通过修正 start 时间）
      start = ts - prevProgress * duration;
      bar._glowStart = start; // 记录动画起始时间，供下次衔接用
    }
    const progress = ((ts - start) % duration) / duration; // 当前动画进度（0~1）
    bar.style.transform = `translateX(${(-100 + 100 * progress)}%)`;
    bar._glowAnimId = requestAnimationFrame(animateBar); // 循环动画
  }
  bar._glowAnimId = requestAnimationFrame(animateBar);
}
onMounted(() => {
  initGraph();
})
</script>

<style lang="scss" scoped>
.graph {
  width: 100%;
  height: 100%;
  background: #202020;
  .button-group {
    display: flex;
    padding: 10px;
    gap: 8px;
    flex-wrap: wrap;
    .ant-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: #fff;
      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }
  }
  #container {
    width: 100%;
    height: 100%;
  }
}

// 弹窗内容样式
.input-dialog-demo {
  .demo-description {
    margin-bottom: 20px;
    padding: 12px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
    color: #52c41a;
    font-size: 14px;
    line-height: 1.5;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }
}

@keyframes progressBarAnim {
  0% {
    margin-left: -40%;
    width: 40%;
  }

  50% {
    margin-left: 50%;
    width: 50%;
  }

  100% {
    margin-left: 100%;
    width: 40%;
  }
}

.progress-bar-anim {
  animation: progressBarAnim 1.2s linear infinite;
}

.glow-text {
  animation: glow 1.5s infinite alternate;
  color: #7FB0F6;
  text-shadow:
    0 0 8px #7FB0F6,
    0 0 16px #7FB0F6,
    0 0 24px #7FB0F6;
}

@keyframes glow {
  from {
    text-shadow:
      0 0 4px #7FB0F6,
      0 0 8px #7FB0F6,
      0 0 12px #7FB0F6;
  }

  to {
    text-shadow:
      0 0 16px #7FB0F6,
      0 0 32px #7FB0F6,
      0 0 48px #7FB0F6;
  }
}
</style>
