<!--
 * @Author: yuzhou<PERSON>e
 * @Date: 2025-04-02 15:22:50
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-23 10:05:03
 * @FilePath: \platform-face-web\src\components\Comparison\AlgorithmScoreItem\index.vue
 * @Description: 算法得分项
 * 
-->
<template>
  <div class="algorithm-score-item" :style="fontSizeStyle">
    <span class="algorithm-score">{{ toFixedDecimal(score, 2) }}</span>
    <span class="algorithm-code">{{ algorithmShortNames[code] || code }}</span>
    <span class="algorithm-name">{{ algorithmShortNames[name] || name }}</span>
  </div>
</template>
<script setup>
import { computed } from 'vue';
import { toFixedDecimal } from '@/utils/tool';
import { algorithmShortNames } from '@/api/algorithm.js';

const props = defineProps({
  score: { type: [String, Number], required: true },
  code: { type: String, required: true },
  name: { type: String, required: true },
  fontSize: { type: String, default: '13px' },
  codeFontSize: { type: String, default: '11px' },
  codeHeight: { type: String, default: '13px' },
  codePadding: { type: String, default: '0 4px' }
});

const fontSizeStyle = computed(() => ({
  fontSize: props.fontSize,
}));
</script>
<style lang="scss" scoped>
.algorithm-score-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #FFFFFF;
}

.algorithm-code {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: v-bind(codePadding);
  height: v-bind(codeHeight);
  color: #6FCBFF;
  border: .5px solid #6FCBFF;
  border-radius: v-bind(codeHeight);
  line-height: v-bind(codeHeight);
  font-size: v-bind(codeFontSize);
}
</style>