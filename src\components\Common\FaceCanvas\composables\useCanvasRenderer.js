import { ref, nextTick } from 'vue';

/**
 * Canvas渲染组合式函数
 * 处理图像加载、Canvas尺寸调整和重绘逻辑
 */
export function useCanvasRenderer() {
  const canvasContext = ref(null);
  const image = new Image();
  
  /**
   * 设置Canvas尺寸
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {DOMRect} containerRect - 容器尺寸
   */
  const setCanvasSize = (canvas, containerRect) => {
    canvas.width = containerRect.width;
    canvas.height = containerRect.height;
  };

  /**
   * 计算图像适应容器的缩放和偏移
   * @param {DOMRect} containerRect - 容器尺寸
   * @param {HTMLImageElement} img - 图像元素
   * @returns {Object} 包含scale和offset的对象
   */
  const calculateImageFitScale = (containerRect, img) => {
    const containerAspect = containerRect.width / containerRect.height;
    const imageAspect = img.naturalWidth / img.naturalHeight;

    if (imageAspect > containerAspect) {
      // 图像较宽，以容器宽度为准
      const baseScale = containerRect.width / img.naturalWidth;
      const offsetX = 0;
      const offsetY = (containerRect.height - img.naturalHeight * baseScale) / 2;
      return { scale: baseScale, offset: { x: offsetX, y: offsetY } };
    } else {
      // 图像较高，以容器高度为准
      const baseScale = containerRect.height / img.naturalHeight;
      const offsetY = 0;
      const offsetX = (containerRect.width - img.naturalWidth * baseScale) / 2;
      return { scale: baseScale, offset: { x: offsetX, y: offsetY } };
    }
  };

  /**
   * 加载图像
   * @param {string} imageSrc - 图像源地址
   * @param {Function} onLoad - 加载完成回调
   * @param {Function} onError - 加载错误回调
   */
  const loadImage = (imageSrc, onLoad, onError) => {
    if (!imageSrc) return;
    
    image.crossOrigin = 'anonymous';
    image.onload = () => {
      nextTick(() => {
        if (onLoad) onLoad();
      });
    };
    image.onerror = () => {
      if (onError) onError();
    };
    image.src = imageSrc;
  };

  /**
   * 重绘Canvas
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {Object} scale - 缩放比例
   * @param {Object} offset - 偏移量
   */
  const redraw = (canvas, scale, offset) => {
    if (!canvasContext.value) {
      canvasContext.value = canvas.getContext('2d');
    }
    
    // 清空画布
    canvasContext.value.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制图像
    if (image.complete && image.naturalWidth > 0) {
      canvasContext.value.drawImage(
        image, 
        offset.x, 
        offset.y, 
        image.naturalWidth * scale, 
        image.naturalHeight * scale
      );
    }
  };

  /**
   * 调整Canvas尺寸和缩放
   * @param {HTMLElement} container - 容器元素
   * @param {HTMLCanvasElement} canvas - Canvas元素
   * @param {Function} setZoomPanState - 设置缩放平移状态的函数
   * @param {Function} onRedraw - 重绘回调
   */
  const adjustCanvasSize = (container, canvas, setZoomPanState, onRedraw) => {
    const containerRect = container.getBoundingClientRect();
    setCanvasSize(canvas, containerRect);
    
    const { scale: newScale, offset: newOffset } = calculateImageFitScale(containerRect, image);
    setZoomPanState({ scale: newScale, offset: newOffset });
    
    if (onRedraw) onRedraw();
  };

  /**
   * 裁剪图像区域
   * @param {HTMLCanvasElement} cropCanvas - 用于裁剪的Canvas
   * @param {Object} rect - 裁剪区域 {x, y, width, height}
   * @returns {string} Base64图像数据
   */
  const cropImageRect = (cropCanvas, rect) => {
    const { x, y, width, height } = rect;
    cropCanvas.width = width;
    cropCanvas.height = height;
    const ctx = cropCanvas.getContext('2d');

    ctx.drawImage(
      image, // 原始图像
      x, y, width, height, // 原图裁剪区域
      0, 0, width, height  // 在canvas上绘制区域
    );

    return cropCanvas.toDataURL('image/png');
  };

  return {
    image,
    canvasContext,
    loadImage,
    setCanvasSize,
    calculateImageFitScale,
    adjustCanvasSize,
    redraw,
    cropImageRect
  };
}
