import axios from 'axios';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, // 请求的默认前缀 只要是发出去请求就会 默认带上这个前缀
  timeout: 10000, // 请求超时时间：10s
  headers: { 'Content-Type': 'application/json;charset=utf-8' }, //设置默认请求头
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求里加入token认证信息
    // const token = getToken()//localStorage.getItem('token')获取的
    // if (token) {
    //     config.headers.Authorization = `Bearer ${token}`
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// 响应拦截器即异常处理
service.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (err) => {
    if (err && err.response) {
      switch (err.response.status) {
        case 400:
          err.message = '错误请求';
          //   ElMessage({
          //     message: msg || '系统出错',
          //     type: 'error',
          //     duration: 5 * 1000,
          //   });
          break;
        case 401:
          err.message = '未授权，请重新登录';
          //router.push('/login')
          //return Promise.reject(error)
          break;
        case 403:
          err.message = '拒绝访问';
          break;
        case 404:
          err.message = '请求错误,未找到该资源';
          break;
        case 405:
          err.message = '请求方法未允许';
          break;
        case 408:
          err.message = '请求超时';
          break;
        case 500:
          err.message = '服务器端出错';
          break;
        case 501:
          err.message = '网络未实现';
          break;
        case 502:
          err.message = '网络错误';
          break;
        case 503:
          err.message = '服务不可用';
          break;
        case 504:
          err.message = '网络超时';
          break;
        case 505:
          err.message = 'http版本不支持该请求';
          break;
        default:
          err.message = `连接错误${err.response.status}`;
      }
    } else {
      err.message = '连接到服务器失败';
    }
    console.error(err.message);
    return Promise.resolve(err.response);
  },
);

// 封装便捷方法
const http = {
  get(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      ...config
    });
  },
  
  post(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config
    });
  },
  
  put(url, data = {}, config = {}) {
    return service({
      method: 'put',
      url,
      data,
      ...config
    });
  },
  
  delete(url, params = {}, config = {}) {
    return service({
      method: 'delete',
      url,
      params,
      ...config
    });
  },
  
  patch(url, data = {}, config = {}) {
    return service({
      method: 'patch',
      url,
      data,
      ...config
    });
  }
};

// Protobuf 封装
const protobuf = {
    /**
   * 创建 Protobuf 请求对象
   * @param {object} MessageType - Protobuf 消息类（如 Portrait）
   * @param {object} fields - 要设置的字段键值对（如 { name: '张三' }）
   * @returns {object} 初始化后的 Protobuf 消息实例
   */
    createRequest(MessageType, fields) {
      const req = new MessageType();
      for (const [key, value] of Object.entries(fields)) {
        const setterName = `set${key.charAt(0).toUpperCase()}${key.slice(1)}`;
        if (typeof req[setterName] === 'function') {
          req[setterName](value);
        } else {
          console.warn(`[protobuf] Setter "${setterName}" not found in ${MessageType.name}`);
        }
      }
      return req;
    },
  /**
   * Protobuf POST 请求（带自动创建请求对象）
   * @param {string} url
   * @param {object} MessageType - 请求消息类（如 Portrait）
   * @param {object} fields - 请求字段键值对
   * @param {object} respType - 响应类型类（如 PortraitList）
   * @param {object} config - 额外 axios 配置
   */
  post(url, fields, respType, MessageType, config = {}) {
    const req = this.createRequest(MessageType, fields);
    return service.post(url, req.serializeBinary(), {
      ...config,
      headers: {
        'Content-Type': 'application/x-protobuf',
        'Accept': 'application/x-protobuf',
        ...(config.headers || {})
      },
      responseType: 'arraybuffer'
    }).then(res => respType.deserializeBinary(new Uint8Array(res)));
  },
  /**
   * Protobuf GET 请求
   * @param {string} url
   * @param {object} params 查询参数
   * @param {object} respType 由 pbjs 生成的响应类型类
   * @param {object} config 额外 axios 配置
   * @returns {Promise<object>} 反序列化后的 pb 对象
   */
  get(url, params = {}, respType, config = {}) {
    return service.get(url, {
      ...config,
      params,
      headers: {
        'Accept': 'application/x-protobuf',
        ...(config.headers || {})
      },
      responseType: 'arraybuffer'
    }).then(res => {
      return respType.deserializeBinary(new Uint8Array(res));
    });
  },
  // 你可以按需添加 put/delete/patch 方法，和 post 类似
};

// 导出实例和便捷方法
export default http;
export { service, protobuf };
