# MatchingResultHeader 人脸比对结果头部组件

## 组件描述

MatchingResultHeader 用于显示当前比对结果的头部信息。组件集成了人脸图像展示、身份信息显示、操作按钮组和候选人像下拉选择等功能，为用户提供直观的比对结果概览和快速操作入口。

## 功能特性

- ✅ **智能人脸展示**：基于 AvatarImage 组件，支持人脸智能居中显示
- ✅ **候选人像切换**：点击头像可展开下拉列表，快速切换查看其他候选人像
- ✅ **身份信息显示**：展示推荐匹配的人员姓名信息
- ✅ **操作按钮组**：提供比对报告、导出、快速研判、历史记录等操作入口
- ✅ **可配置按钮**：支持动态控制第三、第四个按钮的显示和配置
- ✅ **响应式布局**：固定高度布局，适配不同屏幕尺寸

## 使用场景

- 人脸比对结果页面的头部展示
- 身份识别系统的匹配结果概览
- 需要快速切换候选人像的场景
- 集成多种操作功能的结果展示界面

## Props 参数

| 参数名                    | 类型    | 默认值        | 必填 | 说明                   |
| ------------------------- | ------- | ------------- | ---- | ---------------------- |
| `current`                 | Object  | -             | ✅   | 当前展示的人像数据对象 |
| `list`                    | Array   | -             | ✅   | 候选人像列表数据       |
| `thirdIconName`           | String  | `'bar-chart'` | ❌   | 第三个按钮的图标名称   |
| `thirdButtonTooltipTitle` | String  | `'快速研判'`  | ❌   | 第三个按钮的提示文本   |
| `isShowThirdButton`       | Boolean | `true`        | ❌   | 是否显示第三个按钮     |
| `isShowFourthButton`      | Boolean | `false`       | ❌   | 是否显示第四个按钮     |

### current 数据结构

```javascript
{
  // 多人脸数据
  multiFaceData: {
    sourceImageId: 'string',        // 源图片ID
    items: [{
      coordinate: [x, y, width, height]  // 人脸坐标 [左上角x, 左上角y, 宽度, 高度]
    }]
  },
  // 推荐人像信息
  recommendedPortrait: {
    portrait: {
      xm: 'string'                  // 姓名
    }
  },
  involvedFaceId: 'string'          // 涉及的人脸ID
}
```

### list 数据结构

```javascript
[
  [
    {
      imageId: 'string', // 图片ID
      multiFaceData: {
        items: [
          {
            coordinate: [x, y, width, height], // 人脸坐标
          },
        ],
      },
      assumedPortrait: {
        name: 'string', // 假定人像名称
      },
    },
  ],
  // ... 更多候选项
];
```

## Events 事件

| 事件名             | 参数   | 说明                                   |
| ------------------ | ------ | -------------------------------------- |
| `handleGridSelect` | `item` | 候选人像选择时触发，返回选中的人像数据 |
| `handleBtnClick`   | `type` | 操作按钮点击时触发，返回按钮类型       |

### 按钮类型说明

- `'analysisJudgment'`：快速研判按钮
- `'historicalRecords'`：历史记录按钮

## 使用示例

### 1. 基础用法

```vue
<template>
  <MatchingResultHeader :current="currentFace" :list="candidateList" @handle-grid-select="handleFaceSelect" @handle-btn-click="handleButtonClick" />
</template>

<script setup>
import MatchingResultHeader from '@/components/Comparison/MatchingResultHeader/index.vue';

const currentFace = {
  multiFaceData: {
    sourceImageId: 'img-001',
    items: [
      {
        coordinate: [100, 50, 200, 250],
      },
    ],
  },
  recommendedPortrait: {
    portrait: {
      xm: '张三',
    },
  },
  involvedFaceId: 'face-001',
};

const candidateList = [
  [
    {
      imageId: 'candidate-001',
      multiFaceData: {
        items: [
          {
            coordinate: [120, 60, 180, 220],
          },
        ],
      },
      assumedPortrait: {
        name: '李四',
      },
    },
  ],
];

const handleFaceSelect = (selectedItem) => {
  console.log('选中的候选人像:', selectedItem);
};

const handleButtonClick = (buttonType) => {
  console.log('点击的按钮类型:', buttonType);
  switch (buttonType) {
    case 'analysisJudgment':
      // 处理快速研判
      break;
    case 'historicalRecords':
      // 处理历史记录
      break;
  }
};
</script>
```

### 2. 自定义按钮配置

```vue
<template>
  <MatchingResultHeader
    :current="currentFace"
    :list="candidateList"
    third-icon-name="analytics"
    third-button-tooltip-title="数据分析"
    :is-show-third-button="true"
    :is-show-fourth-button="true"
    @handle-grid-select="handleFaceSelect"
    @handle-btn-click="handleButtonClick" />
</template>
```

### 3. 隐藏可选按钮

```vue
<template>
  <MatchingResultHeader
    :current="currentFace"
    :list="candidateList"
    :is-show-third-button="false"
    :is-show-fourth-button="false"
    @handle-grid-select="handleFaceSelect"
    @handle-btn-click="handleButtonClick" />
</template>
```

## 依赖组件

- **AvatarImage**: 智能头像组件，支持人脸居中显示和状态徽章
- **LoadMoreList**: 加载更多列表组件，用于候选人像的垂直展示
- **CustomButtonWithTooltip**: 自定义按钮组件，支持图标和提示信息
- **Divider**: 分割线组件，用于按钮间的视觉分隔
- **SvgIcon**: SVG 图标组件

## 样式说明

### 布局结构

- **容器**: 固定高度 110px，圆角背景容器
- **图片区域**: 绝对定位在左侧，展示当前人脸图像
- **信息区域**: 绝对定位在右侧，展示姓名和操作按钮

### 关键样式类

```scss
.matching-result-header {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  width: 100%;
  height: 110px;
  position: relative;
}

.result-image {
  position: absolute;
  top: 20px;
  left: 40px;
}

.result-summary {
  position: absolute;
  top: calc(50% - 22px);
  left: 180px;
  height: 45px;
  color: #b0bbcc;
}
```

## 注意事项

1. **数据完整性**: 确保传入的 current 和 list 数据包含完整的必要字段
2. **图片加载**: 依赖 DEFAULT_CONFIG 中的 getImageWebURL 函数获取图片 URL
3. **坐标格式**: 人脸坐标数组格式为 [x, y, width, height]
4. **下拉容器**: 使用自定义的 getPopupContainer 函数确保下拉框正确定位
5. **事件处理**: 父组件需要正确处理 handleGridSelect 和 handleBtnClick 事件

## 最佳实践

1. **数据预处理**: 在传入组件前验证数据结构的完整性
2. **错误处理**: 对图片加载失败等异常情况进行适当处理
3. **性能优化**: 大量候选人像时考虑虚拟滚动或分页加载
4. **用户体验**: 提供加载状态和错误提示，增强用户体验
