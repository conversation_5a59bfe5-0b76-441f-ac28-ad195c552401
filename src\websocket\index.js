import WebSocketManager from './WebSocketManager';

let webSocketManager = null;

export function initializeWebSocketManager() {
  if (!webSocketManager) {
    webSocketManager = new WebSocketManager({
      dispatch: (name, data) => {
        console.log(`[WebSocket Dispatch] ${name}:`, data);
      }
    });
  }
  return webSocketManager;
}

export function destroyWebSocketManager() {
  if (webSocketManager) {
    webSocketManager.deactivate();
    webSocketManager = null;
  }
}

export function getWebSocketManager() {
  return webSocketManager;
}
