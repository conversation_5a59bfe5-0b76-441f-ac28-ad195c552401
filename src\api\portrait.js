import request from '@/utils/service';
import auth from '@/utils/auth';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

const cache = new Map();

const CACHE_TTL = 60 * 60 * 1000; // 1 小时，单位：毫秒

export const getAllPortraitGroup = async () => {
  const cacheKey = `all-portrait-group`;
  const now = Date.now();

  if (cache.has(cacheKey)) {
    const { timestamp, result } = cache.get(cacheKey);
    if (now - timestamp < CACHE_TTL) {
      return Promise.resolve(result);
    } else {
      cache.delete(cacheKey);
    }
  }

  const { id: ownerId } = auth.getUserInfo();
  console.log('ownerId =', ownerId);
  const result = await request.get(`${BASE_URL}/api/portrait-library-group`, { ownerId });
  cache.set(cacheKey, { result, timestamp: now });
  return result;
};

export const getAllPortraitLibrariesGroupByType = async () => {
  const cacheKey = `AllPortraitLibrariesGroupByType`;
  const now = Date.now();

  if (cache.has(cacheKey)) {
    const { timestamp, result } = cache.get(cacheKey);
    if (now - timestamp < CACHE_TTL) {
      return Promise.resolve(result);
    } else {
      cache.delete(cacheKey);
    }
  }

  const data = { type: ['BASIC', 'TOPIC_SPECIFIC', 'PERSONAL_BUSINESS'] };
  const result = await request.post(`${BASE_URL}/api/portraitLibraries/getAll`, data);
  cache.set(cacheKey, { result, timestamp: now });
  return result;
};
