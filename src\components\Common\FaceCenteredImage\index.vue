<!--
 * @Author: yuzhou<PERSON>e
 * @Date: 2025-07-22 09:18:41
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 17:52:05
 * @FilePath: \platform-face-web\src\components\Common\FaceCenteredImage\index.vue
 * @Description: 根据坐标放大图像
 * 
 * 
-->
<template>
  <div ref="containerRef" class="face-container" :style="containerStyle">
    <img ref="imageRef" :src="src" class="face-image" :style="imageStyle" alt="face" draggable="false"
      @load="onImageLoad" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, onUnmounted } from "vue";

const props = defineProps({
  src: String, // 图片 URL
  faceX: [Number, String], // 人脸左上角 X 坐标
  faceY: [Number, String], // 人脸左上角 Y 坐标
  faceWidth: [Number, String], // 人脸宽度
  faceHeight: [Number, String], // 人脸高度
  containerWidth: [Number, String], // 容器宽度 (可 px 或 %)
  containerHeight: [Number, String], // 容器高度
  borderRadius: String, // 圆角 ('50%' 可用于头像)
});

const imageRef = ref(null);
const containerRef = ref(null);

// 图片的原始尺寸
const naturalWidth = ref(0);
const naturalHeight = ref(0);

// 支持 px 和 %
const containerStyle = computed(() => ({
  width: typeof props.containerWidth === "number" ? `${props.containerWidth}px` : props.containerWidth,
  height: typeof props.containerHeight === "number" ? `${props.containerHeight}px` : props.containerHeight,
  overflow: "hidden",
  position: "relative",
  borderRadius: props.borderRadius || "0",
}));

const containerSize = reactive({
  width: 0,
  height: 0,
});

let observer = null;

onMounted(async () => {
  await nextTick();
  if (containerRef.value) {
    containerSize.width = containerRef.value.clientWidth;
    containerSize.height = containerRef.value.clientHeight;

    observer = new ResizeObserver(entries => {
      for (const entry of entries) {
        containerSize.width = entry.contentRect.width;
        containerSize.height = entry.contentRect.height;
      }
    });
    observer.observe(containerRef.value);
  }

  // 同步加载
  if (imageRef.value && imageRef.value.complete) {
    onImageLoad();
  }
});

onUnmounted(() => {
  if (observer) observer.disconnect();
});

const imageStyle = computed(() => {
  if (!naturalWidth.value || !naturalHeight.value) return {};

  // 容器实际尺寸
  const containerW = containerSize.width;
  const containerH = containerSize.height;

  // 计算人脸中心点 (基于原始图片)
  const faceCenterX = Number(props.faceX) + Number(props.faceWidth) / 2;;
  const faceCenterY = Number(props.faceY) + Number(props.faceHeight) / 2;

  // 计算缩放比例，确保人脸填充整个容器
  const scaleX = containerW / props.faceWidth;
  const scaleY = containerH / props.faceHeight;
  const scale = Math.min(scaleX, scaleY); // 取较大值确保完整覆盖

  // **计算偏移量**
  const offsetX = containerW / 2 - faceCenterX * scale;
  const offsetY = containerH / 2 - faceCenterY * scale;

  return {
    width: `${naturalWidth.value * scale}px`,
    height: `${naturalHeight.value * scale}px`,
    position: "absolute",
    transform: `translate(${offsetX}px, ${offsetY}px)`,
    transformOrigin: "top left",
  };
});

// **图片加载完成后，获取原始尺寸**
const onImageLoad = () => {
  if (imageRef.value) {
    naturalWidth.value = imageRef.value.naturalWidth;
    naturalHeight.value = imageRef.value.naturalHeight;
  }
};

onMounted(async () => {
  await nextTick(); // 确保 DOM 渲染后再获取尺寸
  if (imageRef.value.complete) onImageLoad(); // 若图片已缓存，手动触发加载
});
</script>

<style scoped>
.face-container {
  display: inline-block;
  background-color: #000;
}

.face-image {
  display: block;
}
</style>