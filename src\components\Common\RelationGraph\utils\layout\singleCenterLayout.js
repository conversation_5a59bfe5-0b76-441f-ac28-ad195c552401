/**
 * 单中心图布局模块
 * 负责单中心图的螺旋布局计算和节点位置优化
 */

import { centerRadius } from '../constants';

/**
 * 生成严格遵循规则的螺旋布局
 * @param {Array} nodes - 节点数据数组
 * @param {number} centerX - 中心节点x坐标
 * @param {number} centerY - 中心节点y坐标
 * @param {Object} options - 配置选项
 * @returns {Array} 节点位置数组
 */
function generateStrictSpiralLayout(nodes, centerX, centerY, options = {}) {
    const {
        width = 600,
        height = 600,
        minGap = 5,
        innerRadius = 120,
        outerRadius = 250,
        sideMinRadius = 400,
        sideMaxRadius = 800,
        baseAngleStep = 45,
        innerRanks = [1, 2, 3],
        outerStartRank = 4,
        spiralNodeCount,
    } = options;

    // 1. 预处理节点数据
    const processedNodes = nodes.map((node, index) => {
        const isCenter = node.isCenter;
        const rank = isCenter ? 0 : node.scoreRank || index;

        const maxScore = Math.max(...(node.scoreData || []).map((item) => item.score || 0), 0.1);
        const ciAnScore = node.scoreData?.find((sd) => sd.algorithmCode === 'ci_an')?.score || node.scoreData?.[0]?.score || 0;

        return {
            ...node,
            rank,
            isCenter,
            visualRadius: centerRadius * maxScore,
            ciAnScore: parseFloat(ciAnScore),
            originalIndex: index,
        };
    });

    // 2. 分数分组统计
    const innerScores = processedNodes.filter((node) => innerRanks.includes(node.rank)).map((node) => node.ciAnScore);
    const outerScores = processedNodes.filter((node) => node.rank >= outerStartRank && node.rank <= spiralNodeCount).map((node) => node.ciAnScore);
    const sideScores = processedNodes.filter((node) => node.rank > spiralNodeCount).map((node) => node.ciAnScore);

    const minInnerScore = Math.min(...innerScores.filter((s) => s > 0));
    const maxInnerScore = Math.max(...innerScores);
    const minOuterScore = Math.min(...outerScores.filter((s) => s > 0));
    const maxOuterScore = Math.max(...outerScores);
    const minSideScore = Math.min(...sideScores.filter((s) => s > 0));
    const maxSideScore = Math.max(...sideScores);

    // 3. 严格的距离计算函数
    const calculateStrictRadius = (node) => {
        const { rank, ciAnScore, visualRadius, isCenter } = node;

        if (isCenter) return 0;
        if (ciAnScore === 1) return centerRadius + visualRadius + minGap;
        if (rank <= spiralNodeCount) {
            const innerMinDistance = centerRadius + visualRadius + minGap;
            const outerMinDistance = visualRadius + minGap + innerRadius;
            const minDistance = rank < outerStartRank ? innerMinDistance : outerMinDistance;
            const baseRadius = rank < outerStartRank ? innerRadius : outerRadius;
            const maxScore = rank < outerStartRank ? maxInnerScore : maxOuterScore;
            const minScore = rank < outerStartRank ? minInnerScore : minOuterScore;
            const ratio = (maxScore - ciAnScore) / (maxScore - minScore);
            const distance1 = minDistance + (baseRadius - minDistance) * (1 - ciAnScore);
            const distance2 = baseRadius * (0.6 + 0.4 * ratio);
            return Math.max(minDistance, distance1, distance2);
        } else {
            // 周边节点使用线性映射到半径范围
            const minDistance = visualRadius + minGap + outerRadius;
            const normalizedScore = (ciAnScore - minSideScore) / (maxSideScore - minSideScore);
            const distance = sideMinRadius + (sideMaxRadius - sideMinRadius) * (1 - normalizedScore);
            return Math.max(minDistance, distance);
        }
    };

    // 4. 严格布局生成
    const positions = [];
    const placedNodes = [];

    // 放置中心节点
    const centerNode = processedNodes.find((node) => node.isCenter);
    if (centerNode) {
        positions.push({
            x: centerX,
            y: centerY,
            rank: 0,
            visualRadius: centerNode.visualRadius,
            originalIndex: centerNode.originalIndex,
        });
        placedNodes.push({
            x: centerX,
            y: centerY,
            radius: centerNode.visualRadius,
        });
    }

    // 先处理前spiralNodeCount个节点（严格模式）
    const mainNodes = processedNodes.filter((node) => !node.isCenter && node.rank <= spiralNodeCount).sort((a, b) => a.rank - b.rank);

    for (const node of mainNodes) {
        const { rank, visualRadius } = node;
        const radius = calculateStrictRadius(node);

        // 严格角度计算（不可调整半径）
        let angle;
        if (rank === 1) angle = 0;
        else if (rank === 2) angle = baseAngleStep;
        else if (rank === 3) angle = 2 * baseAngleStep;
        else {
            const stepIndex = (rank - 4) % 7;
            const steps = [40, 50, 40, 40, 40, 50, 40];
            const lastAngle = positions[positions.length - 1]?.angle || 0;
            angle = (lastAngle + steps[stepIndex]) % 360;
        }

        // 只调整角度来避免冲突
        const bestAngle = findBestAngleForMainNode(centerX, centerY, radius, visualRadius, angle, placedNodes, minGap);

        const x = centerX + radius * Math.cos((bestAngle * Math.PI) / 180);
        const y = centerY + radius * Math.sin((bestAngle * Math.PI) / 180);

        positions.push({
            x,
            y,
            angle: bestAngle,
            radius,
            visualRadius,
            rank,
            originalIndex: node.originalIndex,
            isMainNode: true,
        });

        placedNodes.push({
            x,
            y,
            radius: visualRadius,
        });
    }

    // 再处理周边节点（宽松模式）
    const sideNodes = processedNodes.filter((node) => node.rank > spiralNodeCount).sort((a, b) => a.rank - b.rank);

    for (const node of sideNodes) {
        const { rank, visualRadius } = node;
        const baseRadius = calculateStrictRadius(node);

        // 确定扇形区域（左侧或右侧）
        const isLeft = rank % 2 === 0; // 简单奇偶分配，可自定义
        const sector = isLeft ? { start: 120, end: 240 } : { start: -60, end: 60 };

        // 基础角度（在扇形区域内随机）
        const baseAngle = sector.start + Math.random() * (sector.end - sector.start);

        // 可以同时调整角度和半径
        const bestPosition = findBestPositionForSideNode(
            centerX,
            centerY,
            baseRadius,
            visualRadius,
            baseAngle,
            placedNodes,
            minGap,
            {
                minRadius: sideMinRadius,
                maxRadius: sideMaxRadius,
                sector,
            },
            {
                left: 0,
                right: width,
                top: 0,
                bottom: height,
            },
        );

        positions.push({
            ...bestPosition,
            rank,
            visualRadius,
            originalIndex: node.originalIndex,
            isSideNode: true,
        });

        placedNodes.push({
            x: bestPosition.x,
            y: bestPosition.y,
            radius: visualRadius,
        });
    }

    return positions.sort((a, b) => a.originalIndex - b.originalIndex);
}

/**
 * 为主节点寻找最佳角度（只能调整角度）
 */
function findBestAngleForMainNode(centerX, centerY, radius, visualRadius, preferredAngle, placedNodes, minGap) {
    let bestAngle = preferredAngle;
    let minConflict = Infinity;

    // 尝试±30度范围内的角度
    for (let angleOffset = 0; angleOffset <= 30; angleOffset += 3) {
        for (const direction of [-1, 1]) {
            const testAngle = (preferredAngle + angleOffset * direction + 360) % 360;
            const x = centerX + radius * Math.cos((testAngle * Math.PI) / 180);
            const y = centerY + radius * Math.sin((testAngle * Math.PI) / 180);

            let conflict = 0;
            for (const placed of placedNodes) {
                const dx = x - placed.x;
                const dy = y - placed.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const requiredDistance = visualRadius + placed.radius + minGap;

                if (distance < requiredDistance) {
                    conflict += requiredDistance - distance;
                }
            }

            if (conflict === 0) return testAngle; // 找到无冲突角度立即返回

            if (conflict < minConflict) {
                minConflict = conflict;
                bestAngle = testAngle;
            }
        }
    }

    return bestAngle;
}

/**
 * 为周边节点寻找最佳位置（可调整角度和半径）
 */
function findBestPositionForSideNode(
    centerX,
    centerY,
    baseRadius,
    visualRadius,
    baseAngle,
    placedNodes,
    minGap,
    constraints,
    canvasBounds, // 新增参数：{ left, right, top, bottom }
) {
    const { minRadius, maxRadius, sector } = constraints;
    let bestPosition = { x: 0, y: 0, angle: 0, radius: 0 };
    let bestScore = -Infinity;

    // 尝试多个候选位置
    for (let attempt = 0; attempt < 30; attempt++) {
        // 1. 生成候选角度（在扇形区域内）
        let angle;
        if (attempt < 10) {
            angle = baseAngle + (Math.random() * 20 - 10); // ±10°
        } else if (attempt < 20) {
            angle = baseAngle + (Math.random() * 40 - 20); // ±20°
        } else {
            angle = sector.start + Math.random() * (sector.end - sector.start);
        }
        angle = Math.max(sector.start, Math.min(sector.end, angle));

        // 2. 生成候选半径（在限定范围内）
        let radius;
        if (attempt % 5 === 0) {
            radius = baseRadius + (Math.random() * 2 - 1) * (maxRadius - minRadius) * 0.2;
        } else {
            radius = baseRadius + (Math.random() * 2 - 1) * (maxRadius - minRadius) * 0.05;
        }
        radius = Math.max(minRadius, Math.min(maxRadius, radius));

        // 3. 计算坐标
        let x = centerX + radius * Math.cos((angle * Math.PI) / 180);
        let y = centerY + radius * Math.sin((angle * Math.PI) / 180);

        // 4.边界安全检测与调整
        let adjustedRadius = radius;
        let isAdjusted = false;

        // 检查是否超出画布边界
        const safeDistance = visualRadius + minGap;
        const maxX = canvasBounds.right - safeDistance;
        const minX = canvasBounds.left + safeDistance;
        const maxY = canvasBounds.bottom - safeDistance;
        const minY = canvasBounds.top + safeDistance;

        if (x < minX || x > maxX || y < minY || y > maxY) {
            // 计算允许的最大半径（基于角度和边界）
            const maxRadiusX = Math.min(
                Math.abs((maxX - centerX) / Math.cos((angle * Math.PI) / 180)),
                Math.abs((centerX - minX) / Math.cos((angle * Math.PI) / 180)),
            );
            const maxRadiusY = Math.min(
                Math.abs((maxY - centerY) / Math.sin((angle * Math.PI) / 180)),
                Math.abs((centerY - minY) / Math.sin((angle * Math.PI) / 180)),
            );
            adjustedRadius = Math.min(radius, maxRadiusX, maxRadiusY);
            isAdjusted = true;

            // 重新计算坐标
            x = centerX + adjustedRadius * Math.cos((angle * Math.PI) / 180);
            y = centerY + adjustedRadius * Math.sin((angle * Math.PI) / 180);
        }

        // 5. 冲突检测（考虑所有已放置节点）
        let conflictScore = 0;
        let isValid = true;
        for (const placed of placedNodes) {
            const dx = x - placed.x;
            const dy = y - placed.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const requiredDistance = visualRadius + placed.radius + minGap;

            if (distance < requiredDistance) {
                conflictScore += (requiredDistance - distance) * 100;
                isValid = false;
            }
        }

        // 6. 计算综合评分（惩罚调整和冲突）
        const angleDeviation = Math.min(Math.abs(angle - baseAngle), 360 - Math.abs(angle - baseAngle));
        const radiusDeviation = Math.abs(adjustedRadius - baseRadius);
        const adjustmentPenalty = isAdjusted ? 50 : 0; // 边界调整惩罚

        const positionScore = (isValid ? 1000 : 0) - conflictScore - angleDeviation * 0.3 - radiusDeviation * 0.2 - adjustmentPenalty;

        // 7. 更新最佳位置
        if (positionScore > bestScore) {
            bestScore = positionScore;
            bestPosition = {
                x,
                y,
                angle,
                radius: adjustedRadius,
                wasAdjusted: isAdjusted, // 标记是否经过调整
            };
        }
    }

    return bestPosition;
}

// 单中心图布局
export function layoutNodesStrict(nodes, options) {
    return new Promise((resolve) => {
        const { width = 800, height = 600 } = options;

        const centerX = width / 2;
        const centerY = height / 2;

        // 生成布局 - 传递所有配置参数
        const nodePositions = generateStrictSpiralLayout(nodes, centerX, centerY, options);

        // 应用位置到节点数据
        const centerNode = nodes.find((n) => n.isCenter);
        if (centerNode) {
            centerNode.x = centerX;
            centerNode.y = centerY;
        }

        nodePositions.forEach((pos) => {
            const node = nodes.find((n) => n.scoreRank === pos.rank);
            if (node) {
                node.x = pos.x;
                node.y = pos.y;
                node.layoutAngle = pos.angle;
                node.layoutRadius = pos.radius;
                node.visualRadius = pos.visualRadius;
            }
        });

        resolve(nodes);
    });
}
