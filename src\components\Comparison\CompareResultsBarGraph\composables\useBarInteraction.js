/**
 * 柱状图交互逻辑 composable
 * 处理CompareResultsBarGraph的交互功能
 */
import * as d3 from 'd3';

/**
 * 柱状图交互逻辑 composable
 * @param {Object} baseChart - 基础图表引用
 * @returns {Object} 交互相关的方法
 */
export function useBarInteraction(baseChart) {
  
  /**
   * 绘制操作区域
   * @param {Object} data - 数据项
   * @param {number} index - 索引
   * @param {Function} xCalculator - X坐标计算函数
   * @param {number} rectWidth - 矩形宽度
   * @param {d3.Selection} g4 - SVG组选择器
   * @param {Object} currentData - 当前选中数据
   * @param {Array} legendCheckbox - 图例复选框状态
   * @param {number} altitudeHeight - 高度
   */
  const drawOperationArea = (data, index, xCalculator, rectWidth, g4, currentData, legendCheckbox, altitudeHeight) => {
    const x = xCalculator(data, index);
    const { operationY, operationHeight } = calculateOperationDimensions(legendCheckbox, altitudeHeight);

    g4.append('rect')
      .attr('data-id', `${data.algorithmCode}-${data.portrait.portraitId}`)
      .attr('class', 'operation-area')
      .attr('x', x)
      .attr('y', operationY)
      .attr('width', rectWidth)
      .attr('height', operationHeight)
      .attr('fill', 'transparent')
      .style('cursor', 'pointer')
      .on('click', () => handleBarClick(data, currentData));
  };

  /**
   * 计算操作区域的尺寸
   * @param {Array} legendCheckbox - 图例复选框状态
   * @param {number} altitudeHeight - 高度
   * @returns {Object} 操作区域的Y坐标和高度
   */
  const calculateOperationDimensions = (legendCheckbox, altitudeHeight) => {
    let operationY, operationHeight;
    
    if (legendCheckbox[0].isChecked && legendCheckbox[1].isChecked) {
      operationY = -altitudeHeight;
      operationHeight = altitudeHeight * 2;
    } else if (legendCheckbox[0].isChecked && !legendCheckbox[1].isChecked) {
      operationY = -altitudeHeight;
      operationHeight = altitudeHeight;
    } else if (!legendCheckbox[0].isChecked && legendCheckbox[1].isChecked) {
      operationY = 0;
      operationHeight = altitudeHeight;
    }

    return { operationY, operationHeight };
  };

  /**
   * 处理柱状图点击事件
   * @param {Object} data - 点击的数据项
   * @param {Object} currentData - 当前选中数据
   */
  const handleBarClick = (data, currentData) => {
    if (!baseChart.value) return;

    const flag = currentData.value.has(data);
    
    // 如果不是反选且已有选中项，清除其他选择（单选模式）
    if (!flag && currentData.value.size > 0) {
      clearCurrentSelection(currentData);
    }
    
    // 切换当前项的选中状态
    toggleBarSelection(data);
  };

  /**
   * 清除当前选中状态
   * @param {Object} currentData - 当前选中数据
   */
  const clearCurrentSelection = (currentData) => {
    const currentItem = currentData.value.values().next().value;
    if (currentItem) {
      const selector = `[data-id="${currentItem.algorithmCode}-${currentItem.portrait.portraitId}"]`;
      const clickedBar = d3.selectAll(selector);
      baseChart.value.barChart.updateElements(clickedBar, currentItem);
    }
  };

  /**
   * 切换柱状图选中状态
   * @param {Object} data - 数据项
   */
  const toggleBarSelection = (data) => {
    const selector = `[data-id="${data.algorithmCode}-${data.portrait.portraitId}"]`;
    const clickedBar = d3.selectAll(selector);
    baseChart.value.barChart.updateElements(clickedBar, data);
  };

  /**
   * 清除所有选中状态
   * @param {Object} currentData - 当前选中数据
   */
  const clearAllSelections = (currentData) => {
    if (currentData.value.size > 0) {
      clearCurrentSelection(currentData);
    }
  };

  /**
   * 处理选中数据变化的监听
   * @param {Object} currentData - 当前选中数据
   * @param {Function} clearSelectDataWatcher - 清空数据监听器
   */
  const setupSelectionWatcher = (currentData, clearSelectDataWatcher) => {
    return clearSelectDataWatcher((newData, oldData) => {
      if (newData !== oldData && baseChart.value) {
        clearAllSelections(currentData);
      }
    });
  };

  return {
    drawOperationArea,
    handleBarClick,
    clearAllSelections,
    setupSelectionWatcher,
    calculateOperationDimensions
  };
}
