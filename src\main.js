import { createApp } from 'vue';
import App from './App.vue';
import router from '@/router/index'; //引入路由
import store from './store';
import '@/styles/index.scss'; // 引入全局样式
// 1. 引入Ant Design Vue组件库和样式
import 'ant-design-vue/dist/reset.css';
// 引入自定义 Ant Design Vue 样式
import '@/styles/ant-design-custom.scss';
// 2. 引入SVG图标
import 'virtual:svg-icons-register';
import { Message, Notification } from './utils/message.js';
import CommonComponents from './components/Common';

const app = createApp(App);

// 挂载 message / notification
app.config.globalProperties.$message = Message;
app.config.globalProperties.$notification = Notification;

app.use(CommonComponents);
app.use(router);
app.use(store);
app.mount('#app');

window.app = app;
