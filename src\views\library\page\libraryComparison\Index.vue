<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-29 09:14:02
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 15:47:40
 * @FilePath: \platform-face-web\src\views\library\page\libraryComparison\Index.vue
 * @Description: 人脸比对页面
 * 
 * 
-->
<template>
    <div class="library-comparison-container">
        <DualColumnLayout :loading="loading" headerIcon="comparison-comparison-results-high" headerTitle="人脸比对"
            leftBoxTitle="" rightBoxTitle="" loadingText="人脸比对中" :showStatus="showStatus">
            <template #completed-status>
                <span class="status-text">比对结果</span>
            </template>
            <template #completed-status-text>
                <div class="flex-center w-100 h-100 status-text-completed">相似度 {{ toFixedDecimal(score, 2) }} <Divider
                        color="#2E475C" direction="vertical" height="11px"></Divider> 可能是同一人
                </div>
            </template>
            <template #left-content>
                <ImageUpload v-if="!leftImageId" @onChange="handleImageUpload($event, 'left')"></ImageUpload>
                <AvatarImage v-else :src="getImageWebURL(leftImageId)" ringColor="transparent" :ringThickness="0"
                    borderRadius="8px" width="100%" height="100%"></AvatarImage>
            </template>
            <template #right-content>
                <ImageUpload v-if="!rightImageId" @onChange="handleImageUpload($event, 'right')"></ImageUpload>
                <AvatarImage v-else :src="getImageWebURL(rightImageId)" ringColor="transparent" :ringThickness="0"
                    borderRadius="8px" width="100%" height="100%"></AvatarImage>
            </template>
            <template #action-button>
                <CustomButtonWithTooltip width="188px" height="42px" @click="handleComparison">开始比对
                </CustomButtonWithTooltip>
            </template>
        </DualColumnLayout>
    </div>
</template>

<script setup>
import { ref } from "vue";
import { DEFAULT_CONFIG } from '@/constants';
import { imageComparison } from '@/api/library/library.js';
import { toFixedDecimal } from '@/utils/tool';

import ImageUpload from "../../components/ImageUpload.vue";
import DualColumnLayout from "../../components/DualColumnLayout.vue";

const { getImageWebURL } = DEFAULT_CONFIG || {};

const loading = ref(false);
const showStatus = ref(false);
const leftImageId = ref(null);
const rightImageId = ref(null);
const score = ref(null);

const handleImageUpload = (uploadImageId, type) => {
    if (type === 'left') {
        leftImageId.value = uploadImageId
    } else {
        rightImageId.value = uploadImageId
    }
};

const handleComparison = async () => {
    try {
        loading.value = true;
        showStatus.value = true;
        const payload = {
            algorithmCodes: ['ci_an'],
            sourceImageIds: [leftImageId.value],
            targetImageIds: [rightImageId.value],
        }
        const response = await imageComparison(payload);
        const data = response.data;
        const scores = data?.[0]?.faceCompareResults?.[0].comparedFaces?.[0]?.scores;
        score.value = scores?.AN || 0;
        // console.log('人脸比对的返回的结果', data);
    } catch (error) {
        console.error('人脸比对错误', error);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.library-comparison-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    box-sizing: border-box;
}

.status-text {
    font-size: 14px;
    color: #ffffff;
}

.status-text-completed {
    font-size: 18px;
    color: #4DBFFF;
}
</style>