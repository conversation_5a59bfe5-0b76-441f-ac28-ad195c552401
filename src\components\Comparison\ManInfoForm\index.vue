<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 17:02:37
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 15:09:55
 * @FilePath: \platform-face-web\src\components\Comparison\ManInfoForm\index.vue
 * @Description: 人员信息表单组件
 * 重构后的模块化人员信息表单，支持身份信息、社会行为、其他信息等多个模块
 * 
-->
<template>
  <div class="man-info-form">
    <a-form ref="formRef" :initial-values="INITIAL_FORM_DATA" :model="formData" :colon="false"
      :label-col="{ style: { width: '80px' } }">
      <div class="w-100 h-100 d-flex flex-column g-2">

        <!-- 身份信息模块 -->
        <FormSection title="身份信息" icon-class="identity-info-icon" icon-size="18px">
          <IdentityFields :form-data="formData" :info-dictionaries="infoDictionaries" />
        </FormSection>

        <!-- 社会行为模块 -->
        <FormSection title="社会行为" icon-class="social-behaviour-icon" icon-size="15px">
          <SocialBehaviorFields :form-data="formData" @add-involved-case-people="handleAddInvolvedCasePeople" />
        </FormSection>

        <!-- 其他信息模块 -->
        <FormSection title="其他" icon-class="other-icon" icon-size="15px">
          <OtherFields :form-data="formData" />
        </FormSection>

      </div>

      <!-- 表单底部 -->
      <div class="form-footer">
        <CustomButtonWithTooltip width="220px" height="34px" @click="handleSubmit">
          保存编辑
        </CustomButtonWithTooltip>
      </div>
    </a-form>

    <!-- TODO：手动添加人像弹窗 -->
    <!-- <ManuallyAddPortrait :visible="visible" @get-portrait="handleOk" @close="visible = false" /> -->
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { getDictionary } from '@/api/dictionary.js';

// 导入子组件
import FormSection from '../../Common/FormSection/index.vue';
import IdentityFields from './components/IdentityFields.vue';
import SocialBehaviorFields from './components/SocialBehaviorFields.vue';
import OtherFields from './components/OtherFields.vue';
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';

// 导入 composables
import { useFormData, INITIAL_FORM_DATA } from './composables/useFormData.js';

// Props 和 Emits
const emits = defineEmits(['submit-success']);

const props = defineProps({
  involvedFaceId: {
    type: String,
    default: ''
  },
  initialData: {
    type: Object
  }
});

// 响应式数据
const formRef = ref(null);
const visible = ref(false);
const infoDictionaries = ref(null);
const currentRelatedIndex = ref(0);

// 使用表单数据管理 composable
const { formData, setFormData, processSubmitData } = useFormData(props);

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    // 如果需要校验，可在此处使用 formRef.value.validate()
    // await formRef.value.validate();

    const processedData = processSubmitData(infoDictionaries.value, props.involvedFaceId);
    console.log('提交的表单数据:', processedData);
    emits('submit-success', processedData);
  } catch (error) {
    console.error('提交出错:', error);
    message.error('提交失败，请检查表单信息');
  }
};

/**
 * 处理添加关系人事件
 */
const handleAddInvolvedCasePeople = (index) => {
  currentRelatedIndex.value = index;
  visible.value = true;
};

/**
 * 添加关系人照片
 */
const handleOk = (data) => {
  formData.value.relatedCases[currentRelatedIndex.value].involvedCasePeoples.push({
    imageId: data.images.imageIds[0],
    name: data.name
  });
  visible.value = false;
  console.log('formData.value.involvesPersons', formData.value.involvesPersons);
};

// 生命周期钩子
onMounted(() => {
  getDictionary('ID_CARD_NO,RESIDENTIAL_ADDRESS').then(data => {
    infoDictionaries.value = data;
  });
});

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  setFormData(newData);
}, { immediate: true });

</script>

<style scoped>
.man-info-form {
  background: #1f242b;
  padding: 16px;
  border-radius: 8px;
  color: #fff;
  height: 100%;
  overflow: auto;
}

.form-footer {
  height: 60px;
  align-items: center;
  display: flex;
  justify-content: center;
  margin-top: 24px;
  width: 100%;
}

:deep(.ant-form-item) {
  margin-bottom: 0 !important;
}
</style>
