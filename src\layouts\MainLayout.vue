<!--
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-28 11:21:28
 * @FilePath: \platform-face-web\src\layouts\MainLayout.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="main-layout">
    <top-bar class="top-bar" />
    <div class="main-body">
      <transition name="slide-fade-left" mode="out-in">
        <side-menu v-if="!hideMenu" class="side-menu" :menu="currentMenu" :key="sideMenuKey" />
      </transition>
      <div class="router-view-wrap">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-slide" mode="out-in">
            <keep-alive v-if="route.meta.keepAlive">
              <component :is="Component" :key="route.path" />
            </keep-alive>
            <component v-else :is="Component" :key="route.path" />
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import SideMenu from './components/SideMenu.vue';
import TopBar from './components/TopBar.vue';
import menus from '../router/menus';
import { useRoute } from 'vue-router';
import theme from '@/theme';

// 配置：哪些模块（路由）需要隐藏左侧菜单栏
const hiddenMenuModules = ['/template']; // 只要在这里加模块名或者路由全拼即可

const route = useRoute();
const sideMenuKey = computed(() => {
  // 只要 isAutoHeight 变化，key 就变化，触发动画
  return route.meta?.sideMenuAutoHeight ? 'auto' : 'full';
});

const currentMenu = computed(() => {
  // 取一级路由名作为模块名
  // const moduleName = route.path.split('/')[1];
  return menus;
});
const hideMenu = computed(() => {
  const moduleName = route.path.split('/')[1];
  return hiddenMenuModules.includes(moduleName) || hiddenMenuModules.includes(route.path);
});
</script>

<style lang="scss" scoped>
html,
body,
#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}

.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  // background: v-bind('theme.pageBg');
  overflow: hidden;
}

.top-bar {
  height: 56px;
  background: v-bind('theme.topBarBg');
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.2), 0 1.5px 8px rgba(0, 0, 0, 0.1);
  z-index: 20;
  flex-shrink: 0;
}

.main-body {
  display: flex;
  flex: 1;
  min-height: 0;
  min-width: 0;
  background: v-bind('theme.pageBg') !important;
}

.side-menu {
  width: max-content;
  z-index: 10;
  height: 100%;
}

.router-view-wrap {
  flex: 1;
  overflow: auto;
  padding: 10px;
  background: v-bind('theme.pageBg');
  box-sizing: border-box;
  min-width: 0;
  overflow: hidden;
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.fade-slide-leave-from,
.fade-slide-enter-to {
  opacity: 1;
  transform: translateX(0);
}

.slide-fade-left-enter-active,
.slide-fade-left-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-left-enter-from {
  opacity: 0;
  transform: translateX(-40px);
}

.slide-fade-left-leave-to {
  opacity: 0;
  transform: translateX(-40px);
}

.slide-fade-left-enter-to,
.slide-fade-left-leave-from {
  opacity: 1;
  transform: translateX(0);
}
</style>