/**
 * 柱状图核心逻辑 composable
 * 处理图表的初始化、更新和交互逻辑
 */
import * as d3 from 'd3';
import { ref, reactive } from 'vue';
import { createGradients, addReferenceLines, filterReferenceLines, DEFAULT_REFERENCE_LINES, DEFAULT_MARGIN } from '@/utils/chartUtils';
import textWeightedMatchingS from '@/assets/svg/common/text-weighted-matching.svg';
import textWeightedMatching from '@/assets/svg/common/text-weighted-matching-gray.svg';

/**
 * 柱状图核心逻辑 composable
 * @param {Object} props - 组件属性
 * @param {Function} emit - 事件发射器
 * @param {Object} options - 配置选项
 * @returns {Object} 图表相关的方法和数据
 */
export function useBarChart(props, emit, options = {}) {
  // 默认配置
  const config = {
    margin: DEFAULT_MARGIN,
    rectsPerGroup: 5,
    varianceHeight: 0,
    ...options,
  };

  // 响应式状态
  const state = reactive({
    width: 0,
    height: 0,
    allDataChecked: true,
    initData: false,
    maxScore: 0,
    chartWidth: 0,
    chartHeight: 0,
  });

  const currentData = ref(new Set());

  // SVG 相关变量
  let svg;
  let processedData;
  let altitudeHeight;
  let g, g2, g3, g4;
  let interval;

  /**
   * 初始化SVG和基础结构
   * @param {HTMLElement} svgContainer - SVG容器元素
   */
  const initSVG = (svgContainer) => {
    state.chartHeight = state.height - config.margin.top - config.margin.bottom - config.varianceHeight;
    state.chartWidth = state.width - config.margin.left - config.margin.right;
    altitudeHeight = state.chartHeight / 2;

    // 创建 SVG 元素
    svg = d3.select(svgContainer).append('svg').attr('width', state.width).attr('height', state.height);

    // 创建渐变定义
    createGradients(svg);

    // 创建主要的组
    g = svg
      .append('g')
      .attr('transform', `translate(${config.margin.left}, ${config.margin.top + altitudeHeight + config.varianceHeight})`)
      .attr('pointer-events', 'none');

    g2 = svg
      .append('g')
      .attr('id', 'g2')
      .attr('transform', `translate(${config.margin.left}, ${config.margin.top + altitudeHeight + config.varianceHeight})`);

    g3 = svg
      .append('g')
      .attr('id', 'g3')
      .attr('transform', `translate(${config.margin.left}, ${config.margin.top + altitudeHeight + config.varianceHeight})`);

    g4 = svg
      .append('g')
      .attr('id', 'g4')
      .attr('transform', `translate(${config.margin.left}, ${config.margin.top + altitudeHeight + config.varianceHeight})`);

    return { svg, g, g2, g3, g4, altitudeHeight };
  };

  /**
   * 绘制坐标轴和参考线
   */
  const drawAxesAndReferences = () => {
    // 绘制Y轴线
    g.append('rect')
      .attr('x', -5)
      .attr('y', -altitudeHeight)
      .attr('width', 10)
      .attr('height', altitudeHeight - 7)
      .attr('fill', 'url(#greenGradient)');

    g.append('line')
      .attr('x1', 0)
      .attr('x2', 0)
      .attr('y1', -altitudeHeight + 7)
      .attr('y2', -7)
      .attr('stroke', '#6FBE9C')
      .attr('stroke-width', 1)
      .attr('stroke-dasharray', '4 4');

    g.append('rect')
      .attr('x', -5)
      .attr('y', 7)
      .attr('width', 10)
      .attr('height', altitudeHeight - 7)
      .attr('fill', 'url(#blueGradient)');

    g.append('line')
      .attr('x1', 0)
      .attr('x2', 0)
      .attr('y1', 7)
      .attr('y2', altitudeHeight - 7)
      .attr('stroke', '#44A5DC')
      .attr('stroke-width', 1)
      .attr('stroke-dasharray', '4 4');

    // 绘制参考线以及X轴线
    const filteredReferenceLines = filterReferenceLines(DEFAULT_REFERENCE_LINES, state.maxScore);
    addReferenceLines(g, filteredReferenceLines, state.chartWidth, altitudeHeight);

    // 绘制坐标轴标签
    drawAxisLabels();
  };

  /**
   * 绘制坐标轴标签
   */
  const drawAxisLabels = () => {
    g.append('text')
      .attr('x', -4)
      .attr('y', 0)
      .attr('dy', '0.35em')
      .attr('fill', '#FFF')
      .text('0')
      .style('font-size', '12px')
      .style('text-anchor', 'start');

    g.append('text')
      .attr('x', -18)
      .attr('y', -altitudeHeight)
      .attr('dy', '0.35em')
      .attr('fill', '#90FBCA')
      .text('1')
      .style('font-size', '12px')
      .style('text-anchor', 'start');

    g.append('text')
      .attr('x', -18)
      .attr('y', altitudeHeight)
      .attr('dy', '0.35em')
      .attr('fill', '#4EBFFF')
      .text('1')
      .style('font-size', '12px')
      .style('text-anchor', 'start');
  };

  /**
   * 更新元素选中状态
   * @param {d3.Selection} selection - D3选择器
   * @param {Object} d - 数据项
   */
  const updateElements = (selection, d) => {
    selection.each(function () {
      const element = d3.select(this);
      const isSelected = element.classed('selected');

      element.classed('selected', !isSelected);
      if (element.classed('algorithm-rect')) {
        if (isSelected) {
          if (d.scoreRank === 1 || d.inGroupIndex === 0) {
            element.attr('fill', 'none').attr('stroke', 'white');
          } else if (d?.infoMatchFlag === 'EXACT_MATCH') {
            element.attr('fill', 'url(#greenAndBlue)').attr('stroke', 'none');
          } else {
            element.attr('fill', 'none').attr('stroke', 'transparent');
          }
        } else {
          element.attr('fill', 'url(#greenAndBlue)').attr('stroke', 'none');
        }
      } else if (element.classed('operation-area')) {
        if (isSelected) {
          element.attr('stroke', 'none');
        } else {
          element.attr('stroke', '#90FBCA');
        }
      } else if (element.classed('algorithm-text')) {
        if (isSelected) {
          if (d.scoreRank === 1 || d.inGroupIndex === 0) {
            element.attr('fill', 'white');
          } else if (d?.infoMatchFlag === 'EXACT_MATCH') {
            element.attr('fill', 'black');
          } else {
            element.attr('fill', 'transparent');
          }
        } else {
          element.attr('fill', 'black');
        }
      } else if (element.classed('text-matching-img')) {
        if (isSelected) {
          if (d.scoreRank === 1 || d.inGroupIndex === 0) {
            element.attr('xlink:href', textWeightedMatching);
          } else if (d?.infoMatchFlag === 'EXACT_MATCH') {
            element.attr('xlink:href', textWeightedMatchingS);
          } else {
            element.attr('xlink:href', null);
          }
        } else {
          element.attr('xlink:href', textWeightedMatchingS);
        }
      } else if (element.classed('text-label')) {
        if (isSelected) {
          element.text('');
        } else {
          element.text(d.xLabel);
        }
      } else if (element.classed('compensation-score')) {
        if (isSelected) {
          if (d.scoreRank === 1 || d?.infoMatchFlag === 'EXACT_MATCH' || d.inGroupIndex === 0) {
            element.attr('fill', '#727982');
          } else {
            element.attr('fill', 'transparent');
          }
        } else {
          element.attr('fill', '#727982');
        }
      }

      // 发射选中数据变化事件
      if (d) {
        if (!isSelected) {
          currentData.value.add(d);
        } else {
          currentData.value.clear();
        }
        emit('selectedData', Array.from(currentData.value));
      }
    });
  };

  return {
    state,
    currentData,
    config,
    initSVG,
    drawAxesAndReferences,
    updateElements,
    // 暴露内部变量的getter
    getSVGElements: () => ({ svg, g, g2, g3, g4, altitudeHeight, interval, processedData }),
    setSVGElements: (elements) => {
      if (elements.svg) svg = elements.svg;
      if (elements.g) g = elements.g;
      if (elements.g2) g2 = elements.g2;
      if (elements.g3) g3 = elements.g3;
      if (elements.g4) g4 = elements.g4;
      if (elements.altitudeHeight !== undefined) altitudeHeight = elements.altitudeHeight;
      if (elements.interval !== undefined) interval = elements.interval;
      if (elements.processedData) processedData = elements.processedData;
    },
  };
}
