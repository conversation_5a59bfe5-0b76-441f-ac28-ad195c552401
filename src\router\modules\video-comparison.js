/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-11 10:16:08
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-14 17:35:05
 * @FilePath: \frontend-template\src\router\modules\video-comparison.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE 
 */
import lineIcon from '@/assets/svg/menus/line.svg'
import lineActiveIcon from '@/assets/svg/menus/line-active.svg'

export default [
  {
    path: '/video-comparison',
    name: 'VideoComparison',
    meta: { title: '视频比对', icon: lineIcon, activeIcon: lineActiveIcon, menu: true, keepAlive: false, layout: 'main' },
    redirect: '/video-comparison/search',
    children: [
      {
        path: '/video-comparison/search',
        name: 'VideoComparisonSearch',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '定位检索', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: '/video-comparison/group',
        name: 'VideoComparisonGroup',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '归一分组', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: '/video-comparison/association',
        name: 'VideoComparisonAssociation',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: 'MN关联', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: '/video-comparison/file',
        name: 'VideoComparisonFile',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '智能归档', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: '/library/find',
        name: 'VideoComparisonFind',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '人像查找', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
    ]
  },
];