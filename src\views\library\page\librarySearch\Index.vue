<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-29 09:51:57
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-30 16:00:50
 * @FilePath: \platform-face-web\src\views\library\page\librarySearch\Index.vue
 * @Description: 
 * 
 * 
-->
<template>
    <div class="library-search-container">
        <!-- 上部分：标题 -->
        <div class="header">
            <div class="d-flex align-items-center">
                <SvgIcon iconClass="comparison-comparison-search-high" size="14px"></SvgIcon>
                <span class="box-title ml-2">人脸搜索</span>
            </div>
            <Divider marginSize="22px" color="#2E475C"></Divider>
        </div>
        <div class="content-wrapper">
            <div class="left-panel">
                <div class="top-section">
                    <ImageUpload></ImageUpload>
                </div>
                <div class="middle-section">
                    <!-- Middle content -->
                    <ComparisonParamsForm :fieldConfig="[{ type: 'portraitGroup', label: '选择人像组/库', showLabel: false, buttonBg: 'transparent', fontSize: '14px' }, {
                        type: 'numberOfReturns', label: '返回结果数量', marks: {
                            0: {
                                style: {
                                    top: '-27px',
                                },
                                label: '0',
                            },
                            10: {
                                style: {
                                    top: '-27px',
                                },
                                label: '10',
                            },
                            20: {
                                style: {
                                    top: '-27px',
                                },
                                label: '20',
                            },
                            30: {
                                style: {
                                    top: '-27px',
                                },
                                label: '30',
                            },
                            40: {
                                style: {
                                    top: '-27px',
                                },
                                label: '40',
                            },
                            50: {
                                style: {
                                    top: '-27px',
                                },
                                label: '50',
                            },
                        }
                    },]" layout="horizontal" :hiddenSubmitButton="true" :isShowIndex="false" headTitle="参数设置"
                        headIcon="comparison-comparison-strategy-gray">
                        <template #item-actions="{ field }">
                            <div v-if="field.type === 'portraitGroup'" class="ml-1">
                                <CustomButtonWithTooltip width="120px" height="32px" fontSize="12px">人像组/库管理
                                </CustomButtonWithTooltip>
                            </div>
                        </template>
                    </ComparisonParamsForm>
                </div>
                <div class="bottom-section">
                    <CustomButtonWithTooltip width="188px" height="42px">开始搜索</CustomButtonWithTooltip>
                </div>
            </div>
            <Divider color="#2E475C" direction="vertical" marginSize="30px" height="90%"></Divider>
            <div class="right-panel">
                <div v-if="loading" class="w-100 h-100 flex-center">
                    <span class="loading-title">搜索人像组-中国乒乓球运动员</span>
                    <SvgIcon icon-class="loading" class="spin-icon" size="24px"></SvgIcon>
                </div>
                <div v-else-if="!resultData || resultData?.length === 0" class="w-100 h-100 flex-center">
                    <a-empty description="暂无结果，请先上传图片" :image="ImageEmpty"></a-empty>
                </div>
                <div v-else class="relative">
                    <div class="right-header d-flex justify-content-between align-items-center">
                        <span style="color: #F1F6F8; font-size: 14px;">搜索结果</span>
                        <div class="d-flex align-items-center">
                            <CustomButtonWithTooltip iconName="top-overview" buttonBg="transparent"
                                hoverBg="transparent">
                            </CustomButtonWithTooltip>
                            <Divider color="#2E475C" direction="vertical" height="9px"></Divider>
                            <CustomButtonWithTooltip iconName="bar-chart-gray" buttonBg="transparent"
                                hoverBg="transparent">
                            </CustomButtonWithTooltip>
                            <Divider color="#2E475C" direction="vertical" height="9px"></Divider>
                            <CustomButtonWithTooltip iconName="bubble-charts-gray" buttonBg="transparent"
                                hoverBg="transparent">
                            </CustomButtonWithTooltip>
                        </div>
                    </div>
                    <div style="height: 75vh;">
                        <LoadMoreList :data="resultData" :has-more="false" key-name="portraitId" layoutType="grid"
                            :gridColumns="2">
                            <template #default="{ item, isSelected }">
                                <div>
                                    <AvatarImage :src="getImageWebURL(item.portrait.standardPortraitImages[0].imageId)"
                                        :gap="7" :ring-thickness="1" ring-color="#2E475C" border-radius="14px"
                                        width="482px" height="283px"
                                        :face-x="item.portrait.standardPortraitImages[0]?.coordinate[0]"
                                        :face-y="item.portrait.standardPortraitImages[0]?.coordinate[1]"
                                        :face-width="item.portrait.standardPortraitImages[0]?.coordinate[2]"
                                        :face-height="item.portrait.standardPortraitImages[0]?.coordinate[3]">
                                    </AvatarImage>
                                    <div class="w-100 flex-between mt-2">
                                        <span style="font-size: 14px; color: #4DBFFF;">相似度：0.88</span>
                                        <Divider color="#2E475C" direction="vertical" height="50px" marginSize="17px">
                                        </Divider>
                                        <div class="flex-1">
                                            <div class="w-100 d-flex" style="gap: 18px;">
                                                <div class="tag-container">
                                                    <SvgIcon iconClass="ci_an-blue" size="12px"></SvgIcon>
                                                    <span>智能融合算法 0.89</span>
                                                    <SvgIcon iconClass="down-arrow-gray" size="9px"></SvgIcon>
                                                </div>
                                                <div class="tag-container">
                                                    <span>入库时间：2025年01月01日</span>
                                                </div>
                                            </div>

                                            <div class="portrait-info mt-2">
                                                <span>姓名：孙颖莎</span>
                                                <Divider color="#2E475C" direction="vertical" height="9px"></Divider>
                                                <span>性别：女</span>
                                                <Divider color="#2E475C" direction="vertical" height="9px"></Divider>
                                                <span>证件号：123456789101112</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </LoadMoreList>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script setup>
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';
import ComparisonParamsForm from '@/components/Comparison/ComparisonParamsForm/index.vue'
import ImageUpload from '../../components/ImageUpload.vue';
import ImageEmpty from "@/assets/svg/empty/image-empty.svg"
import { ref } from 'vue';
import { DEFAULT_CONFIG } from '@/constants';

const { getImageWebURL } = DEFAULT_CONFIG || {};

const loading = ref(false)

const item = {
    "portrait": {
        "id": "202506211033598390000517873712",
        "portraitId": "202506211033598390000517873712",
        "libraryId": "68561615f197700cae8594f6",
        "libraryName": "中国乒乓球",
        "xm": "许昕",
        "xb": "MALE",
        "csrq": "1990-01-08T00:00:00.000+00:00",
        "zjh": "",
        "status": "ADDED",
        "standardPortraitImages": [
            {
                "imageId": "368e71c8-031b-412e-be90-2dd3d38ebe42",
                "idx": "0",
                "coordinate": [
                    "171",
                    "55",
                    "84",
                    "112"
                ],
                "isMultiFace": false
            },
            {
                "imageId": "47ef94c9-504d-4cef-82d3-17113b53710c",
                "idx": "0",
                "coordinate": [
                    "243",
                    "131",
                    "134",
                    "181"
                ],
                "isMultiFace": false
            },
            {
                "imageId": "12dfe4c7-b637-423d-8581-2c2e93f72e10",
                "idx": "0",
                "coordinate": [
                    "69",
                    "64",
                    "159",
                    "211"
                ],
                "isMultiFace": false
            }
        ],
        "createdAt": "2025-06-27T17:17:26.586Z",
        "updatedAt": "2025-06-27T17:17:26.586Z"
    },
    "score": 1,
    "scoreRank": 1,
    "infoMatching": {
        "score": 0,
        "items": []
    }
}
const resultData = ref([item, item, item, item, item, item, item, item, item])
</script>

<style scoped lang="scss">
.library-search-container {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.box-title {
    font-size: 14px;
    color: #F1F6F8;
}

.header {
    margin-bottom: 64px;
}

.content-wrapper {
    display: flex;
    flex: 1;
    gap: 20px;
}

.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;

    .top-section {
        height: 323px;
        border: 1px solid #495464;
        padding: 10px 9px;
        border-radius: 14px;
    }

    .middle-section {
        padding: 8px;
        border: solid 1px #3E4D61;
        border-radius: 14px;
        background: var(--color-primary-bg-image);

        :deep(.portrait-text) {
            padding: 0px;
        }

        :deep(.form-label) {
            font-size: 14px;
        }

        :deep(.ant-slider-horizontal.ant-slider-with-marks) {
            margin-bottom: 0px;
        }
    }

    .bottom-section {
        margin: 0 auto;
    }
}

.right-panel {
    flex: 2;
    height: 75vh;
    color: #8593A7;
    padding-right: 20px;

    .loading-title {
        color: #fff;
        font-size: 14px;
        margin-right: 17px;
    }

    .right-header {
        position: absolute;
        top: 0;
        transform: translateY(-110%);
        width: 100%;
    }
}

.spin-icon {
    animation: spin 2s infinite linear;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.tag-container {
    background: #222937;
    height: 20px;
    border-radius: 10px;
    font-size: 10px;
    color: #B0BBCC;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 12px;
}

.portrait-info {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;

    span {
        color: #F1F6F8;
        white-space: nowrap;
    }
}
</style>
