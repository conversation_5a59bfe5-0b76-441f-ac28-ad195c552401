# AvatarImage 智能头像组件

一个功能强大的 Vue 3 头像组件，整合了基础图片显示、边框装饰、状态徽章、人脸智能居中、箭头指示等多种功能。

## 功能特性

- 🖼️ **基础图片显示**: 支持各种尺寸和样式的图片展示
- 🎨 **边框装饰**: 可自定义边框颜色、厚度、圆角等样式
- 🏷️ **状态徽章**: 支持四个角落的状态标识显示
- 🎯 **人脸智能居中**: 根据人脸坐标自动裁剪和居中显示
- ➡️ **箭头指示器**: 支持右侧和底部箭头指示
- 🔧 **灵活配置**: 通过 props 控制各功能的开启/关闭
- 🎪 **插槽支持**: 丰富的插槽支持自定义内容

## 安装使用

```vue
<template>
  <!-- 基础用法 -->
  <AvatarImage src="/images/avatar.jpg" :width="120" :height="120" border-radius="50%" />

  <!-- 带状态徽章 -->
  <AvatarImage src="/images/user.jpg" :width="100" :height="100" :enable-status="true">
    <template #statusTopRight>
      <div class="online-badge">●</div>
    </template>
  </AvatarImage>

  <!-- 人脸智能居中 -->
  <AvatarImage
    src="/images/portrait.jpg"
    :width="200"
    :height="200"
    :enable-face-center="true"
    :face-x="100"
    :face-y="50"
    :face-width="200"
    :face-height="250" />
</template>

<script setup>
import AvatarImage from '@/components/Common/AvatarImage/index.vue';
</script>
```

## Props 参数

### 基础图片属性

| 参数名 | 类型     | 默认值 | 说明                 |
| ------ | -------- | ------ | -------------------- |
| `src`  | `String` | -      | 图片 URL 地址 (必填) |
| `alt`  | `String` | `''`   | 图片 alt 属性        |

### 边框样式属性

| 参数名            | 类型               | 默认值      | 说明           |
| ----------------- | ------------------ | ----------- | -------------- |
| `ringColor`       | `String`           | `'#2B2F38'` | 边框颜色       |
| `ringThickness`   | `Number`           | `3`         | 边框厚度 (px)  |
| `gap`             | `Number \| String` | `0`         | 边框与图片间距 |
| `width`           | `Number \| String` | `80`        | 组件宽度       |
| `height`          | `Number \| String` | `80`        | 组件高度       |
| `borderRadius`    | `String`           | `'50%'`     | 圆角设置       |
| `backgroundColor` | `String`           | `'#16171c'` | 背景颜色       |
| `overflow`        | `String`           | `'hidden'`  | 溢出处理       |

### 状态徽章功能

| 参数名              | 类型     | 默认值                              | 说明           |
| ------------------- | -------- | ----------------------------------- | -------------- |
| `statusTopLeft`     | `Object` | `{ top: '-12px', left: '16px' }`    | 左上角徽章位置 |
| `statusTopRight`    | `Object` | `{ top: '-12px', right: '16px' }`   | 右上角徽章位置 |
| `statusBottomLeft`  | `Object` | `{ bottom: '-11px', left: '16px' }` | 左下角徽章位置 |
| `statusBottomRight` | `Object` | `{ bottom: '-11px', right: '2px' }` | 右下角徽章位置 |

### 人脸居中功能

| 参数名            | 类型               | 默认值 | 说明              |
| ----------------- | ------------------ | ------ | ----------------- |
| `faceX`           | `Number \| String` | -      | 人脸左上角 X 坐标 |
| `faceY`           | `Number \| String` | -      | 人脸左上角 Y 坐标 |
| `faceWidth`       | `Number \| String` | -      | 人脸宽度          |
| `faceHeight`      | `Number \| String` | -      | 人脸高度          |
| `containerWidth`  | `Number \| String` | `100%` | 容器宽度          |
| `containerHeight` | `Number \| String` | `100%` | 容器高度          |

### 箭头指示器功能

| 参数名           | 类型      | 默认值    | 说明                           |
| ---------------- | --------- | --------- | ------------------------------ |
| `showArrow`      | `Boolean` | `false`   | 是否显示箭头                   |
| `arrowDirection` | `String`  | `'right'` | 箭头方向 ('right' 或 'bottom') |

## 插槽 Slots

| 插槽名              | 说明                                            |
| ------------------- | ----------------------------------------------- |
| `image`             | 自定义图片内容                                  |
| `childImage`        | 自定义人脸居中图片内容 (仅在启用人脸居中时可用) |
| `statusTopLeft`     | 左上角状态徽章内容                              |
| `statusTopRight`    | 右上角状态徽章内容                              |
| `statusBottomLeft`  | 左下角状态徽章内容                              |
| `statusBottomRight` | 右下角状态徽章内容                              |

## 使用示例

### 1. 基础头像

```vue
<AvatarImage src="/images/user-avatar.jpg" :width="80" :height="80" border-radius="50%" ring-color="#4DBFFF" :ring-thickness="2" />
```

### 2. 带在线状态的头像

```vue
<AvatarImage src="/images/user.jpg" :width="100" :height="100" :enable-status="true" border-radius="50%">
  <template #statusBottomRight>
    <div class="status-dot online"></div>
  </template>
</AvatarImage>

<style scoped>
.status-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #fff;
}
.online {
  background-color: #52c41a;
}
</style>
```

### 3. 人脸智能居中头像

```vue
<AvatarImage
  src="/images/portrait.jpg"
  :width="200"
  :height="200"
  :face-x="120"
  :face-y="80"
  :face-width="180"
  :face-height="220"
  border-radius="8px" />
```

### 4. 带箭头指示的身份图片

```vue
<AvatarImage
  src="/images/identity.jpg"
  :width="300"
  :height="160"
  :enable-status="true"
  :show-arrow="true"
  arrow-direction="right"
  :face-x="100"
  :face-y="50"
  :face-width="200"
  :face-height="250"
  border-radius="8px"
  ring-color="#4DBFFF">
  <template #statusTopLeft>
    <span class="confidence-score">98%</span>
  </template>
  <template #statusTopRight>
    <span class="match-icon">✓</span>
  </template>
</AvatarImage>
```

### 5. 方形卡片样式

```vue
<AvatarImage
  src="/images/card.jpg"
  width="100%"
  height="200px"
  :enable-status="true"
  border-radius="12px"
  ring-color="#e0e0e0"
  :ring-thickness="1"
  :gap="4">
  <template #statusTopRight>
    <div class="card-type">VIP</div>
  </template>
</AvatarImage>
```

## 注意事项

1. **人脸坐标**: 人脸坐标基于图片原始尺寸，不是显示尺寸
2. **性能优化**: 人脸居中功能会自动处理图片加载状态
3. **样式继承**: 保持了原组件的所有样式和行为

## 浏览器兼容性

- 现代浏览器 (Chrome 64+、Firefox 69+、Safari 13.1+)
- 需要 ResizeObserver 支持 (人脸居中功能)
