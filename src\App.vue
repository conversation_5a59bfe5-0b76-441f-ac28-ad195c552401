<!--
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-17 10:36:07
 * @FilePath: \platform-face-web\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <a-config-provider :locale="zhCN" :theme="{
    algorithm: theme.darkAlgorithm,
    token: {
      colorPrimary: '#90FBC9',
      colorBgBase: '#16171C',
      fontSize: 13,
    },
  }">
    <component v-if="layoutReady" :is="layoutComponent" />
    <EmptyLayout v-else />
  </a-config-provider>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { theme } from 'ant-design-vue';
import MainLayout from '@/layouts/MainLayout.vue';
import EmptyLayout from '@/layouts/EmptyLayout.vue';

const route = useRoute();
const layoutReady = ref(false);

const layoutComponent = computed(() => {
  return route.meta.layout === 'main' ? MainLayout : EmptyLayout;
});

// 只要 route.meta.layout 发生变化（首次有值），就设置 layoutReady
watch(
  () => route.meta.layout,
  (val) => {
    // 只要 meta.layout 有值（哪怕是 undefined），就说明路由已初始化
    layoutReady.value = true;
  },
  { immediate: true }
);
</script>
