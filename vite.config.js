/*
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 16:12:17
 * @FilePath: \platform-face-web\vite.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import { createHtmlPlugin } from 'vite-plugin-html';
import { join } from 'path';
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import topLevelAwait from 'vite-plugin-top-level-await';

const proxy = {
  '/api': {
    target: 'http://**************:8083', // 需改为真实后端
    // target: 'http://*************:7794', // 林杰
    // target: 'http://**************:8083', // 测试环境
    // target: 'http://**************:7790', // 毫锋
    // target: 'http://localhost:4000', // Mockoon
    changeOrigin: true,
    configure: (proxy) => {
      proxy.on('error', (err, req, res) => {
        console.error('Proxy Error:', err);
      });
    },
  },
  '/login': {
    target: 'http://**************:8083', // 需改为真实后端
    // target: 'http://**************:8083', // 测试环境
    // target: 'http://**************:7790', // 毫锋
    // target: 'http://localhost:4000', // Mockoon
    changeOrigin: true,
    configure: (proxy) => {
      proxy.on('error', (err, req, res) => {
        console.error('Proxy Error:', err);
      });
    },
  },
};

export default defineConfig(({ mode }) => {
  // 获取`.env`环境配置文件
  const env = loadEnv(mode, process.cwd());
  return {
    base: env.VITE_BASE_PATH,
    plugins: [
      vue(),
      // 解决 `import { ref , reactive ..... } from 'vue'` 大量引入的问题
      AutoImport({
        imports: ['vue', 'vue-router'], //自动引入
        eslintrc: {
          enabled: false, // 1、改为true用于生成eslint配置。2、生成后改回false，避免重复生成消耗
        },
      }),
      //替换网站标题
      createHtmlPlugin({
        inject: {
          data: {
            title: env.VITE_APP_TITLE,
          },
        },
      }),
      Components({
        resolvers: [AntDesignVueResolver({ importStyle: 'less' })], // 或 'css'
      }),
      // SVG图标插件
      createSvgIconsPlugin({
        iconDirs: [join(__dirname, 'src/assets/svg/common/'), join(__dirname, 'src/assets/svg/')],
        symbolId: 'icon-[dir]-[name]',
      }),
      topLevelAwait({
        promiseExportName: '__tla',
        promiseImportName: (i) => `__tla_${i}`,
      }),
    ],
    resolve: {
      // 配置路径别名
      alias: {
        '@': join(__dirname, 'src'),
        '@api': join(__dirname, 'src/api'),
        '@utils': join(__dirname, 'src/utils'),
      },
    },
    // 配置SVG作为URL导入
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
    },
    // 反向代理解决跨域问题
    server: {
      host: '0.0.0.0', // 局域网别人也可访问
      port: Number(env.VITE_APP_PORT), //端口号
      strictPort: false,
      open: true, // 运行时自动打开浏览器
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'must-revalidate', // 强制缓存重新验证，通常用于确保开发环境的资源总是最新的，在生产环境通常会配置其他值，如 max-age
      },
      proxy,
    },
    //打包配置
    build: {
      cssCodeSplit: true,
      sourcemap: false,
      reportCompressedSize: false,
      chunkSizeWarningLimit: 2048,
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/[hash].js',
          entryFileNames: 'assets/[hash].js',
          assetFileNames: 'assets/[hash][extname]',
          manualChunks: {
            vue: ['vue', 'vuex', 'vue-router', '@vue/compiler-sfc'],
            antdv: ['ant-design-vue', '@ant-design/icons-vue', '@ant-design-vue/use'],
            plugin: ['core-js'],
          },
        },
      },
      outDir: env.VITE_OUT_DIR || 'dist',
      terserOptions: {
        compress: {
          drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
          drop_console: env.VITE_DROP_CONSOLE === 'true',
        },
      },
    },
    optimizeDeps: {
      include: ['sockjs-client', '@ant-design/icons-vue', '@ant-design-vue/use', 'dayjs', 'vue', 'vue-router', 'vuex', 'ant-design-vue', 'jsencrypt'],
      exclude: [],
      force: true,
    },
  };
});
