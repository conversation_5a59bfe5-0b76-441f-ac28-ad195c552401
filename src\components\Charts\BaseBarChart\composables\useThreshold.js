/*
 * @Author: CaiXiaomin
 * @Date: 2025-07-25 09:16:52
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-25 15:46:17
 * @FilePath: \platform-face-web-v0.9.x\src\components\Graph\BaseBarChart\composables\useThreshold.js
 * @Description:
 *
 *
 */
/**
 * 阈值相关的 composable
 * 处理阈值线、阈值矩形和阈值复选框的逻辑
 */
import { ref } from 'vue';
import { toFixedDecimal } from '@/utils/tool';
import { addReferenceLines } from '@/utils/chartUtils';

/**
 * 阈值管理 composable
 * @param {Object} threshold - 组件属性中传递的threshold阈值
 * @param {Object} state - 组件状态
 * @returns {Object} 阈值相关的方法和数据
 */
export function useThreshold(threshold, state) {
  // 阈值复选框配置
  const thresholdCheckbox = ref([
    { id: 'all', label: '显示全部', x: 1050, y: -132, color: 'white', isChecked: state.allDataChecked },
    { id: '0.85', label: '0.85 高概览阈值', x: 1050, y: -102, color: '#90fbc9', isChecked: true },
    { id: '0.75', label: '0.75 中概览阈值', x: 1050, y: -72, color: '#69b293', isChecked: true },
    { id: '0.65', label: '0.65 低概览阈值', x: 1050, y: -42, color: '#ff7f6b', isChecked: true },
    { id: toFixedDecimal(threshold, 2), label: `${toFixedDecimal(threshold, 2)} 设定阈值`, x: 1050, y: -12, color: '#ffd86b', isChecked: true },
  ]);

  /**
   * 更新阈值复选框位置
   * @param {number} maxScore - 最大分数
   */
  const updateThresholdCheckbox = (maxScore) => {
    // 根据maxScore过滤掉不需要的阈值选项
    const filteredCheckbox = thresholdCheckbox.value.filter((checkbox) => {
      if (maxScore < 0.65) {
        return checkbox.id !== '0.85' && checkbox.id !== '0.75';
      } else if (maxScore < 0.75) {
        return checkbox.id !== '0.85';
      } else {
        return true;
      }
    });

    filteredCheckbox.sort((a, b) => {
      if (a.id === 'all') return -1; // all 始终排在第一位
      if (b.id === 'all') return 1; // all 始终排在第一位
      return parseFloat(b.id) - parseFloat(a.id); // 其余按 id 从大到小排序
    });

    // 重新计算y坐标
    const baseY = -132;
    const step = 30;
    filteredCheckbox.forEach((checkbox, index) => {
      checkbox.x = state.chartWidth + 50; // 调整多选框的位置
      checkbox.y = baseY + index * step;
    });

    return filteredCheckbox;
  };

  /**
   * 更新设定阈值线和阈值矩形
   * @param {number} threshold - 阈值
   * @param {number} chartWidth - 图表宽度
   * @param {d3.Selection} g3 - 阈值组选择器
   * @param {number} altitudeHeight - 高度的一半
   */
  const updateThresholdLineAndRect = (threshold, chartWidth, g3, altitudeHeight) => {
    try {
      g3.selectAll('*').remove();
      const thresholdNum = parseFloat(toFixedDecimal(threshold, 2)) * -1;
      const setThreshold = [
        { value: thresholdNum, color: '#ffd86b', dashArray: '', width: 1, x1: 34 }, // 设定阈值线
      ];
      addReferenceLines(g3, setThreshold, chartWidth, altitudeHeight);
      g3.append('rect')
        .attr('x', 34)
        .attr('y', thresholdNum * altitudeHeight)
        .attr('width', chartWidth - 34)
        .attr('height', Math.abs(thresholdNum) * altitudeHeight)
        .attr('fill', 'url(#yellowGradient)')
        .style('opacity', 0.15);
    } catch (error) {
      console.log('绘制阈值线和矩形错误', error);
    }
  };

  return {
    thresholdCheckbox,
    updateThresholdCheckbox,
    updateThresholdLineAndRect,
  };
}
