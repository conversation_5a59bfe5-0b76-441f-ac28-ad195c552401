// BaseResultFilter 共享样式
// 从原组件中提取的样式，确保拆分后样式一致性

// 全局选择器样式
:deep(.ant-select-selector) {
  background-color: #24394e !important;
  background: #24394e !important;
}

:deep(.ant-select) {
  border: 2px solid transparent !important;
}

:deep(.ant-select-focused) {
  border: 2px solid #24394e !important;

  .ant-select-selector {
    box-shadow: none !important;
  }
}

:deep(.ant-select-dropdown) {
  background-color: #242A36 !important;
  background: #242A36 !important;
  border-radius: 8px !important;
}

// 梯形区域特殊样式
.trapezoid {
  height: 45px;
  position: relative;
  background-color: var(--color-bg-1);
  clip-path: polygon(15% 0, 100% 0, 85% 100%, 0 100%);
  padding: 5px 45px;
  z-index: 1;

  :deep(.ant-select-selector) {
    background-color: var(--color-bg-1) !important;
    background: var(--color-bg-1) !important;
  }

  :deep(.ant-select) {
    border: 2px solid transparent !important;
  }

  :deep(.ant-select-focused) {
    border: 2px solid var(--color-bg-1) !important;

    .ant-select-selector {
      box-shadow: none !important;
    }
  }
}

// 动画效果
.blinking-red-dot {
  width: 8px;
  height: 8px;
  background-color: #FF3D5B;
  border-radius: 50%;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

// 通用工具类
.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 20px;
}

.label {
  color: inherit;
  font-size: inherit;
}

// 算法选择器特殊样式
.algorithm-div {
  left: 70px;
}

// 响应式设计
@media (max-width: 768px) {
  .matching-filter-container {
    flex-direction: column;
    height: auto;
    gap: 8px;
  }

  .inner-container {
    flex-wrap: wrap;
    height: auto;
    padding: 8px;
  }

  .filter-item {
    padding: 0 10px;
  }
}
