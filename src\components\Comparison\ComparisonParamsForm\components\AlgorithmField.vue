<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 17:15:07
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 17:52:49
 * @FilePath: \platform-face-web\src\components\Comparison\ComparisonParamsForm\components\AlgorithmField.vue
 * @Description:  算法字段组件，算法多选组件，包含算法图标显示和多选逻辑
 * 
-->
<template>
  <div class="algorithm-field-wrapper">
    <div v-if="modelValue?.length > 1" class="d-flex align-items-center z-1 position-absolute ml-2 h-100">
      <SvgIcon v-for="(item, index) in algorithmOptions" :key="index"
        :icon-class="`${item.value}-${modelValue.includes(item.value) ? 'blue' : 'gray'}`" size="17px" class="mr-2" />
    </div>
    <a-select :value="modelValue" class="form-input" :options="algorithmOptions" mode="multiple"
      :default-active-first-option="false" placement="bottomLeft" placeholder="请选择算法" :show-search="false"
      :get-popup-container="getPopupContainer" :disabled="disabled" @change="handleChange">
      <template #tagRender="{ value: val, label }">
        <div v-if="!(modelValue?.length > 1)" class="w-100 d-flex align-items-center g-1">
          <SvgIcon :icon-class="`${val}-blue`"></SvgIcon>
          <span style="color: #4dbfff;">{{ label }}</span>
        </div>
      </template>
      <template #option="{ value: val, label }">
        <div class="d-flex">
          <SvgIcon :icon-class="`${val}-${modelValue.includes(val) ? 'blue' : 'gray'}`" size="17px" class="mr-2" />
          <span>{{ label }}</span>
        </div>
      </template>
    </a-select>
    <SvgIcon class="cursor-pointer position-absolute" icon-class="down-arrow-high" style="right: 7%;top:32%"
      size="10px">
    </SvgIcon>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  algorithmOptions: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  getPopupContainer: {
    type: Function,
    default: () => document.body
  }
});

const emits = defineEmits(['update:modelValue', 'change']);

const handleChange = (value) => {
  emits('update:modelValue', value);
  emits('change', value);
};
</script>

<style scoped lang="scss">
@use '../styles/form-styles.scss';

.algorithm-field-wrapper {
  position: relative;
  width: 100%;
}
</style>
