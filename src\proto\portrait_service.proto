syntax = "proto3";

package portrait;

import "google/protobuf/timestamp.proto";

option java_package = "zky.platform.portrait.grpc";
option java_multiple_files = true;

service PortraitService {
  rpc GetLibrary(GetLibraryRequest) returns (Library);
  rpc ListLibraries(ListLibrariesRequest) returns (LibraryList);
  rpc CreateLibrary(CreateLibraryRequest) returns (Library);
  rpc UpdateLibrary(UpdateLibraryRequest) returns (Library);
  rpc DeleteLibrary(DeleteLibraryRequest) returns (DeleteResponse);
  rpc GetGroup(GetGroupRequest) returns (Group);
  rpc ListGroups(ListGroupsRequest) returns (GroupList);
  rpc CreateGroup(CreateGroupRequest) returns (Group);
  rpc UpdateGroup(UpdateGroupRequest) returns (Group);
  rpc DeleteGroup(DeleteGroupRequest) returns (DeleteResponse);

  rpc PagedQueryPortraits(PagedQueryPortraitsRequest) returns (PortraitList);
  rpc SearchPortraits(SearchPortraitsRequest) returns (PortraitList);
  rpc FindPortraitById(FindPortraitByIdRequest) returns (Portrait);
  rpc FindPortraitsByIds(FindPortraitsByIdsRequest) returns (PortraitList);
  rpc FindPortraitsByIdsLibraries(FindPortraitsByIdsLibrariesRequest) returns (PortraitList);

}

message Library {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string tags = 4;
  string createdBy = 5;
  string createdAt = 6;
}

message Group {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string libraryIds = 4;
  string createdBy = 5;
  string createdAt = 6;
}

message Portrait {
  string portraitId = 1;
  string libraryId = 2;
  string name = 3;
  string gender = 4;
  google.protobuf.Timestamp birthDate = 5;
  string identityCardNumber = 6;

  map<string, string> attributes = 7;

  repeated PortraitImage portraitImages = 8;
  repeated PortraitImage otherImages = 9;

  bool deleted = 10;
}

message PortraitImage {
  string imageId = 1;
  string imagePath = 2;
  string object = 3;
  map<string, string> meta = 4;
}

message LibraryList {
  repeated Library items = 1;
}

message GroupList {
  repeated Group items = 1;
}

message PortraitList {
  repeated Portrait items = 1;
}

message DeleteResponse {
  bool success = 1;
  string message = 2;
}

message GetLibraryRequest {
  string id = 1;
}

message ListLibrariesRequest {}

message CreateLibraryRequest {
  string name = 1;
  string description = 2;
  repeated string tags = 3;
}

message UpdateLibraryRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string tags = 4;
}

message DeleteLibraryRequest {
  string id = 1;
}

message GetGroupRequest {
  string id = 1;
}

message ListGroupsRequest {
}

message CreateGroupRequest {
  string name = 1;
  string description = 2;
  repeated string libraryIds = 3;
}

message UpdateGroupRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string libraryIds = 4;
}

message DeleteGroupRequest {
  string id = 1;
}

message PagedQueryPortraitsRequest {
  string libraryId = 1;
  string afterId = 2;
  int32 limit = 3;
}

message SearchPortraitsRequest {
  repeated string libraryIds = 1;
  PortraitSearchCriteria criteria = 2;
  int32 limit = 3;
}

message FindPortraitByIdRequest {
  string libraryId = 1;
  string portraitId = 2;
}

message FindPortraitsByIdsRequest {
  string libraryId = 1;
  repeated string portraitIds = 2;
}

message FindPortraitsByIdsLibrariesRequest {
  repeated string libraryIds = 1;
  repeated string portraitIds = 2;
}

message PortraitSearchCriteria {
  string name = 1;
  bool isContaining = 2;
  string gender = 3;
  string identityCardNumber = 4;
  string status = 5;
  int32 minAge = 6;
  int32 maxAge = 7;
  string afterId = 8; // 游标分页支持
}
