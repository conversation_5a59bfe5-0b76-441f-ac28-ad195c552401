# ComparisonParamsPopover 比对参数弹窗组件

## 📖 组件概述

ComparisonParamsPopover 是一个将比对参数表单包装在弹出窗中的高级组件。它自动处理算法列表和人像组数据的获取，提供悬停显示的弹窗交互，简化了比对参数配置的调用流程。

## ✨ 主要功能

- 🎯 **智能弹窗**：基于鼠标悬停的智能弹窗显示/隐藏逻辑
- 🔄 **数据自动获取**：自动请求算法列表和人像组数据，无需手动传入
- 🎨 **灵活布局**：支持上下两种弹窗位置，支持分离模式定位
- 🎪 **插槽支持**：支持自定义触发按钮，提供丰富的插槽参数
- 📱 **响应式交互**：智能的鼠标交互逻辑，支持延时隐藏
- 🔧 **参数预设**：支持默认参数配置和表单数据预填充

## 🏗️ 组件架构

```
ComparisonParamsPopover
├── 触发按钮 (CustomButtonWithTooltip 或自定义插槽)
└── 弹出窗口
    └── ComparisonParamsForm (比对参数表单)
```

## 📋 Props 参数

### 基础配置

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `comparisonType` | String | `'image-to-library'` | 比对类型：`'image-to-library'` 或 `'image-to-image'` |
| `buttonText` | String | `''` | 自定义按钮文本，为空时显示默认文本 |
| `defaultValues` | Object | `{}` | 表单默认值配置 |

### 弹窗配置

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `placement` | String | `'top'` | 弹窗位置：`'top'` 或 `'bottom'` |
| `separated` | Boolean | `false` | 是否启用分离模式（独立定位） |
| `separatedOffsetX` | String | `'-200px'` | 分离模式下的水平偏移量 |

### 功能配置

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `requirePortraitFirst` | Boolean | `false` | 是否要求优先选择人像组 |
| `usedPortraitLibraryGroupInfos` | Array | `[]` | 已使用的人像库组信息，用于禁用选项 |

## 📤 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `submit-success` | `formData: Object` | 表单提交成功事件，返回处理后的表单数据 |
| `comparisonChange` | `formData: Object` | 表单数据变更事件（透传自 ComparisonParamsForm） |

## 🎪 Slots 插槽

### button 插槽

自定义触发按钮，提供以下插槽参数：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `handleConfirm` | Function | 确认提交函数 |
| `disabled` | Boolean | 按钮禁用状态 |
| `handleButtonMouseEnter` | Function | 按钮鼠标进入事件处理函数 |
| `handleButtonMouseLeave` | Function | 按钮鼠标离开事件处理函数 |
| `isButtonHovered` | Boolean | 按钮悬停状态 |

## 🚀 使用方式

### 1. 基础用法

```vue
<template>
  <!-- 使用默认按钮 -->
  <ComparisonParamsPopover
    :comparison-type="'image-to-library'"
    @submit-success="handleSubmit"
    @comparison-change="handleChange" />
</template>

<script setup>
const handleSubmit = (formData) => {
  console.log('提交的表单数据:', formData);
};

const handleChange = (formData) => {
  console.log('表单数据变更:', formData);
};
</script>
```

### 2. 自定义按钮文本

```vue
<template>
  <ComparisonParamsPopover
    button-text="开始比对检索"
    :comparison-type="'image-to-library'"
    @submit-success="handleSubmit" />
</template>
```

### 3. 自定义触发按钮

```vue
<template>
  <ComparisonParamsPopover @submit-success="handleSubmit">
    <template #button="{ handleConfirm, disabled, handleButtonMouseEnter, handleButtonMouseLeave }">
      <a-button 
        :disabled="disabled"
        @click="handleConfirm"
        @mouseenter="handleButtonMouseEnter"
        @mouseleave="handleButtonMouseLeave">
        自定义按钮
      </a-button>
    </template>
  </ComparisonParamsPopover>
</template>
```

### 4. 配置弹窗位置

```vue
<template>
  <!-- 弹窗显示在下方 -->
  <ComparisonParamsPopover
    placement="bottom"
    :comparison-type="'image-to-image'"
    @submit-success="handleSubmit" />
</template>
```

### 5. 分离模式

```vue
<template>
  <!-- 启用分离模式，自定义偏移量 -->
  <ComparisonParamsPopover
    :separated="true"
    separated-offset-x="-300px"
    @submit-success="handleSubmit" />
</template>
```

### 6. 预设默认值

```vue
<template>
  <ComparisonParamsPopover
    :default-values="defaultParams"
    :require-portrait-first="true"
    @submit-success="handleSubmit" />
</template>

<script setup>
const defaultParams = {
  algorithmCodes: ['ci_an', 'face_rec'],
  numberOfReturns: 100,
  matchingThreshold: 0.8,
  portraitLibraryGroupId: 'group-123'
};
</script>
```

### 7. 禁用已使用的人像库

```vue
<template>
  <ComparisonParamsPopover
    :used-portrait-library-group-infos="usedGroups"
    @submit-success="handleSubmit" />
</template>

<script setup>
const usedGroups = [
  { libraryGroupId: 'group-1' },
  { libraryGroupId: 'group-2' }
];
</script>
```

## 🎯 交互逻辑

### 普通模式
- 鼠标悬停按钮 → 显示弹窗
- 鼠标离开按钮 → 延时200ms隐藏弹窗
- 鼠标进入弹窗 → 取消隐藏，保持显示
- 鼠标离开弹窗 → 延时200ms隐藏弹窗

### 分离模式
- 鼠标悬停按钮 → 显示/隐藏弹窗（切换模式）
- 弹窗显示时再次悬停按钮 → 隐藏弹窗
- 鼠标离开按钮 → 如果弹窗未悬停则延时隐藏

## 📊 数据处理

### 自动数据获取
组件会在挂载时自动获取：
- **算法列表**：通过 `getAlgorithms()` API 获取可用算法
- **人像组列表**：通过 `getAllPortraitGroup()` API 获取人像组数据

### 表单数据转换
提交时会自动处理表单数据：
```javascript
// 原始表单数据
{
  portraitLibraryId: 'group-123',
  algorithmCodes: ['ci_an'],
  matchingThreshold: 0.7,
  // ...其他字段
}

// 转换后的提交数据
{
  portraitLibraryIds: ['group-123'], // 转换为数组格式
  algorithmCodes: ['ci_an'],
  matchingThreshold: 0.7,
  strategyId: '', // 如果表单有变更，清空策略ID
  // ...其他字段
}
```

## 🎨 样式定制

### CSS 变量
```scss
.popover {
  background: var(--color-primary-bg-image);
  border-radius: 8px;
  box-shadow: 5px 10px 10px rgba(0, 0, 0, 0.3);
}
```

### 自定义样式类
- `.container` - 容器样式
- `.popover` - 弹窗样式
- `.popover.top` - 上方弹窗
- `.popover.bottom` - 下方弹窗
- `.popover.separated` - 分离模式弹窗

## 💡 最佳实践

1. **合理选择弹窗位置**：根据按钮在页面中的位置选择 `placement`
2. **使用分离模式**：在空间受限的场景下使用分离模式
3. **预设默认值**：为常用场景预设合理的默认参数
4. **处理禁用状态**：通过 `usedPortraitLibraryGroupInfos` 避免重复选择
5. **监听数据变更**：使用 `comparisonChange` 事件实时响应用户操作

## 🔧 依赖组件

- `ComparisonParamsForm` - 比对参数表单组件
- `CustomButtonWithTooltip` - 自定义按钮组件
- `lodash` - 工具库（cloneDeep, isEqual）

## 🌟 优势特点

- ✅ **开箱即用**：自动处理数据获取，无需手动配置
- ✅ **交互友好**：智能的悬停逻辑，用户体验良好
- ✅ **高度可定制**：支持多种配置选项和插槽定制
- ✅ **数据处理**：自动处理表单数据格式转换
- ✅ **状态管理**：完整的表单状态和验证逻辑

## 📝 注意事项

1. **API 依赖**：组件依赖 `getAlgorithms` 和 `getAllPortraitGroup` API
2. **数据格式**：确保 API 返回的数据格式符合组件预期
3. **性能考虑**：数据获取在组件挂载时进行，避免频繁创建销毁
4. **错误处理**：API 请求失败时会在控制台输出错误信息

## 🔄 版本信息

- **作者**: yuzhouisme, CaiXiaomin
- **创建日期**: 2025-03-25
- **最后修改**: 2025-07-23
- **依赖**: Vue 3.x, Ant Design Vue 4.x
