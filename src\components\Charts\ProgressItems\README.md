# ProgressItems 进度图形组合组件

## 组件简介 | Introduction

`ProgressItems` 是一个支持多种进度图形（圆形、矩形、三角形）组合展示的父组件。通过传递数组参数，可灵活渲染任意数量、任意类型的进度图形，并支持横向/竖向布局，适用于多步骤进度、流程节点等场景。

`ProgressItems` is a parent component for displaying multiple progress shapes (circle, rectangle, triangle) in combination. It supports flexible rendering of any number and type of progress shapes via array props, and allows horizontal/vertical layout. Suitable for multi-step progress, workflow nodes, etc.

---

## 内置图形子组件介绍 | Built-in Shape Components

本组件内置三种进度图形子组件，均支持通过 props 灵活配置：

- **CircleProgress**：圆形进度条，适合展示环形进度。
- **RectProgress**：矩形进度条，支持圆角，适合展示方形或流程节点进度。
- **TriangleProgress**：三角形进度条，适合特殊流程或自定义节点。

### 通用 Props
| 属性名       | 类型    | 默认值     | 说明（中文）         | Description (EN)         |
|--------------|---------|------------|----------------------|--------------------------|
| value        | Number  | 0          | 进度值，0~1          | Progress value, 0~1      |
| label        | String  | ''         | 进度标签             | Progress label           |
| color        | String  | #4079AA    | 进度条颜色           | Progress color           |
| trackColor   | String  | #2F3C50    | 轨道颜色             | Track color              |
| bgColor      | String  | transparent| 底色（背景色）        | Background color         |
| size         | Number  | 80         | 组件尺寸             | Component size           |
| strokeWidth  | Number  | 8          | 进度条宽度           | Progress bar width       |

> 其中 `RectProgress` 还支持 `radius`（圆角半径）。

#### 用法示例
```vue
<CircleProgress :value="0.7" label="上传" color="#3a7adf" bgColor="#f5f5f5" />
<RectProgress :value="0.5" label="智能分组" color="#ff9800" :radius="12" bgColor="#eee" />
<TriangleProgress :value="1" label="比对" color="#4caf50" bgColor="#fffbe6" />
```

---

## 属性说明 | Props

| 属性名   | 类型    | 默认值 | 说明（中文）                                   | Description (EN)                       |
|----------|---------|--------|-----------------------------------------------|----------------------------------------|
| items    | Array   | []     | 进度项数组，每项包含 type、value、label 等     | Progress items array, see below         |
| layout   | String  | 'row'  | 布局方式，'row' 横向，'column' 竖向            | Layout: 'row' (horizontal), 'column' (vertical) |

### items 示例 | Example of items
```js
[
  { type: 'circle', value: 0.7, label: '上传', color: '#3a7adf', bgColor: '#f5f5f5' },
  { type: 'rect', value: 0.5, label: '智能分组', color: '#ff9800', radius: 12, bgColor: '#eee' },
  { type: 'triangle', value: 1, label: '比对', color: '#4caf50', bgColor: '#fffbe6' }
]
```
- `type`：'circle' | 'rect' | 'triangle'，指定图形类型
- 其余参数会透传给对应子组件

---

## 用法示例 | Usage Example

```vue
<script setup>
import ProgressItems from './Index.vue';
const items = [
  { type: 'circle', value: 0.7, label: '上传', color: '#3a7adf', bgColor: '#f5f5f5' },
  { type: 'rect', value: 0.5, label: '智能分组', color: '#ff9800', radius: 12, bgColor: '#eee' },
  { type: 'triangle', value: 1, label: '比对', color: '#4caf50', bgColor: '#fffbe6' }
];
</script>
<template>
  <ProgressItems :items="items" layout="row" />
</template>
```

---

## 布局说明 | Layout
- `layout="row"` 横向排列（默认）
- `layout="column"` 竖向排列

---

## 子组件扩展 | Subcomponent Extension
- 支持 `CircleProgress`、`RectProgress`、`TriangleProgress` 三种图形
- 可通过 items 数组灵活组合、扩展
- 如需更多形状，可自定义子组件并在父组件中注册

---

## 维护与贡献 | Maintenance & Contribution
如需扩展功能或修复 bug，欢迎提交 PR 或 issue。

---

## License
MIT 