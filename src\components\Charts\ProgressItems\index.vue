<template>
  <!--
    ProgressItems 进度图形组合组件
    根据 items 数组动态渲染多种进度图形（圆形、矩形、三角形），支持横向/竖向布局
    ProgressItems: Render multiple progress shapes (circle, rect, triangle) by items array, support row/column layout
  -->
  <div :class="['progress-items-container', layoutClass]">
    <component
      v-for="(item, idx) in items"
      :key="idx"
      :is="getComponentType(item.type)"
      v-bind="item"
    />
  </div>
</template>

<script setup>
import CircleProgress from './components/CircleProgress.vue';
import RectProgress from './components/RectProgress.vue';
import TriangleProgress from './components/TriangleProgress.vue';
import { computed } from 'vue';

/**
 * 进度图形组合组件
 * @property {Array} items - 进度项数组，每项包含type、value、label等，决定渲染的图形和内容
 * @property {string} layout - 布局方式，'row'横向，'column'竖向
 */
const props = defineProps({
  items: {
    type: Array,
    default: () => [], // [{type: 'circle'|'rect'|'triangle', value: 0.5, label: 'xxx', ...}]
  },
  layout: {
    type: String,
    default: 'row', // 'row' | 'column'
  },
});

// 布局class，决定横向还是竖向排列
const layoutClass = computed(() => (props.layout === 'column' ? 'vertical' : 'horizontal'));

/**
 * 根据type返回对应的进度图形子组件
 * @param {string} type - 图形类型
 * @returns {object} 组件对象
 */
function getComponentType(type) {
  switch (type) {
    case 'circle':
      return CircleProgress;
    case 'rect':
      return RectProgress;
    case 'triangle':
      return TriangleProgress;
    default:
      return CircleProgress;
  }
}
</script>

<style scoped>
.progress-items-container {
  display: flex;
  gap: 10px;
  width: 100%;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
.horizontal {
  flex-direction: row;
}
.vertical {
  flex-direction: column;
}
</style>