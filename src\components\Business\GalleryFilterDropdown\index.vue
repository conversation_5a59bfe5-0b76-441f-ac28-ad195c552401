<!-- 图册的筛选项 -->
<template>
    <div ref="filterDropdown" class="filter-dropdown-container">
        <span :style="{ 'height': iconHeight }">
            <CustomButtonWithTooltip icon-name='filter-gray' :icon-size="iconSize" :button-bg="'none'"
                :hover-bg="'none'" :height="iconHeight" :disabled="disabled" @mouseenter="handleTrigger"
                @click="handleTrigger" />
        </span>

        <transition name="fade">
            <div v-if="isOpen" class="dropdown-panel" :class="{ 'placement-left': placement === 'left' }"
                @mouseleave="handleMouseLeave" @click.stop>

                <!-- 排序 -->
                <div v-if="showSort && currentSort" class="header-row">
                    <div class="d-flex align-items-center g-2">
                        <span>{{ currentSort.label }}</span>
                    </div>
                    <SvgIcon icon-class="checked" width="8px" height="10px" />
                </div>

                <!-- 处理"筛选"和"显示全部" -->
                <div v-if="showSelectAll" class="dropdown-header">
                    <div class="header-row" :class="{ selected: !isAllSelected }">
                        <span class="header-text">筛选</span>
                        <SvgIcon v-if="!isAllSelected" icon-class="checked" width="8px" height="10px" />
                    </div>
                    <div class="header-row" :class="{ selected: isAllSelected }" @click="setFilterState('all')">
                        <span class="header-text">显示全部</span>
                        <SvgIcon v-if="isAllSelected" icon-class="checked" width="8px" height="10px" />
                    </div>
                </div>

                <div class="filter-container">
                    <!-- 遍历每个分组 -->
                    <div v-for="(group, groupIndex) in groupedItems" :key="groupIndex" class="filter-group">
                        <!-- 分组标题 -->
                        <div v-if="group.name" class="d-flex align-items-center g-1">
                            <Divider />
                            <div class="group-label">{{ group.name }}</div>
                        </div>

                        <!-- 遍历分组中的每个选项 -->
                        <FilterItem v-for="(item, idx) in group.items" :key="`item-${groupIndex}-${idx}`" :item="item"
                            :selected-values="modelValue" @toggle="toggleSelection" />
                    </div>
                </div>

            </div>
        </transition>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import Divider from '@/components/common/Divider/index.vue';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import CustomButtonWithTooltip from '@/components/common/CustomButtonWithTooltip/index.vue';
import FilterItem from './components/FilterItem.vue'

const props = defineProps({
    // 数据相关
    options: {
        type: Array,
        default: () => []
    },
    modelValue: {
        type: Array,
        default: () => []
    },

    // 功能配置
    showSort: {
        type: Boolean,
        default: true
    },
    sortOptions: {
        type: Array,
        default: () => [
            { label: '按最近添加排序', value: 'recent', order: 'desc' }
        ]
    },
    showSelectAll: {
        type: Boolean,
        default: true
    },

    // 样式配置
    placement: {
        type: String,
        default: 'right',
        validator: (value) => ['left', 'right'].includes(value)
    },
    iconHeight: {
        type: String,
        default: '27px'
    },
    iconSize: {
        type: String,
        default: '14px'
    },

    // 交互配置
    trigger: {
        type: String,
        default: 'hover',
        validator: (value) => ['hover', 'click'].includes(value)
    },
    closeOnSelect: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const emits = defineEmits(['update:modelValue', 'change', 'sort-change', 'open', 'close'])

const isOpen = ref(false);
const currentSort = ref(props.sortOptions[0] || null);

// 建立值到项目的映射关系
const valueToItemMap = computed(() => {
    const map = new Map()

    function traverse(items) {
        items.forEach(item => {
            if (item.value) {
                map.set(item.value, item)
            }
            if (item.children) {
                traverse(item.children)
            }
        })
    }

    groupedItems.value.forEach(group => {
        if (group.items) {
            traverse(group.items)
        }
    })

    return map
})

// 获取所有可选的值
const allOptions = computed(() => {
    const values = [];

    function traverse(items) {
        items.forEach(item => {
            if (item.value) {
                values.push(item.value);
            }
            if (item.children) {
                traverse(item.children);
            }
        })
    }

    groupedItems.value.forEach(group => {
        if (group.items) {
            traverse(group.items)
        }
    })

    return values;
});

// 获取选中的完整项目信息
const selectedItems = computed(() => {
    return props.modelValue.map(value => valueToItemMap.value.get(value)).filter(Boolean)
})

// 使用传入的 options 作为数据源
const groupedItems = computed(() => props.options)

// 如果已选中所有选项，则视为"显示全部"
const isAllSelected = computed(() => {
    return allOptions.value.every(opt => props.modelValue.includes(opt));
});

// 更新选中值的方法
const updateModelValue = (newValues) => {
    emits('update:modelValue', newValues)
    emits('change', newValues, newValues.map(value => valueToItemMap.value.get(value)).filter(Boolean))
}

const toggleSelection = (valueOrValues) => {
    let newValues

    // 批量更新
    if (Array.isArray(valueOrValues)) {
        newValues = [...valueOrValues];
    }
    // 单个值切换
    else {
        const currentValues = [...props.modelValue]
        const index = currentValues.indexOf(valueOrValues);
        // 多选模式
        if (index === -1) {
            newValues = [...currentValues, valueOrValues];
        } else {
            newValues = currentValues.filter(v => v !== valueOrValues);
        }
    }

    updateModelValue(newValues)

    // 如果配置了选择后关闭,则关闭下拉框
    if ((props.closeOnSelect) && !Array.isArray(valueOrValues)) {
        isOpen.value = false
        emits('close')
    }
};

const setFilterState = (state) => {
    if (state === 'all') {
        // 选中全部
        updateModelValue([...allOptions.value])
    }
};

// 处理触发器事件
const handleTrigger = () => {
    if (props.disabled) return

    if (props.trigger === 'hover') {
        isOpen.value = true
        emits('open')
    } else if (props.trigger === 'click') {
        isOpen.value = !isOpen.value
        emits(isOpen.value ? 'open' : 'close')
    }
}

const handleMouseLeave = () => {
    if (props.trigger === 'hover') {
        isOpen.value = false;
        emits('close')
    }
};

const filterDropdown = ref(null);

const handleClickOutside = (e) => {
    if (!filterDropdown.value.contains(e.target)) {
        isOpen.value = false;
        emits('close')
    }
};

// 暴露方法给外部使用
defineExpose({
    selectedItems,
    selectedValues: computed(() => props.modelValue),
    getItemByValue: (value) => valueToItemMap.value.get(value),
    selectAll: () => updateModelValue([...allOptions.value]),
    clearAll: () => updateModelValue([]),
    open: () => {
        isOpen.value = true
        emits('open')
    },
    close: () => {
        isOpen.value = false
        emits('close')
    }
})

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.filter-dropdown-container {
    position: relative;
    display: inline-block;
}

.dropdown-panel {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 220px;
    background-color: #242A36;
    border: 1px solid #555;
    border-radius: 12px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 9999;
}

.dropdown-panel.placement-left {
    right: auto;
    left: 0;
}

.group-label {
    font-size: 10px;
    white-space: nowrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.dropdown-header {
    margin-bottom: 12px;
    border-bottom: 1px solid #444;
    padding-bottom: 8px;
}

.header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    gap: 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    margin-bottom: 8px;
}

.header-row:hover {
    background-color: #2b2b2b;
}

.header-row.selected {
    background-color: #393939;
    color: #FFFFFF;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
