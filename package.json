{"name": "vue3-js-template", "private": true, "version": "2.0.0", "description": "一套基于vue3、js、vite5的项目模板，封装了axios，vue-router，scss，配置了vite.config.js。", "type": "module", "scripts": {"dev": "vite --mode development", "build:pro": "vite build --mode production", "build:test": "vite build --mode test", "build:dev": "vite build --mode development", "preview": "vite preview", "proto": "npx pbjs -t static-module -w commonjs --main -o src/proto/portrait_service_pb.js src/proto/portrait_service.proto", "deploy": "pnpm run build:pro && git add . && git commit -m \"gitee自动部署\" && git push -u origin master && git subtree push --prefix dist-pro origin gh-pages"}, "dependencies": {"@ant-design-vue/use": "^0.0.1-alpha.10", "@ant-design/icons-vue": "^5.1.9", "@antv/g": "^6.1.27", "@antv/g6": "^5.0.49", "@antv/layout": "^1.2.14-beta.9", "@stomp/stompjs": "^6.1.2", "@videojs-player/vue": "^1.0.0", "@vueuse/core": "^9.13.0", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "core-js": "^3.40.0", "crypto-js": "^4.2.0", "d3": "^7.9.0", "dashjs": "^4.7.3", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "echarts": "^5.6.0", "google-protobuf": "^3.21.4", "grpc-web": "^1.5.0", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "path-to-regexp": "^7.1.0", "sockjs-client": "^1.6.1", "uuid": "^11.0.3", "vue": "^3.4.21", "vue-drag-resize": "^1.5.4", "vue-draggable-resizable": "^3.0.0", "vue-lazyload": "^3.0.0", "vue-router": "^4.3.0", "vue-waterfall-plugin-next": "^2.6.2", "vue3-draggable-resizable": "^1.6.5", "vuex": "^4.0.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.6", "@vitejs/plugin-vue": "^6.0.0", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "prettier": "3.0.3", "protobufjs": "^7.5.3", "sass": "^1.69.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.4", "vite-plugin-html": "^3.2.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-top-level-await": "^1.5.0"}}