/**
 * 柱状图绘制逻辑 composable
 * 专门处理CompareResultsBarGraph的绘制逻辑
 */
import { algorithmShortNames } from '@/api/algorithm';
import {
  drawStandardBar,
  drawBarScoreText,
  drawCompensationScoreText,
  drawAlgorithmBadge,
  drawIcon,
  createStandardBarConfig,
} from '@/utils/barChartDrawingUtils';
import { BAR_CHART_COLORS } from '@/utils/chartUtils.js';
import perfectMatch from '@/assets/svg/common/perfect-match.svg';
import textWeightedMatchingS from '@/assets/svg/common/text-weighted-matching.svg';
import textWeightedMatching from '@/assets/svg/common/text-weighted-matching-gray.svg';

/**
 * 柱状图绘制逻辑 composable
 * @returns {Object} 绘制相关的方法
 */
export function useBarDrawing() {
  /**
   * 绘制主要柱状图（人脸相似度）
   * @param {Object} data - 数据项
   * @param {number} index - 索引
   * @param {Function} xCalculator - X坐标计算函数
   * @param {number} rectWidth - 矩形宽度
   * @param {number} altitudeHeight - 高度
   * @param {d3.Selection} g2 - SVG组选择器
   * @param {Array} legendCheckbox - 图例复选框状态
   */
  const drawMainBars = (data, index, xCalculator, rectWidth, altitudeHeight, g2, legendCheckbox) => {
    if (!legendCheckbox[0].isChecked) return;

    const x = xCalculator(data, index);
    const baseConfig = createStandardBarConfig(data, { x, rectWidth, altitudeHeight });

    // 绘制标准柱状图
    drawStandardBar(g2, {
      ...baseConfig,
      className: 'main-bar',
      foregroundFill: BAR_CHART_COLORS.GRADIENTS.GREEN,
      isInverted: false,
    });

    // 绘制分数文本
    drawBarScoreText(g2, {
      className: 'main-bar',
      x: x + rectWidth / 2,
      y: -(data.score * altitudeHeight) + 8,
      score: data.score,
      dataId: baseConfig.dataId,
    });

    // 绘制补充分数
    const shouldShow = data.scoreRank === 1 || data?.infoMatchFlag === 'EXACT_MATCH' || data.inGroupIndex === 0;
    drawCompensationScoreText(g2, {
      className: 'main-bar compensation-score',
      x: x + rectWidth / 2,
      y: -data.score * altitudeHeight - 24,
      score: data.score,
      shouldShow,
      dataId: baseConfig.dataId,
    });

    // 绘制算法标识
    drawAlgorithmBadge(g2, {
      x,
      width: rectWidth,
      algorithmText: algorithmShortNames[data?.algorithmCode],
      isExactMatch: data?.infoMatchFlag === 'EXACT_MATCH',
      shouldShowBorder: data.scoreRank === 1 || data.inGroupIndex === 0,
      dataId: baseConfig.dataId,
    });
  };

  /**
   * 绘制文本匹配度柱状图
   * @param {Object} data - 数据项
   * @param {number} index - 索引
   * @param {Function} xCalculator - X坐标计算函数
   * @param {number} rectWidth - 矩形宽度
   * @param {number} altitudeHeight - 高度
   * @param {d3.Selection} g2 - SVG组选择器
   * @param {Array} legendCheckbox - 图例复选框状态
   */
  const drawTextMatchingBars = (data, index, xCalculator, rectWidth, altitudeHeight, g2, legendCheckbox) => {
    if (!legendCheckbox[1].isChecked) return;

    const x = xCalculator(data, index);
    const infoScore = data?.infoMatch?.score || 0;
    const dataId = `${data.algorithmCode}-${data.portrait.portraitId}`;

    // 绘制倒置柱状图（文本匹配）
    drawStandardBar(g2, {
      className: 'stacked-bar',
      x,
      width: rectWidth,
      score: infoScore,
      altitudeHeight,
      foregroundFill: BAR_CHART_COLORS.GRADIENTS.BLUE,
      isInverted: true,
      dataId,
    });

    // 绘制分数文本
    drawBarScoreText(g2, {
      className: 'stacked-bar',
      x: x + rectWidth / 2,
      y: infoScore * altitudeHeight - 24,
      score: infoScore,
      dataId,
    });

    // 绘制补充分数
    const shouldShow = data.scoreRank === 1 || data?.infoMatchFlag === 'EXACT_MATCH' || data.inGroupIndex === 0;
    drawCompensationScoreText(g2, {
      className: 'stacked-bar compensation-score',
      x: x + rectWidth / 2,
      y: infoScore * altitudeHeight + 8,
      score: infoScore,
      shouldShow,
      dataId,
    });

    // 绘制文本匹配图标
    drawTextMatchingIcon(g2, data, x);
  };

  /**
   * 绘制文本匹配图标
   * @param {d3.Selection} g2 - SVG组选择器
   * @param {Object} data - 数据项
   * @param {number} x - X坐标
   */
  const drawTextMatchingIcon = (g2, data, x) => {
    let iconSrc = null;

    if (data?.infoMatchFlag === 'EXACT_MATCH') {
      iconSrc = textWeightedMatchingS;
    } else if (data.scoreRank === 1 || data.inGroupIndex === 0) {
      iconSrc = textWeightedMatching;
    }
    drawIcon(g2, {
      className: 'stacked-bar text-matching-img',
      x: x + 8,
      y: 9,
      iconSrc,
      dataId: `${data.algorithmCode}-${data.portrait.portraitId}`,
    });
  };

  /**
   * 绘制Top排名标签
   * @param {Object} data - 数据项
   * @param {number} index - 索引
   * @param {Function} xCalculator - X坐标计算函数
   * @param {d3.Selection} g2 - SVG组选择器
   * @param {number} altitudeHeight - 高度
   * @param {number} rectWidth - 矩形宽度
   */
  const drawTopLabel = (data, index, xCalculator, g2, altitudeHeight, rectWidth) => {
    const x = xCalculator(data, index);
    const text = data?.infoMatchFlag === 'EXACT_MATCH' ? data.xLabel : '';

    drawBarScoreText(g2, {
      className: 'text-label',
      x: x + rectWidth / 2,
      y: -altitudeHeight - 20,
      text,
      fill: BAR_CHART_COLORS.WHITE,
      dataId: `${data.algorithmCode}-${data.portrait.portraitId}`,
    });
  };

  /**
   * 绘制完全匹配图标
   * @param {Object} data - 数据项
   * @param {number} index - 索引
   * @param {Function} xCalculator - X坐标计算函数
   * @param {number} altitudeHeight - 高度
   * @param {d3.Selection} g2 - SVG组选择器
   */
  const drawPerfectMatchIcon = (data, index, xCalculator, altitudeHeight, g2) => {
    if (data?.infoMatchFlag === 'EXACT_MATCH') {
      const x = xCalculator(data, index);

      drawIcon(g2, {
        x: x + 8,
        y: altitudeHeight + 12,
        iconSrc: perfectMatch,
      });
    }
  };

  return {
    drawMainBars,
    drawTextMatchingBars,
    drawTopLabel,
    drawPerfectMatchIcon,
  };
}
