/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-09 15:21:15
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-11 15:28:42
 * @FilePath: \vue3-js-template-master\src\router\menus.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/router/menus.js
import routes from './modules/index'

const modules = import.meta.glob('./modules/*.js', { eager: true });

// 定义菜单顺序和权重
const menuConfig = {
  '/home': { order: 1, title: '首页' },
  '/topic': { order: 2, title: '工作专题' },
  '/library': { order: 3, title: '人脸比对' },
  '/video-comparison': { order: 4, title: '视频比对' },
  '/tools': { order: 5, title: '智能工具' },
  '/manage': { order: 6, title: '平台管理' },
  // 可以继续添加其他路由配置
};

const menus = routes
  .filter(route => route.meta && route.meta.menu)
  .map(route => {
    const config = menuConfig[route.path] || { order: 999 };
    return {
      key: route.path,
      title: route.meta.title,
      label: route.meta.title,
      icon: route.meta.icon || null,
      activeIcon: route.meta.activeIcon || null,
      menu: route.meta.menu,
      subMenus: route.children || [],
      order: config.order
    };
  })
  .sort((a, b) => a.order - b.order);
export default menus;
