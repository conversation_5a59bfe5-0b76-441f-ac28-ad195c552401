/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-10 17:01:17
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-11 11:42:11
 * @FilePath: \vue3-js-template-master\src\theme\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const theme = {
  // 左侧菜单
  sideMenuBg: '#000',
  sideMenuColor: '#fff',
  sideMenuActiveColor: '#4DBFFF',
  sideMenuSelectedBg: 'transparent',
  sideMenuSelectedColor: '#4DBFFF',

  // 顶部栏
  topBarBg: '#000',
  topBarColor: '#b6cfff',

  // 页面背景
  pageBg: '#16171C',
  topBarMenuBg: 'transparent',
  topBarMenuColor: '#8E96A0',
  topBarMenuActiveBg: 'transparent',
  topBarMenuActiveColor: '#4DBFFF',
  topBarMenuSelectedBg: 'transparent',
  topBarMenuSelectedColor: '#4DBFFF',
};

export default theme;