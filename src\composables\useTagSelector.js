import request from '@/utils/service';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

export function useTagSelector() {
    /**
     * 搜索标签
     * @param {string} query 搜索关键词
     * @returns {Promise<Array>} 标签列表
     */
    const handleSearchTags = async (query) => {
        try {
            const response = await request.get(`${BASE_URL}/api/users/tags`, {
                params: { name: query },
            });

            return (
                response.data?.map((item) => ({
                    id: item.id,
                    name: item.name,
                    description: item.description || '',
                })) || []
            );
        } catch (error) {
            console.error('搜索标签失败:', error);
            return [];
        }
    };

    /**
     * 创建新标签
     * @param {string} name 标签名称
     * @returns {Promise<Object>} 新创建的标签
     */
    const handleCreateTag = async (name) => {
        try {
            const response = await request.post(`${BASE_URL}/api/users/tags`, {
                name: name.trim(),
            });

            return {
                id: response.data.id,
                name: response.data.name,
                description: response.data.description || '',
            };
        } catch (error) {
            console.error('创建标签失败:', error);
            throw error;
        }
    };

    /**
     * 标签变化处理
     * @param {Array} tags 当前标签列表
     */
    const handleTagChange = (tags) => {
        console.log('标签已更新:', tags);
        // 可以在这里添加额外的业务逻辑
    };

    return {
        handleSearchTags,
        handleCreateTag,
        handleTagChange,
    };
}
