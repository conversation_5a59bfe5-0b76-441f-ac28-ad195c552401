/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-10 17:35:59
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-11 16:41:04
 * @FilePath: \frontend-template\src\router\modules\topic.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import lineIcon from '@/assets/svg/menus/line.svg'
import lineActiveIcon from '@/assets/svg/menus/line-active.svg'

export default [
  {
    path: '/topic',
    name: 'Topic',
    meta: { title: '工作专题', icon: lineIcon, activeIcon: lineActiveIcon, menu: true, keepAlive: false, layout: 'main' },
    redirect: '/topic/chat',
    children: [
      {
        path: 'chat',
        name: 'Chat',
        component: () => import('@/views/graph/Index.vue'),
        meta: { title: 'AI对话', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'struct-analysis',
        name: 'StructAnalysis',
        component: () => import('@/components/Error/403.vue'),
        meta: { title: '专题任务构析', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'collect',
        name: 'Collect',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '专题检索汇集', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'analysis',
        name: 'Analysis',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '专题智能分析', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'report',
        name: 'Report',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '专题智能报告', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'portrait',
        name: 'Portrait',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '专题人物画像', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'files',
        name: 'Files',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '专题人物档案', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
    ]
  },
];