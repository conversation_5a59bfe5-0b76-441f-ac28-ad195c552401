<!--/**
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-09 15:00:10
 * @FilePath: \vue3-js-template-master\src\layouts\components\TopBarLeft.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A
-->
<template>
  <div class="top-bar-logo">
    <img src="@/assets/svg/common/logo.svg" alt="">
    <router-link to="/">
      <SvgIcon icon-class="logo-text" width="251px" height="18px" />
    </router-link>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.top-bar-logo {
  font-size: 1.2rem;
  font-weight: bold;
  color: #6ed0ff;
  letter-spacing: 0.1em;
  display: flex;
  align-items: center;
  gap: 5px;
}
</style>