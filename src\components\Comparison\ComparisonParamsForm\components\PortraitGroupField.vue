<!--
/**
 * 人像组字段组件
 * 
 * @description 人像组下拉选择组件，包含人像组选择和人像库展示
 * <AUTHOR>
 * @version 1.0.0
 */
-->
<template>
  <a-dropdown trigger="click" placement="top" :get-popup-container="getPopupContainer"
    :overlay-style="{ padding: '12px', width: '400px' }">
    <template #overlay>
      <div class="select-library-dropdown">
        <div class="group-column">
          <span style="font-size: 14px;">人像组</span>
          <Divider />
          <div style="overflow-y: auto;height: 150px;overflow-x: hidden;" class="radio-div">
            <a-radio-group :value="modelValue" :options="portraitGroupOptionsCopy" @change="handleChange">
            </a-radio-group>
          </div>
        </div>
        <Divider direction="vertical" height="192px" type="dashed" />
        <div class="library-column">
          <span style="font-size: 14px;">包含人像库</span>
          <Divider />
          <div style="overflow-y: auto;height: 150px;overflow-x: hidden;">
            <div v-if="hoveredGroup?.libraries?.length" class="library-list">
              <div v-for="lib in hoveredGroup.libraries" :key="lib.id" class="library-item">
                <span>{{ lib.name }}</span>
              </div>
            </div>
            <div v-else class="empty-libraries">
              <span>暂无人像库</span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="d-flex align-items-center justify-content-between w-100 h-100 portrait-text" :style="buttonStyle">
      <span>{{ selectedGroupNames.join(', ') || '请选择人像组' }}</span>
      <SvgIcon icon-class="down-arrow-high" size="10px"></SvgIcon>
    </div>
  </a-dropdown>
</template>

<script setup>
import { computed } from 'vue';
import Divider from '@/components/Common/Divider/index.vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  portraitGroupOptions: {
    type: Array,
    default: () => []
  },
  getPopupContainer: {
    type: Function,
    default: () => document.body
  },
  buttonBg: {
    type: String,
    default: '#293040'
  },
  fontSize: {
    type: String,
    default: '16px'
  },

});

const emits = defineEmits(['update:modelValue']);

// 人像组选项处理
const portraitGroupOptionsCopy = computed(() => {
  return props.portraitGroupOptions.map(group => ({
    ...group,
    label: group.name,
    value: group.id
  }));
});

// 当前选中的人像组
const hoveredGroup = computed(() => {
  return props.portraitGroupOptions.find(group => group.id === props.modelValue);
});

// 选中的人像组名称
const selectedGroupNames = computed(() => {
  return props.portraitGroupOptions
    .filter(g => props.modelValue === g.id)
    .map(g => g.name);
});

// 处理值变更
const handleChange = (e) => {
  // 对于 a-radio-group，e.target.value 是选中的值
  const value = e.target.value;
  emits('update:modelValue', value);
};

const buttonStyle = computed(() => {
  return {
    background: props.buttonBg,
    fontSize: props.fontSize
  }
})
</script>

<style scoped lang="scss">
@use '../styles/form-styles.scss';
</style>
