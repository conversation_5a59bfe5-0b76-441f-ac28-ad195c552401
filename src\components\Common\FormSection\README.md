# FormSection 表单区域组件

## 📖 组件概述

FormSection 是一个通用的表单区域组件，用于创建带有图标和标题的表单分组区域。该组件提供了统一的视觉样式和布局结构，适用于复杂表单的模块化组织。

## ✨ 主要功能

- 🎯 **统一布局**：提供标准化的表单区域布局结构
- 🎨 **图标支持**：支持自定义图标和图标尺寸
- 📝 **标题显示**：清晰的区域标题展示
- 🔧 **插槽机制**：通过默认插槽自定义内容区域
- 🎪 **样式统一**：统一的边框、圆角、内边距样式
- 📱 **响应式**：完全支持响应式布局

## 🏗️ 组件结构

```
src/components/Common/FormSection/
├── index.vue                    # 主组件 (65行)
└── README.md                    # 组件文档
```

## 📋 Props 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| title | String | - | ✅ | 区域标题文本 |
| iconClass | String | - | ✅ | 图标类名，用于SvgIcon组件 |
| iconSize | String | '18px' | ❌ | 图标尺寸，支持任何CSS尺寸单位 |

### Props 详细说明

#### title
- **类型**: `String`
- **必填**: 是
- **说明**: 表单区域的标题文本，会显示在图标右侧

#### iconClass
- **类型**: `String`
- **必填**: 是
- **说明**: 传递给SvgIcon组件的图标类名，需要确保对应的SVG图标已注册

#### iconSize
- **类型**: `String`
- **默认值**: `'18px'`
- **说明**: 图标的显示尺寸，支持px、em、rem等CSS单位

## 🎨 插槽 Slots

### default 默认插槽

用于放置表单区域的具体内容，如表单字段、其他组件等。

```vue
<FormSection title="身份信息" icon-class="identity-info-icon">
  <!-- 这里是默认插槽内容 -->
  <a-form-item label="姓名">
    <a-input />
  </a-form-item>
  <a-form-item label="年龄">
    <a-input-number />
  </a-form-item>
</FormSection>
```

## 🚀 使用方式

### 1. 基础用法

```vue
<template>
  <FormSection title="基本信息" icon-class="info-icon">
    <a-form-item label="姓名">
      <a-input v-model:value="form.name" placeholder="请输入姓名" />
    </a-form-item>
    <a-form-item label="邮箱">
      <a-input v-model:value="form.email" placeholder="请输入邮箱" />
    </a-form-item>
  </FormSection>
</template>

<script setup>
import { ref } from 'vue';
import FormSection from '@/components/Common/FormSection/index.vue';

const form = ref({
  name: '',
  email: ''
});
</script>
```

### 2. 自定义图标尺寸

```vue
<template>
  <FormSection 
    title="重要信息" 
    icon-class="warning-icon" 
    icon-size="24px">
    <a-form-item label="重要备注">
      <a-textarea v-model:value="form.note" />
    </a-form-item>
  </FormSection>
</template>
```

### 3. 多个区域组合

```vue
<template>
  <div class="form-container">
    <!-- 身份信息区域 -->
    <FormSection title="身份信息" icon-class="identity-info-icon">
      <a-form-item label="姓名">
        <a-input v-model:value="form.name" />
      </a-form-item>
      <a-form-item label="性别">
        <a-radio-group v-model:value="form.gender">
          <a-radio value="male">男</a-radio>
          <a-radio value="female">女</a-radio>
        </a-radio-group>
      </a-form-item>
    </FormSection>

    <!-- 联系信息区域 -->
    <FormSection title="联系信息" icon-class="contact-icon">
      <a-form-item label="电话">
        <a-input v-model:value="form.phone" />
      </a-form-item>
      <a-form-item label="地址">
        <a-input v-model:value="form.address" />
      </a-form-item>
    </FormSection>

    <!-- 其他信息区域 -->
    <FormSection title="其他" icon-class="other-icon" icon-size="15px">
      <a-form-item label="备注">
        <a-textarea v-model:value="form.remark" />
      </a-form-item>
    </FormSection>
  </div>
</template>
```

## 🔧 与其他组件配合

### 配合FormArrayFieldGroup使用

```vue
<template>
  <FormSection title="身份信息" icon-class="identity-info-icon">
    <FormArrayFieldGroup 
      label="曾用名" 
      v-model="formData.usedNames" 
      field-key="oldName">
      <template #default="{ item, index, updateItem }">
        <a-input 
          :value="item" 
          @update:value="updateItem(index, $event)" 
          placeholder="请填写" />
      </template>
    </FormArrayFieldGroup>
  </FormSection>
</template>
```

### 在复杂表单中使用

```vue
<template>
  <a-form :model="formData" :colon="false">
    <div class="w-100 h-100 d-flex flex-column g-2">
      <!-- 身份信息模块 -->
      <FormSection title="身份信息" icon-class="identity-info-icon" icon-size="18px">
        <IdentityFields :form-data="formData" />
      </FormSection>

      <!-- 社会行为模块 -->
      <FormSection title="社会行为" icon-class="social-behaviour-icon" icon-size="15px">
        <SocialBehaviorFields :form-data="formData" />
      </FormSection>

      <!-- 其他信息模块 -->
      <FormSection title="其他" icon-class="other-icon" icon-size="15px">
        <OtherFields :form-data="formData" />
      </FormSection>
    </div>
  </a-form>
</template>
```

## 🎨 样式说明

### 默认样式

组件提供了以下默认样式：

- **外层容器**: `margin-bottom: 16px` - 区域间距
- **内容区域**: 
  - `border: 1px solid #3c4554` - 深色边框
  - `border-radius: 8px` - 圆角边框
  - `padding: 16px` - 内边距
  - `margin-top: 8px` - 标题与内容间距

### 标题区域样式

- 使用Flexbox布局：`d-flex align-items-center g-1`
- 图标与标题水平排列，垂直居中
- 图标与标题间距为8px（通过g-1类实现）

### 样式定制

如需自定义样式，可以通过CSS覆盖：

```scss
.form-section-wrapper {
  // 自定义外层样式
  margin-bottom: 24px;
  
  .form-section {
    // 自定义内容区域样式
    border: 2px solid #4dbfff;
    border-radius: 12px;
    padding: 20px;
    background: rgba(77, 191, 255, 0.05);
  }
}
```

## 📱 响应式支持

组件完全支持响应式布局，内容区域会根据父容器自动调整：

```vue
<template>
  <div class="responsive-container">
    <FormSection title="响应式区域" icon-class="responsive-icon">
      <div class="responsive-content">
        <!-- 响应式内容 -->
      </div>
    </FormSection>
  </div>
</template>

<style scoped>
.responsive-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.responsive-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}
</style>
```

## ⚠️ 注意事项

1. **图标依赖**：确保传入的iconClass对应的SVG图标已在项目中注册
2. **标题必填**：title属性是必填的，用于区域标识
3. **嵌套使用**：避免FormSection组件的嵌套使用，保持扁平的结构
4. **样式继承**：内容区域会继承父级的字体和颜色样式
5. **间距控制**：组件间距通过margin-bottom控制，注意与其他组件的间距协调

## 🚀 扩展建议

1. **可折叠功能**：添加展开/收起功能，适用于长表单
2. **状态指示**：添加验证状态指示（成功、错误、警告）
3. **帮助信息**：支持显示帮助文本或提示信息
4. **主题支持**：支持多种主题样式切换
5. **动画效果**：为展开收起添加过渡动画

## 📝 更新日志

### v1.0.0 (2025-07-23)
- 初始版本发布
- 支持基础的图标、标题、内容区域布局
- 提供统一的样式规范
- 支持自定义图标尺寸
