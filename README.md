# 前端开发规范文档

## 目录
1. [项目概述](#项目概述)
2. [Git 工作流规范](#git-工作流规范)
3. [代码规范](#代码规范)
4. [项目结构规范](#项目结构规范)
5. [视图模块子页面结构规范](#视图模块子页面结构规范)
6. [跨模块组件复用规范](#跨模块组件复用规范)
7. [组件开发规范](#组件开发规范)
8. [API 接口规范](#api-接口规范)
9. [状态管理规范](#状态管理规范)
10. [样式规范](#样式规范)
11. [测试规范](#测试规范)
12. [构建与部署规范](#构建与部署规范)
13. [性能优化规范](#性能优化规范)
14. [安全规范](#安全规范)
15. [大型项目后期规范问题](#大型项目后期规范问题)
16. [状态管理优化方案](#状态管理优化方案)
17. [布局组件规范](#布局组件规范)
18. [组件封装思想规范](#组件封装思想规范)
19. [文件代码行数约定](#文件代码行数约定)

## 项目概述

### 技术栈
- **前端框架**: Vue 3.2.47
- **构建工具**: Vite 4.0.0
- **UI 组件库**: Ant Design Vue 4.1.0
- **状态管理**: Vuex 4.0.2
- **路由管理**: Vue Router 4.0.12
- **样式预处理器**: Less 4.1.3
- **代码规范**: ESLint + Prettier
- **Git 钩子**: Husky + lint-staged

## Git 工作流规范

### 分支管理策略

#### 分支命名规范
```
主分支: main/master
开发分支: develop
功能分支: feature/功能名称
修复分支: hotfix/问题描述
发布分支: release/版本号
```

#### 分支创建规范
```bash
# 创建功能分支
git checkout -b feature/user-authentication

# 创建修复分支
git checkout -b hotfix/login-bug-fix

# 创建发布分支
git checkout -b release/v1.2.0
```

### 提交信息规范

#### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```bash
# 功能提交
git commit -m "feat(auth): 添加用户登录功能

- 实现用户名密码登录
- 添加JWT token验证
- 集成第三方登录

Closes #123"

# 修复提交
git commit -m "fix(upload): 修复文件上传进度显示问题

修复上传进度条不更新的问题，确保进度实时显示

Fixes #456"

# 文档提交
git commit -m "docs(readme): 更新项目安装说明

添加详细的开发环境配置步骤"
```

### 代码审查规范

#### 提交前检查
```bash
# 运行代码检查
npm run lint

# 运行类型检查（如果使用TypeScript）
npm run type-check

# 运行测试
npm run test
```

#### Pull Request 规范
1. **标题格式**: `[类型] 简短描述`
2. **描述内容**:
   - 功能描述
   - 修改内容
   - 测试情况
   - 相关Issue
3. **标签使用**: 使用适当的标签分类

## 代码规范

### 命名规范详解

#### 1. 变量命名规范

##### 基本变量命名
```javascript
// ✅ 正确 - 使用 camelCase
const userName = 'admin';
const userInfo = { name: 'John', age: 30 };
const isAuthenticated = true;
const hasPermission = false;

// ❌ 错误 - 使用下划线或连字符
const user_name = 'admin';
const user-info = { name: 'John', age: 30 };
const is_authenticated = true;
```

##### 布尔值变量命名
```javascript
// ✅ 正确 - 使用 is/has/can/should 前缀
const isLoading = false;
const hasPermission = true;
const canEdit = true;
const shouldRefresh = false;
const isVisible = true;
const hasError = false;

// ❌ 错误 - 不使用前缀或使用不明确的前缀
const loading = false;
const permission = true;
const edit = true;
```

##### 数组变量命名
```javascript
// ✅ 正确 - 使用复数形式
const users = [];
const fileList = [];
const menuItems = [];
const errorMessages = [];

// ❌ 错误 - 使用单数形式
const user = [];
const file = [];
const menuItem = [];
```

##### 对象变量命名
```javascript
// ✅ 正确 - 使用描述性名称
const userProfile = { name: 'John', age: 30 };
const uploadConfig = { maxSize: 1024, allowedTypes: ['jpg', 'png'] };
const apiResponse = { data: [], total: 0 };

// ❌ 错误 - 使用不明确的名称
const data = { name: 'John', age: 30 };
const config = { maxSize: 1024, allowedTypes: ['jpg', 'png'] };
```

#### 2. 常量命名规范

##### 全局常量
```javascript
// ✅ 正确 - 使用 UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_FILE_SIZE = 10 * 1024 * 1024;
const DEFAULT_TIMEOUT = 5000;
const SUPPORTED_FORMATS = ['jpg', 'png', 'gif'];

// ❌ 错误 - 使用其他命名方式
const apiBaseUrl = 'https://api.example.com';
const maxFileSize = 10 * 1024 * 1024;
const defaultTimeout = 5000;
```

##### 模块级常量
```javascript
// ✅ 正确 - 使用 UPPER_SNAKE_CASE
const UPLOAD_STATUS = {
  PENDING: 'pending',
  UPLOADING: 'uploading',
  COMPLETED: 'completed',
  FAILED: 'failed'
};

const FILE_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  DOCUMENT: 'document'
};

// ❌ 错误 - 使用小写
const uploadStatus = {
  pending: 'pending',
  uploading: 'uploading',
  completed: 'completed',
  failed: 'failed'
};
```

##### 枚举常量
```javascript
// ✅ 正确 - 使用对象形式定义枚举
const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest'
} as const;

const PERMISSION_LEVELS = {
  READ: 1,
  WRITE: 2,
  DELETE: 3,
  ADMIN: 4
} as const;

// 使用示例
const userRole = USER_ROLES.ADMIN;
const permissionLevel = PERMISSION_LEVELS.WRITE;
```

#### 3. 函数命名规范

##### 动词前缀规范
```javascript
// ✅ 正确 - 使用动词前缀
const getUserInfo = () => {};
const updateUserProfile = () => {};
const deleteUser = () => {};
const validateForm = () => {};
const handleSubmit = () => {};
const fetchData = () => {};
const uploadFile = () => {};

// ❌ 错误 - 不使用动词前缀
const userInfo = () => {};
const userProfile = () => {};
const user = () => {};
const form = () => {};
```

##### 异步函数命名
```javascript
// ✅ 正确 - 使用 async/await 或 Promise 相关命名
const fetchUserData = async () => {};
const uploadFileAsync = async () => {};
const getUserInfoPromise = () => {};

// ❌ 错误 - 不使用异步相关命名
const getUserData = async () => {};
const uploadFile = async () => {};
```

#### 4. 类命名规范

```javascript
// ✅ 正确 - 使用 PascalCase
class UserService {
  constructor() {}
  
  getUserInfo() {}
  
  updateUser() {}
}

class FileUploader {
  constructor() {}
  
  upload() {}
  
  validate() {}
}

// ❌ 错误 - 使用 camelCase 或其他命名
class userService {
  constructor() {}
}

class file_uploader {
  constructor() {}
}
```

### 常量文件组织规范

#### 1. 常量文件结构

##### 基础常量文件
```javascript
// constants/APP_MENUS.js
import icon1n from '@/assets/menu-icons/menu-1n.svg';
import icon1a from '@/assets/menu-icons/menu-1a.svg';

const APP_MENUS = [
  {
    name: '首页',
    extendName: '',
    icon: icon1n,
    activeIcon: icon1a,
    showDivider: false,
    path: '/home',
    children: [
      {
        name: '首页',
        path: '/home',
      },
      {
        name: '平台总览',
        path: '/data-screen',
      },
    ],
  },
  // ... 其他菜单项
];

export default APP_MENUS;
```

##### 配置常量文件
```javascript
// constants/DEFAULT_CONFIG.js
const DEFAULT_CONFIG = {
  // 应用配置
  enableMenuAccessControl: true,
  isAuthRequired: true,
  whitePages: ['/user/login', '/not-found', '/unauthorized'],

  // API 配置
  uploadVideoAPI: 'http://**************:6792/api/v2/videos',
  uploadVideoHeaders: { 
    bucketId: '39ba7d84-15c0-4aeb-9716-711a5454c70c' 
  },
  uploadImageAPI: 'http://**************:6790/api/v2/images/upload',
  uploadImageHeaders: { 
    bucketId: '8b8c1248-7c68-4968-bf36-ca5986075177' 
  },

  // 工具函数
  getVideoWebUrl: (videoId) => {
    return `http://**************:6792/api/v2/videos/download/${videoId}?bucketId=${DEFAULT_CONFIG.uploadVideoHeaders.bucketId}`;
  },
  getVideoThumbnailUrl: (thumbnailImageId) => {
    return `http://**************:9005/processed-videos${thumbnailImageId}`;
  },
  getImageWebURL: (imageId) => {
    return `http://**************:6790/api/v2/images/download/${imageId}?bucketId=${DEFAULT_CONFIG.uploadImageHeaders.bucketId}`;
  }
};

export default DEFAULT_CONFIG;
```

##### 颜色常量文件
```javascript
// constants/COLORS.js
const COLORS = {
  // 主色调
  primary: '#00aaff',
  primaryLight: '#33bbff',
  primaryDark: '#0088cc',
  
  // 辅助色
  secondary: '#16171c',
  secondaryLight: '#2a2d33',
  secondaryDark: '#0f1014',
  
  // 功能色
  success: '#52c41a',
  warning: '#faad14',
  error: '#f5222d',
  info: '#1890ff',
  
  // 中性色
  textPrimary: '#262626',
  textSecondary: '#8c8c8c',
  textDisabled: '#bfbfbf',
  
  borderBase: '#d9d9d9',
  borderSplit: '#f0f0f0',
  
  backgroundBase: '#f5f5f5',
  backgroundLight: '#fafafa',
  
  // 状态色
  successBg: '#f6ffed',
  warningBg: '#fffbe6',
  errorBg: '#fff2f0',
  infoBg: '#e6f7ff'
};

export default COLORS;
```

##### 错误码常量文件
```javascript
// constants/ERROR_CODE.js
const ERROR_CODE = {
  // 认证相关错误
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  TOKEN_EXPIRED: 40101,
  TOKEN_INVALID: 40102,
  
  // 请求相关错误
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  REQUEST_TIMEOUT: 408,
  
  // 服务器错误
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
  
  // 业务错误
  USER_NOT_FOUND: 10001,
  FILE_TOO_LARGE: 10002,
  INVALID_FILE_TYPE: 10003,
  UPLOAD_FAILED: 10004,
  
  // 网络错误
  NETWORK_ERROR: 20001,
  CONNECTION_TIMEOUT: 20002
};

export default ERROR_CODE;
```

#### 2. 常量文件统一导出

```javascript
// constants/index.js
import DEFAULT_CONFIG from './DEFAULT_CONFIG';
import ERROR_CODE from './ERROR_CODE';
import APP_MENUS from './APP_MENUS';
import COLORS from './COLORS';

// 统一导出所有常量
export {
  DEFAULT_CONFIG,
  ERROR_CODE,
  APP_MENUS,
  COLORS
};

// 默认导出配置对象
export default {
  DEFAULT_CONFIG,
  ERROR_CODE,
  APP_MENUS,
  COLORS
};
```

### 文件夹命名规范

> **注意**: 关于视图模块下子模块页面跳转的详细规范，请参考 [视图模块子页面结构规范](#视图模块子页面结构规范) 章节。

#### 1. 目录结构命名

##### 基础目录
```
src/
├── api/                    # API 接口定义 (小写)
├── assets/                 # 静态资源 (小写)
├── components/             # 公共组件 (小写)
├── composables/            # 组合式函数 (小写)
├── constants/              # 常量定义 (小写)
├── layouts/                # 布局组件 (小写)
├── router/                 # 路由配置 (小写)
├── store/                  # 状态管理 (小写)
├── styles/                 # 全局样式 (小写)
├── utils/                  # 工具函数 (小写)
├── views/                  # 页面组件 (小写)
├── websocket/              # WebSocket 相关 (小写)
└── plugins/                # 插件文件 (小写)
```

##### 功能模块目录
```
src/views/
├── home/                   # 首页模块 (小写)
├── auth/                   # 认证模块 (小写)
├── dashboard/              # 仪表板模块 (小写)
├── upload/                 # 上传模块 (小写)
├── library/                # 库管理模块 (小写)
├── comparison/             # 比对模块 (小写)
├── video-analyzing/        # 视频分析模块 (kebab-case)
├── video-archiving/        # 视频归档模块 (kebab-case)
├── chatting-page/          # 聊天页面模块 (kebab-case)
├── third-party-info/       # 第三方信息模块 (kebab-case)
├── data-screen/            # 数据大屏模块 (kebab-case)
├── picture-edit/           # 图片编辑模块 (kebab-case)
└── platform-management/    # 平台管理模块 (kebab-case)
```

##### 组件目录
```
src/components/
├── Common/                 # 通用组件 (PascalCase)
│   ├── Button.vue
│   ├── Modal.vue
│   └── Loading.vue
├── Business/               # 业务组件 (PascalCase)
│   ├── UserCard.vue
│   ├── FileUploader.vue
│   ├── VideoPlayer.vue
│   ├── ImageViewer.vue
│   ├── SearchForm.vue
│   └── StatusTag.vue
├── Domain/                 # 领域组件 (特定领域复用)
│   ├── Video/             # 视频相关组件
│   │   ├── VideoList.vue
│   │   ├── VideoPlayer.vue
│   │   ├── VideoUploader.vue
│   │   └── VideoThumbnail.vue
│   ├── Image/             # 图片相关组件
│   │   ├── ImageList.vue
│   │   ├── ImageViewer.vue
│   │   ├── ImageUploader.vue
│   │   └── ImageCropper.vue
│   └── User/              # 用户相关组件
│       ├── UserList.vue
│       ├── UserCard.vue
│       ├── UserForm.vue
│       └── UserAvatar.vue
└── Module/                 # 模块专用组件 (单模块使用)
    ├── VideoAnalyzing/    # 视频分析模块专用
    │   ├── AnalysisPanel.vue
    │   ├── ResultDisplay.vue
    │   └── TimelineView.vue
    ├── PlatformManagement/ # 平台管理模块专用
    │   ├── RoleSelector.vue
    │   ├── PermissionTree.vue
    │   └── SystemConfig.vue
    └── Upload/            # 上传模块专用
        ├── UploadProgress.vue
        ├── FilePreview.vue
        └── BatchUploader.vue
```

#### 3. 视图模块子页面结构规范

##### 子模块页面组织
```
src/views/
├── video-analyzing/        # 视频分析模块
│   ├── index.vue          # 模块主页面
│   ├── components/        # 模块专用组件
│   │   ├── VideoList.vue
│   │   ├── AnalysisPanel.vue
│   │   └── ResultDisplay.vue
│   ├── pages/            # 子页面
│   │   ├── list/         # 列表页面
│   │   │   ├── index.vue
│   │   │   ├── components/
│   │   │   └── composables/
│   │   ├── detail/       # 详情页面
│   │   │   ├── index.vue
│   │   │   ├── components/
│   │   │   └── composables/
│   │   ├── create/       # 创建页面
│   │   │   ├── index.vue
│   │   │   ├── components/
│   │   │   └── composables/
│   │   └── edit/         # 编辑页面
│   │       ├── index.vue
│   │       ├── components/
│   │       └── composables/
│   ├── composables/      # 模块专用组合式函数
│   │   ├── useVideoAnalysis.js
│   │   └── useVideoUpload.js
│   ├── utils/           # 模块专用工具函数
│   │   ├── videoProcessor.js
│   │   └── analysisHelper.js
│   └── styles/          # 模块专用样式
│       └── video-analyzing.less
├── platform-management/   # 平台管理模块
│   ├── index.vue
│   ├── components/
│   ├── pages/
│   │   ├── user-management/    # 用户管理
│   │   │   ├── index.vue
│   │   │   ├── list.vue
│   │   │   ├── create.vue
│   │   │   └── edit.vue
│   │   ├── role-management/    # 角色管理
│   │   │   ├── index.vue
│   │   │   ├── list.vue
│   │   │   ├── create.vue
│   │   │   └── edit.vue
│   │   └── system-config/      # 系统配置
│   │       ├── index.vue
│   │       ├── general.vue
│   │       ├── security.vue
│   │       └── notification.vue
│   ├── composables/
│   ├── utils/
│   └── styles/
└── upload/               # 上传模块
    ├── index.vue
    ├── components/
    ├── pages/
    │   ├── file-upload/      # 文件上传
    │   │   ├── index.vue
    │   │   ├── components/
    │   │   └── composables/
    │   ├── batch-upload/     # 批量上传
    │   │   ├── index.vue
    │   │   ├── components/
    │   │   └── composables/
    │   └── upload-history/   # 上传历史
    │       ├── index.vue
    │       ├── components/
    │       └── composables/
    ├── composables/
    ├── utils/
    └── styles/
```

##### 子页面路由配置规范

###### 路由文件结构
```javascript
// router/modules/video-analyzing.js
import { createRouter } from 'vue-router';

const videoAnalyzingRoutes = {
  path: '/video-analyzing',
  name: 'VideoAnalyzing',
  component: () => import('@/views/video-analyzing/index.vue'),
  meta: {
    title: '视频分析',
    requiresAuth: true,
    permissions: ['video:analyze']
  },
  children: [
    {
      path: '',
      name: 'VideoAnalyzingIndex',
      redirect: '/video-analyzing/list'
    },
    {
      path: 'list',
      name: 'VideoAnalyzingList',
      component: () => import('@/views/video-analyzing/pages/list/index.vue'),
      meta: {
        title: '视频列表',
        breadcrumb: ['视频分析', '视频列表']
      }
    },
    {
      path: 'detail/:id',
      name: 'VideoAnalyzingDetail',
      component: () => import('@/views/video-analyzing/pages/detail/index.vue'),
      meta: {
        title: '视频详情',
        breadcrumb: ['视频分析', '视频列表', '视频详情']
      }
    },
    {
      path: 'create',
      name: 'VideoAnalyzingCreate',
      component: () => import('@/views/video-analyzing/pages/create/index.vue'),
      meta: {
        title: '创建分析',
        breadcrumb: ['视频分析', '创建分析']
      }
    },
    {
      path: 'edit/:id',
      name: 'VideoAnalyzingEdit',
      component: () => import('@/views/video-analyzing/pages/edit/index.vue'),
      meta: {
        title: '编辑分析',
        breadcrumb: ['视频分析', '编辑分析']
      }
    }
  ]
};

export default videoAnalyzingRoutes;
```

###### 页面跳转规范

```javascript
// ✅ 正确 - 使用命名路由进行页面跳转
import { useRouter } from 'vue-router';

const router = useRouter();

// 跳转到列表页面
const goToList = () => {
  router.push({ name: 'VideoAnalyzingList' });
};

// 跳转到详情页面
const goToDetail = (id) => {
  router.push({ 
    name: 'VideoAnalyzingDetail', 
    params: { id } 
  });
};

// 跳转到创建页面
const goToCreate = () => {
  router.push({ name: 'VideoAnalyzingCreate' });
};

// 跳转到编辑页面
const goToEdit = (id) => {
  router.push({ 
    name: 'VideoAnalyzingEdit', 
    params: { id } 
  });
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// ❌ 错误 - 避免使用路径字符串跳转
const goToList = () => {
  router.push('/video-analyzing/list');
};

const goToDetail = (id) => {
  router.push(`/video-analyzing/detail/${id}`);
};
```

##### 子页面组件结构规范

###### 主页面组件 (index.vue)
```vue
<template>
  <div class="video-analyzing">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>{{ pageTitle }}</h1>
      <div class="header-actions">
        <a-button @click="handleCreate">新建分析</a-button>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 根据当前路由计算页面标题
const pageTitle = computed(() => {
  const routeMap = {
    'VideoAnalyzingList': '视频列表',
    'VideoAnalyzingDetail': '视频详情',
    'VideoAnalyzingCreate': '创建分析',
    'VideoAnalyzingEdit': '编辑分析'
  };
  return routeMap[route.name] || '视频分析';
});

// 页面操作
const handleCreate = () => {
  router.push({ name: 'VideoAnalyzingCreate' });
};
</script>

<style lang="less" scoped>
.video-analyzing {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .page-content {
    background: #fff;
    padding: 24px;
    border-radius: 6px;
  }
}
</style>
```

###### 子页面组件 (pages/list/index.vue)
```vue
<template>
  <div class="video-list">
    <!-- 搜索区域 -->
    <div class="search-section">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="视频名称">
          <a-input v-model="searchForm.name" placeholder="请输入视频名称" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #action="{ record }">
          <a-space>
            <a @click="handleView(record)">查看</a>
            <a @click="handleEdit(record)">编辑</a>
            <a-popconfirm
              title="确定删除吗？"
              @confirm="handleDelete(record)"
            >
              <a>删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useVideoAnalysis } from '@/views/video-analyzing/composables/useVideoAnalysis';

const router = useRouter();
const { fetchVideoList, deleteVideo } = useVideoAnalysis();

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const searchForm = reactive({
  name: '',
  status: undefined
});

// 表格配置
const columns = [
  { title: '视频名称', dataIndex: 'name', key: 'name' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '操作', key: 'action', slots: { customRender: 'action' } }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
});

// 页面操作
const handleSearch = async () => {
  await loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: undefined
  });
  loadData();
};

const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
};

const handleView = (record) => {
  router.push({ 
    name: 'VideoAnalyzingDetail', 
    params: { id: record.id } 
  });
};

const handleEdit = (record) => {
  router.push({ 
    name: 'VideoAnalyzingEdit', 
    params: { id: record.id } 
  });
};

const handleDelete = async (record) => {
  try {
    await deleteVideo(record.id);
    await loadData();
  } catch (error) {
    console.error('删除失败:', error);
  }
};

// 数据加载
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      pageSize: pagination.pageSize
    };
    const result = await fetchVideoList(params);
    tableData.value = result.data;
    pagination.total = result.total;
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.video-list {
  .search-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .table-section {
    background: #fff;
    padding: 16px;
    border-radius: 6px;
  }
}
</style>
```

##### 子页面状态管理规范

###### 模块级状态管理
```javascript
// store/modules/videoAnalyzing.js
const state = {
  videoList: [],
  currentVideo: null,
  loading: false,
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  searchForm: {
    name: '',
    status: undefined
  }
};

const mutations = {
  SET_VIDEO_LIST(state, list) {
    state.videoList = list;
  },
  SET_CURRENT_VIDEO(state, video) {
    state.currentVideo = video;
  },
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  SET_PAGINATION(state, pagination) {
    state.pagination = { ...state.pagination, ...pagination };
  },
  SET_SEARCH_FORM(state, form) {
    state.searchForm = { ...state.searchForm, ...form };
  }
};

const actions = {
  async fetchVideoList({ commit }, params) {
    commit('SET_LOADING', true);
    try {
      const response = await api.getVideoList(params);
      commit('SET_VIDEO_LIST', response.data);
      commit('SET_PAGINATION', {
        total: response.total,
        current: params.page,
        pageSize: params.pageSize
      });
      return response;
    } finally {
      commit('SET_LOADING', false);
    }
  },

  async fetchVideoDetail({ commit }, id) {
    commit('SET_LOADING', true);
    try {
      const response = await api.getVideoDetail(id);
      commit('SET_CURRENT_VIDEO', response.data);
      return response;
    } finally {
      commit('SET_LOADING', false);
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
```

##### 子页面组合式函数规范

###### 模块专用组合式函数
```javascript
// views/video-analyzing/composables/useVideoAnalysis.js
import { ref, reactive } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

export const useVideoAnalysis = () => {
  const store = useStore();
  const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const searchForm = reactive({
    name: '',
    status: undefined
  });

  // 获取视频列表
  const fetchVideoList = async (params = {}) => {
    loading.value = true;
    try {
      const result = await store.dispatch('videoAnalyzing/fetchVideoList', {
        ...searchForm,
        ...params
      });
      return result;
    } catch (error) {
      message.error('获取视频列表失败');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取视频详情
  const fetchVideoDetail = async (id) => {
    loading.value = true;
    try {
      const result = await store.dispatch('videoAnalyzing/fetchVideoDetail', id);
      return result;
    } catch (error) {
      message.error('获取视频详情失败');
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 页面跳转
  const goToList = () => {
    router.push({ name: 'VideoAnalyzingList' });
  };

  const goToDetail = (id) => {
    router.push({ 
      name: 'VideoAnalyzingDetail', 
      params: { id } 
    });
  };

  const goToCreate = () => {
    router.push({ name: 'VideoAnalyzingCreate' });
  };

  const goToEdit = (id) => {
    router.push({ 
      name: 'VideoAnalyzingEdit', 
      params: { id } 
    });
  };

  return {
    loading,
    searchForm,
    fetchVideoList,
    fetchVideoDetail,
    goToList,
    goToDetail,
    goToCreate,
    goToEdit
  };
};
```

##### 子页面样式规范

###### 模块级样式文件
```less
// views/video-analyzing/styles/video-analyzing.less
.video-analyzing {
  // 页面布局
  .page-container {
    padding: 24px;
    background: #f0f2f5;
    min-height: 100vh;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-content {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  }

  // 搜索区域
  .search-section {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;

    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  // 表格区域
  .table-section {
    padding: 16px 24px;

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }
    }
  }

  // 详情页面
  .detail-section {
    padding: 24px;

    .detail-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .detail-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
    }

    .detail-content {
      .detail-item {
        display: flex;
        margin-bottom: 16px;

        .item-label {
          width: 120px;
          color: #8c8c8c;
          font-weight: 500;
        }

        .item-value {
          flex: 1;
          color: #262626;
        }
      }
    }
  }

  // 表单页面
  .form-section {
    padding: 24px;

    .form-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .form-title {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
      }
    }

    .form-content {
      max-width: 600px;

      .ant-form-item {
        margin-bottom: 24px;
      }
    }

    .form-actions {
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
      text-align: center;

      .ant-btn {
        margin: 0 8px;
      }
    }
  }
}
```

##### 状态管理分层与结构优化建议

1. **全局状态（Global State）**
   - 只存放全局唯一且高频访问的数据，如用户信息、权限、主题、全局配置等。
   - 目录建议：`src/store/global/` 或 `src/store/modules/global.js`
   - 必须开启命名空间（如 Vuex 的 `namespaced: true`），防止命名冲突。

2. **业务模块状态（Module State）**
   - 每个业务模块有独立的状态文件，只管理本模块的数据和逻辑。
   - 目录建议：`src/store/modules/业务模块名/`，如 `src/store/modules/videoAnalyzing/`
   - 只暴露必要的 getter/action，避免全局污染。

3. **组合式函数/局部状态（Composables/Local State）**
   - 页面或组件级的临时状态，优先用组合式函数（如 useXxx）或组件自身的 reactive/ref。
   - 目录建议：`src/composables/` 或 `src/views/xxx/composables/`
   - 只在当前页面/组件内使用，不入全局 store。

4. **状态管理结构示例**

```
src/
├── store/
│   ├── index.js           # 入口，统一注册所有模块
│   ├── global/            # 全局状态
│   │   └── user.js
│   └── modules/           # 业务模块状态
│       ├── videoAnalyzing/
│       │   └── index.js
│       ├── upload/
│       │   └── index.js
│       └── ...
├── composables/
│   ├── useUser.js         # 全局组合式函数
│   └── ...
└── views/
    └── video-analyzing/
        └── composables/
            └── useVideoList.js
```

5. **状态管理优化建议**
   - 所有模块必须 `namespaced: true`。
   - 导出时统一用模块名，便于按需引入和解构。
   - 全局状态：`store.state.global.user`
   - 模块状态：`store.state.videoAnalyzing.xxx`
   - 组合式函数：直接 `const { data } = useVideoList()`
   - 全局重要状态（如登录信息）可用 `vuex-persistedstate` 或 Pinia 的持久化插件。
   - 业务模块状态一般不持久化，页面离开自动重置。
   - 每个模块应提供 `resetState` action，页面离开时主动调用，防止脏数据残留。
   - 业务模块间禁止直接互相访问状态，如需通信用全局事件总线或 props/emit。

6. **进阶方案（推荐 Pinia）**
   - 新项目或有重构计划，推荐使用 Pinia 替代 Vuex。
   - 语法更简洁，类型推断更好，支持组合式 API。
   - 支持模块自动拆分、热更新、插件扩展。
   - 状态定义和逻辑更聚合，便于维护。
   - 结构示例：
```
src/
├── stores/
│   ├── useGlobalStore.js
│   ├── useUserStore.js
│   ├── useVideoAnalyzingStore.js
│   └── ...
```
   - 每个 store 文件只管理一个领域/模块，导出 `useXxxStore` 组合式函数，使用时直接 `const store = useVideoAnalyzingStore()`。

#### 4. 跨模块组件复用规范

##### 组件分类与作用域

###### 组件分类
```
src/components/
├── Common/                 # 通用组件 (全局复用)
│   ├── Button.vue
│   ├── Modal.vue
│   ├── Loading.vue
│   ├── Table.vue
│   ├── Form.vue
│   └── Pagination.vue
├── Business/               # 业务组件 (跨模块复用)
│   ├── UserCard.vue
│   ├── FileUploader.vue
│   ├── VideoPlayer.vue
│   ├── ImageViewer.vue
│   ├── SearchForm.vue
│   └── StatusTag.vue
├── Domain/                 # 领域组件 (特定领域复用)
│   ├── Video/             # 视频相关组件
│   │   ├── VideoList.vue
│   │   ├── VideoPlayer.vue
│   │   ├── VideoUploader.vue
│   │   └── VideoThumbnail.vue
│   ├── Image/             # 图片相关组件
│   │   ├── ImageList.vue
│   │   ├── ImageViewer.vue
│   │   ├── ImageUploader.vue
│   │   └── ImageCropper.vue
│   └── User/              # 用户相关组件
│       ├── UserList.vue
│       ├── UserCard.vue
│       ├── UserForm.vue
│       └── UserAvatar.vue
└── Module/                 # 模块专用组件 (单模块使用)
    ├── VideoAnalyzing/    # 视频分析模块专用
    │   ├── AnalysisPanel.vue
    │   ├── ResultDisplay.vue
    │   └── TimelineView.vue
    ├── PlatformManagement/ # 平台管理模块专用
    │   ├── RoleSelector.vue
    │   ├── PermissionTree.vue
    │   └── SystemConfig.vue
    └── Upload/            # 上传模块专用
        ├── UploadProgress.vue
        ├── FilePreview.vue
        └── BatchUploader.vue
```

##### 组件复用策略

###### 1. 通用组件 (Common)
```javascript
// ✅ 正确 - 通用组件应该高度抽象，支持多种场景
// components/Common/Table.vue
<template>
  <div class="common-table">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template v-for="slot in Object.keys($slots)" #[slot]="props">
        <slot :name="slot" v-bind="props" />
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  dataSource: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: [Object, Boolean],
    default: () => ({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true
    })
  }
});

const emit = defineEmits(['change']);

const handleTableChange = (pagination, filters, sorter) => {
  emit('change', { pagination, filters, sorter });
};
</script>
```

###### 2. 业务组件 (Business)
```javascript
// ✅ 正确 - 业务组件应该封装特定业务逻辑
// components/Business/FileUploader.vue
<template>
  <div class="file-uploader">
    <a-upload
      :file-list="fileList"
      :before-upload="beforeUpload"
      :custom-request="customUpload"
      @change="handleChange"
    >
      <a-button>
        <upload-outlined />
        选择文件
      </a-button>
    </a-upload>
    
    <div v-if="uploadProgress > 0" class="upload-progress">
      <a-progress :percent="uploadProgress" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  accept: {
    type: String,
    default: '*'
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  multiple: {
    type: Boolean,
    default: false
  },
  onUpload: {
    type: Function,
    required: true
  }
});

const emit = defineEmits(['change', 'success', 'error']);

const fileList = ref([]);
const uploadProgress = ref(0);

const beforeUpload = (file) => {
  // 文件大小检查
  if (file.size > props.maxSize) {
    message.error('文件大小超出限制');
    return false;
  }
  
  // 文件类型检查
  if (props.accept !== '*' && !file.type.match(props.accept)) {
    message.error('文件类型不支持');
    return false;
  }
  
  return true;
};

const customUpload = async ({ file, onProgress, onSuccess, onError }) => {
  try {
    uploadProgress.value = 0;
    
    const result = await props.onUpload(file, (progress) => {
      uploadProgress.value = progress;
      onProgress({ percent: progress });
    });
    
    onSuccess(result);
    emit('success', result);
  } catch (error) {
    onError(error);
    emit('error', error);
  }
};

const handleChange = (info) => {
  fileList.value = info.fileList;
  emit('change', info);
};
</script>
```

###### 3. 领域组件 (Domain)
```javascript
// ✅ 正确 - 领域组件应该封装特定领域的业务逻辑
// components/Domain/Video/VideoPlayer.vue
<template>
  <div class="video-player">
    <video
      ref="videoRef"
      :src="videoUrl"
      :controls="showControls"
      :autoplay="autoplay"
      :loop="loop"
      @loadedmetadata="handleLoadedMetadata"
      @timeupdate="handleTimeUpdate"
      @ended="handleEnded"
    />
    
    <div v-if="showCustomControls" class="custom-controls">
      <a-button @click="togglePlay">
        {{ isPlaying ? '暂停' : '播放' }}
      </a-button>
      <a-slider
        v-model="currentTime"
        :max="duration"
        @change="handleSeek"
      />
      <span>{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  videoUrl: {
    type: String,
    required: true
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showCustomControls: {
    type: Boolean,
    default: false
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  loop: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['play', 'pause', 'ended', 'timeupdate']);

const videoRef = ref(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);

const togglePlay = () => {
  if (isPlaying.value) {
    videoRef.value?.pause();
  } else {
    videoRef.value?.play();
  }
};

const handleSeek = (value) => {
  if (videoRef.value) {
    videoRef.value.currentTime = value;
  }
};

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const handleLoadedMetadata = () => {
  duration.value = videoRef.value?.duration || 0;
};

const handleTimeUpdate = () => {
  currentTime.value = videoRef.value?.currentTime || 0;
  emit('timeupdate', currentTime.value);
};

const handleEnded = () => {
  isPlaying.value = false;
  emit('ended');
};

// 监听播放状态
const handlePlay = () => {
  isPlaying.value = true;
  emit('play');
};

const handlePause = () => {
  isPlaying.value = false;
  emit('pause');
};

onMounted(() => {
  if (videoRef.value) {
    videoRef.value.addEventListener('play', handlePlay);
    videoRef.value.addEventListener('pause', handlePause);
  }
});

onUnmounted(() => {
  if (videoRef.value) {
    videoRef.value.removeEventListener('play', handlePlay);
    videoRef.value.removeEventListener('pause', handlePause);
  }
});
</script>
```

##### 组件迁移与重构规范

###### 1. 组件识别与分类
```javascript
// 组件分类决策树
const componentClassification = {
  // 1. 是否被多个模块使用？
  isUsedByMultipleModules: {
    yes: {
      // 2. 是否包含特定业务逻辑？
      hasBusinessLogic: {
        yes: 'Business', // 业务组件
        no: 'Common'     // 通用组件
      }
    },
    no: {
      // 3. 是否属于特定领域？
      belongsToDomain: {
        yes: 'Domain',   // 领域组件
        no: 'Module'     // 模块专用组件
      }
    }
  }
};
```

###### 2. 组件迁移流程
```javascript
// 组件迁移步骤
const migrationSteps = [
  {
    step: 1,
    action: '识别复用组件',
    description: '分析哪些组件被多个模块使用',
    example: 'FileUploader 在 upload 和 video-analyzing 模块中都有使用'
  },
  {
    step: 2,
    action: '确定组件分类',
    description: '根据组件特性确定应该放在哪个分类下',
    example: 'FileUploader 包含文件上传业务逻辑，应归类为 Business'
  },
  {
    step: 3,
    action: '重构组件接口',
    description: '抽象组件接口，使其更通用',
    example: '添加 props 支持不同的上传配置'
  },
  {
    step: 4,
    action: '更新导入路径',
    description: '更新所有使用该组件的地方的导入路径',
    example: '从 @/views/upload/components/FileUploader.vue 改为 @/components/Business/FileUploader.vue'
  },
  {
    step: 5,
    action: '测试验证',
    description: '确保组件在不同场景下都能正常工作',
    example: '在 upload 和 video-analyzing 模块中测试 FileUploader'
  }
];
```

###### 3. 组件重构示例
```javascript
// 重构前 - 模块专用组件
// views/upload/components/FileUploader.vue
<template>
  <div class="upload-file-uploader">
    <a-upload
      :file-list="fileList"
      :before-upload="beforeUpload"
      @change="handleChange"
    >
      <a-button>上传文件</a-button>
    </a-upload>
  </div>
</template>

<script setup>
// 硬编码的配置
const beforeUpload = (file) => {
  if (file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过10MB');
    return false;
  }
  return true;
};

const handleChange = (info) => {
  // 硬编码的处理逻辑
  if (info.file.status === 'done') {
    // 上传到固定的API
    uploadToAPI(info.file);
  }
};
</script>

// 重构后 - 业务组件
// components/Business/FileUploader.vue
<template>
  <div class="file-uploader">
    <a-upload
      :file-list="fileList"
      :before-upload="beforeUpload"
      :custom-request="customUpload"
      @change="handleChange"
    >
      <a-button>
        <upload-outlined />
        {{ buttonText }}
      </a-button>
    </a-upload>
  </div>
</template>

<script setup>
const props = defineProps({
  // 可配置的文件类型
  accept: {
    type: String,
    default: '*'
  },
  // 可配置的文件大小限制
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024
  },
  // 可配置的按钮文本
  buttonText: {
    type: String,
    default: '选择文件'
  },
  // 可配置的上传函数
  onUpload: {
    type: Function,
    required: true
  },
  // 可配置的验证函数
  onValidate: {
    type: Function,
    default: null
  }
});

const emit = defineEmits(['change', 'success', 'error']);

const beforeUpload = (file) => {
  // 使用配置的验证函数
  if (props.onValidate) {
    return props.onValidate(file);
  }
  
  // 默认验证逻辑
  if (file.size > props.maxSize) {
    message.error(`文件大小不能超过${props.maxSize / 1024 / 1024}MB`);
    return false;
  }
  
  if (props.accept !== '*' && !file.type.match(props.accept)) {
    message.error('文件类型不支持');
    return false;
  }
  
  return true;
};

const customUpload = async ({ file, onProgress, onSuccess, onError }) => {
  try {
    const result = await props.onUpload(file, onProgress);
    onSuccess(result);
    emit('success', result);
  } catch (error) {
    onError(error);
    emit('error', error);
  }
};

const handleChange = (info) => {
  emit('change', info);
};
</script>
```

##### 组件使用规范

###### 1. 导入路径规范
```javascript
// ✅ 正确 - 按组件分类导入
// 通用组件
import CommonTable from '@/components/Common/Table.vue';
import CommonModal from '@/components/Common/Modal.vue';

// 业务组件
import FileUploader from '@/components/Business/FileUploader.vue';
import VideoPlayer from '@/components/Business/VideoPlayer.vue';

// 领域组件
import VideoList from '@/components/Domain/Video/VideoList.vue';
import ImageViewer from '@/components/Domain/Image/ImageViewer.vue';

// 模块专用组件
import AnalysisPanel from '@/components/Module/VideoAnalyzing/AnalysisPanel.vue';

// ❌ 错误 - 避免直接从模块目录导入可复用组件
import FileUploader from '@/views/upload/components/FileUploader.vue';
import VideoPlayer from '@/views/video-analyzing/components/VideoPlayer.vue';
```

###### 2. 组件使用示例
```javascript
// 在 upload 模块中使用 FileUploader
<template>
  <div class="upload-page">
    <FileUploader
      accept=".jpg,.png,.gif"
      :max-size="5 * 1024 * 1024"
      button-text="上传图片"
      :on-upload="handleImageUpload"
      :on-validate="validateImage"
      @success="handleUploadSuccess"
      @error="handleUploadError"
    />
  </div>
</template>

<script setup>
import FileUploader from '@/components/Business/FileUploader.vue';

const handleImageUpload = async (file, onProgress) => {
  // 图片上传逻辑
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await api.uploadImage(formData, {
    onUploadProgress: (progressEvent) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress({ percent: progress });
    }
  });
  
  return response.data;
};

const validateImage = (file) => {
  // 自定义验证逻辑
  if (file.size > 5 * 1024 * 1024) {
    message.error('图片大小不能超过5MB');
    return false;
  }
  
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、GIF 格式的图片');
    return false;
  }
  
  return true;
};
</script>

// 在 video-analyzing 模块中使用 FileUploader
<template>
  <div class="video-analyzing-page">
    <FileUploader
      accept=".mp4,.avi,.mov"
      :max-size="100 * 1024 * 1024"
      button-text="上传视频"
      :on-upload="handleVideoUpload"
      :on-validate="validateVideo"
      @success="handleVideoUploadSuccess"
      @error="handleVideoUploadError"
    />
  </div>
</template>

<script setup>
import FileUploader from '@/components/Business/FileUploader.vue';

const handleVideoUpload = async (file, onProgress) => {
  // 视频上传逻辑
  const formData = new FormData();
  formData.append('video', file);
  
  const response = await api.uploadVideo(formData, {
    onUploadProgress: (progressEvent) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress({ percent: progress });
    }
  });
  
  return response.data;
};

const validateVideo = (file) => {
  // 视频验证逻辑
  if (file.size > 100 * 1024 * 1024) {
    message.error('视频大小不能超过100MB');
    return false;
  }
  
  const allowedTypes = ['video/mp4', 'video/avi', 'video/quicktime'];
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持 MP4、AVI、MOV 格式的视频');
    return false;
  }
  
  return true;
};
</script>
```

##### 组件文档规范

###### 1. 组件文档模板
```javascript
// components/Business/FileUploader/README.md
# FileUploader 文件上传组件

## 组件描述
通用的文件上传组件，支持多种文件类型和自定义验证规则。

## 功能特性
- 支持多种文件类型
- 自定义文件大小限制
- 自定义验证规则
- 上传进度显示
- 错误处理

## 使用场景
- 图片上传
- 视频上传
- 文档上传
- 批量文件上传

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| accept | String | '*' | 接受的文件类型 |
| maxSize | Number | 10485760 | 文件大小限制（字节） |
| buttonText | String | '选择文件' | 按钮文本 |
| onUpload | Function | - | 上传函数（必需） |
| onValidate | Function | null | 自定义验证函数 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | info | 文件列表变化时触发 |
| success | result | 上传成功时触发 |
| error | error | 上传失败时触发 |

## 使用示例

```vue
<template>
  <FileUploader
    accept=".jpg,.png"
    :max-size="5 * 1024 * 1024"
    button-text="上传图片"
    :on-upload="handleUpload"
    @success="handleSuccess"
  />
</template>

<script setup>
import FileUploader from '@/components/Business/FileUploader.vue';

const handleUpload = async (file, onProgress) => {
  // 上传逻辑
};

const handleSuccess = (result) => {
  console.log('上传成功:', result);
};
</script>
```

## 注意事项
1. onUpload 函数是必需的
2. 文件大小限制建议根据实际需求设置
3. 建议提供 onValidate 函数进行自定义验证
```

###### 2. 组件索引文件
```javascript
// components/index.js
// 通用组件
export { default as CommonTable } from './Common/Table.vue';
export { default as CommonModal } from './Common/Modal.vue';
export { default as CommonLoading } from './Common/Loading.vue';

// 业务组件
export { default as FileUploader } from './Business/FileUploader.vue';
export { default as VideoPlayer } from './Business/VideoPlayer.vue';
export { default as ImageViewer } from './Business/ImageViewer.vue';

// 领域组件
export { default as VideoList } from './Domain/Video/VideoList.vue';
export { default as VideoPlayer as DomainVideoPlayer } from './Domain/Video/VideoPlayer.vue';
export { default as ImageList } from './Domain/Image/ImageList.vue';
export { default as ImageViewer as DomainImageViewer } from './Domain/Image/ImageViewer.vue';

// 模块专用组件
export { default as AnalysisPanel } from './Module/VideoAnalyzing/AnalysisPanel.vue';
export { default as RoleSelector } from './Module/PlatformManagement/RoleSelector.vue';
```

##### 组件测试规范

###### 1. 组件测试结构
```javascript
// components/Business/__tests__/FileUploader.test.js
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import FileUploader from '../FileUploader.vue';

describe('FileUploader', () => {
  const mockUpload = vi.fn();
  
  const createWrapper = (props = {}) => {
    return mount(FileUploader, {
      props: {
        onUpload: mockUpload,
        ...props
      }
    });
  };

  it('应该正确渲染上传按钮', () => {
    const wrapper = createWrapper();
    expect(wrapper.find('button').text()).toBe('选择文件');
  });

  it('应该支持自定义按钮文本', () => {
    const wrapper = createWrapper({ buttonText: '上传图片' });
    expect(wrapper.find('button').text()).toBe('上传图片');
  });

  it('应该验证文件大小限制', async () => {
    const wrapper = createWrapper({ maxSize: 1024 });
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    Object.defineProperty(file, 'size', { value: 2048 });
    
    const upload = wrapper.findComponent({ name: 'AUpload' });
    const result = await upload.vm.beforeUpload(file);
    
    expect(result).toBe(false);
  });

  it('应该调用上传函数', async () => {
    const wrapper = createWrapper();
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    const upload = wrapper.findComponent({ name: 'AUpload' });
    await upload.vm.customRequest({ file });
    
    expect(mockUpload).toHaveBeenCalledWith(file, expect.any(Function));
  });
});
```

###### 2. 组件集成测试
```javascript
// tests/integration/FileUploader.integration.test.js
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import FileUploader from '@/components/Business/FileUploader.vue';

describe('FileUploader 集成测试', () => {
  it('应该在图片上传场景中正常工作', async () => {
    const mockUpload = vi.fn().mockResolvedValue({ url: 'test.jpg' });
    const wrapper = mount(FileUploader, {
      props: {
        accept: '.jpg,.png',
        maxSize: 5 * 1024 * 1024,
        buttonText: '上传图片',
        onUpload: mockUpload
      }
    });

    // 模拟文件选择
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const upload = wrapper.findComponent({ name: 'AUpload' });
    
    await upload.vm.customRequest({ 
      file,
      onSuccess: vi.fn(),
      onError: vi.fn()
    });

    expect(mockUpload).toHaveBeenCalled();
  });

  it('应该在视频上传场景中正常工作', async () => {
    const mockUpload = vi.fn().mockResolvedValue({ url: 'test.mp4' });
    const wrapper = mount(FileUploader, {
      props: {
        accept: '.mp4,.avi',
        maxSize: 100 * 1024 * 1024,
        buttonText: '上传视频',
        onUpload: mockUpload
      }
    });

    // 模拟文件选择
    const file = new File(['test'], 'test.mp4', { type: 'video/mp4' });
    const upload = wrapper.findComponent({ name: 'AUpload' });
    
    await upload.vm.customRequest({ 
      file,
      onSuccess: vi.fn(),
      onError: vi.fn()
    });

    expect(mockUpload).toHaveBeenCalled();
  });
});
```

#### 2. 文件命名规范

##### Vue 组件文件
```javascript
// ✅ 正确 - 使用 PascalCase
UserProfile.vue
FileUploader.vue
VideoPlayer.vue
ImageComparison.vue
FaceRecognition.vue

// ❌ 错误 - 使用其他命名方式
userProfile.vue
file-uploader.vue
video_player.vue
```

##### JavaScript 工具文件
```javascript
// ✅ 正确 - 使用 camelCase
userService.js
fileUploader.js
videoPlayer.js
imageProcessor.js
faceDetector.js

// ❌ 错误 - 使用其他命名方式
UserService.js
file-uploader.js
video_player.js
```

##### 样式文件
```javascript
// ✅ 正确 - 使用 kebab-case
user-profile.less
file-uploader.less
video-player.less
image-comparison.less

// ❌ 错误 - 使用其他命名方式
userProfile.less
fileUploader.less
user_profile.less
```

##### 常量文件
```javascript
// ✅ 正确 - 使用 UPPER_SNAKE_CASE
APP_MENUS.js
DEFAULT_CONFIG.js
ERROR_CODE.js
COLORS.js
API_ENDPOINTS.js

// ❌ 错误 - 使用其他命名方式
appMenus.js
defaultConfig.js
errorCode.js
```

### 代码书写规范

#### 1. 变量声明规范

##### 使用 const 和 let
```javascript
// ✅ 正确 - 优先使用 const，需要重新赋值时使用 let
const userName = 'admin';
const userConfig = { name: 'John', age: 30 };
let currentIndex = 0;
let isLoading = false;

// ❌ 错误 - 避免使用 var
var userName = 'admin';
var userConfig = { name: 'John', age: 30 };
```

##### 解构赋值
```javascript
// ✅ 正确 - 使用解构赋值
const { name, age, email } = user;
const [first, second, ...rest] = array;
const { data, pagination } = response;

// ❌ 错误 - 避免逐个赋值
const name = user.name;
const age = user.age;
const email = user.email;
```

##### 对象属性简写
```javascript
// ✅ 正确 - 使用属性简写
const user = { name, age, email };
const config = { apiUrl, timeout, retries };

// ❌ 错误 - 避免重复写属性名
const user = { name: name, age: age, email: email };
```

#### 2. 函数书写规范

##### 箭头函数
```javascript
// ✅ 正确 - 使用箭头函数
const getUserInfo = () => {};
const updateUser = (user) => {};
const validateForm = (formData) => {
  return formData.name && formData.email;
};

// ❌ 错误 - 避免使用 function 关键字
function getUserInfo() {}
function updateUser(user) {}
```

##### 异步函数
```javascript
// ✅ 正确 - 使用 async/await
const fetchUserData = async (userId) => {
  try {
    const response = await api.getUser(userId);
    return response.data;
  } catch (error) {
    console.error('获取用户数据失败:', error);
    throw error;
  }
};

// ❌ 错误 - 避免使用 Promise.then()
const fetchUserData = (userId) => {
  return api.getUser(userId)
    .then(response => response.data)
    .catch(error => {
      console.error('获取用户数据失败:', error);
      throw error;
    });
};
```

#### 3. 对象和数组书写规范

##### 对象字面量
```javascript
// ✅ 正确 - 使用简写语法
const user = {
  name,
  age,
  email,
  getFullName() {
    return `${this.name} ${this.age}`;
  }
};

// ❌ 错误 - 避免使用传统语法
const user = {
  name: name,
  age: age,
  email: email,
  getFullName: function() {
    return this.name + ' ' + this.age;
  }
};
```

##### 数组方法
```javascript
// ✅ 正确 - 使用现代数组方法
const filteredUsers = users.filter(user => user.active);
const userNames = users.map(user => user.name);
const totalAge = users.reduce((sum, user) => sum + user.age, 0);

// ❌ 错误 - 避免使用 for 循环
const filteredUsers = [];
for (let i = 0; i < users.length; i++) {
  if (users[i].active) {
    filteredUsers.push(users[i]);
  }
}
```

#### 4. 模板字符串规范

```javascript
// ✅ 正确 - 使用模板字符串
const greeting = `Hello, ${userName}!`;
const url = `${baseUrl}/api/users/${userId}`;
const message = `
  用户信息:
  姓名: ${user.name}
  年龄: ${user.age}
  邮箱: ${user.email}
`;

// ❌ 错误 - 避免使用字符串拼接
const greeting = 'Hello, ' + userName + '!';
const url = baseUrl + '/api/users/' + userId;
```

#### 5. 条件语句规范

##### 三元运算符
```javascript
// ✅ 正确 - 简单条件使用三元运算符
const status = isActive ? 'active' : 'inactive';
const message = hasError ? '操作失败' : '操作成功';

// ❌ 错误 - 避免过度使用三元运算符
const complexResult = condition1 ? 
  (condition2 ? value1 : value2) : 
  (condition3 ? value3 : value4);
```

##### 条件判断
```javascript
// ✅ 正确 - 使用严格相等
if (status === 'active') {}
if (count !== 0) {}
if (user && user.name) {}

// ❌ 错误 - 避免使用 == 和 !=
if (status == 'active') {}
if (count != 0) {}
```

#### 6. 错误处理规范

```javascript
// ✅ 正确 - 使用 try-catch 处理异步错误
const uploadFile = async (file) => {
  try {
    const result = await api.upload(file);
    return result;
  } catch (error) {
    console.error('文件上传失败:', error);
    throw new Error('文件上传失败，请重试');
  }
};

// ✅ 正确 - 使用可选链和空值合并
const userName = user?.name || 'Unknown';
const config = options?.timeout ?? 5000;
```

### 注释规范

#### 1. 函数注释
```javascript
/**
 * 上传文件到服务器
 * @param {File} file - 要上传的文件对象
 * @param {Object} options - 上传选项
 * @param {number} options.timeout - 超时时间（毫秒）
 * @param {Function} options.onProgress - 进度回调函数
 * @returns {Promise<Object>} 上传结果，包含文件ID和URL
 * @throws {Error} 当上传失败时抛出错误
 * @example
 * const result = await uploadFile(file, {
 *   timeout: 30000,
 *   onProgress: (progress) => console.log(progress)
 * });
 */
const uploadFile = async (file, options = {}) => {
  // 实现代码
};
```

#### 2. 类注释
```javascript
/**
 * 用户管理服务类
 * 
 * @description 提供用户相关的所有操作，包括增删改查
 * <AUTHOR>
 * @since 1.0.0
 * 
 * @example
 * const userService = new UserService();
 * const user = await userService.getUserById('123');
 */
class UserService {
  /**
   * 构造函数
   * @param {string} baseUrl - API基础URL
   */
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
  }

  /**
   * 根据ID获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 用户信息对象
   */
  async getUserById(userId) {
    // 实现代码
  }
}
```

#### 3. 常量注释
```javascript
/**
 * 应用菜单配置
 * 
 * @description 定义应用的主要导航菜单结构
 * @type {Array<Object>}
 */
const APP_MENUS = [
  {
    name: '首页',           // 菜单显示名称
    extendName: '',         // 扩展名称
    icon: icon1n,          // 默认图标
    activeIcon: icon1a,    // 激活状态图标
    showDivider: false,    // 是否显示分割线
    path: '/home',         // 路由路径
    children: [            // 子菜单
      {
        name: '首页',
        path: '/home',
      },
    ],
  },
];
```

#### 4. 行内注释
```javascript
// ✅ 正确 - 使用行内注释解释复杂逻辑
const result = users
  .filter(user => user.active)           // 过滤活跃用户
  .map(user => ({                        // 转换用户数据
    id: user.id,
    name: user.name,
    email: user.email
  }))
  .sort((a, b) => a.name.localeCompare(b.name)); // 按姓名排序

// ✅ 正确 - 解释业务逻辑
const isEligibleForDiscount = user.age >= 18 && user.purchaseCount >= 5; // 18岁以上且购买5次以上可享受折扣

// ❌ 错误 - 避免无意义的注释
const userName = 'John'; // 设置用户名为John
const age = 30; // 设置年龄为30
```

### 导入导出规范

#### 1. 导入规范
```javascript
// ✅ 正确 - 按类型分组导入
// 第三方库导入
import Vue from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import { createStore } from 'vuex';

// 本地模块导入
import { DEFAULT_CONFIG, ERROR_CODE } from '@/constants';
import { authAPI, uploadAPI } from '@/api';
import { useAuth, useUpload } from '@/composables';

// 组件导入
import UserCard from '@/components/UserCard.vue';
import FileUploader from '@/components/FileUploader.vue';

// 工具函数导入
import { formatDate, validateEmail } from '@/utils';

// ❌ 错误 - 避免混合导入
import Vue from 'vue';
import UserCard from '@/components/UserCard.vue';
import { createRouter } from 'vue-router';
import { DEFAULT_CONFIG } from '@/constants';
```

#### 2. 导出规范
```javascript
// ✅ 正确 - 使用命名导出
export const API_ENDPOINTS = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  UPLOAD: '/upload/file'
};

export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest'
};

// ✅ 正确 - 使用默认导出
const DEFAULT_CONFIG = {
  apiBaseUrl: 'https://api.example.com',
  timeout: 5000
};

export default DEFAULT_CONFIG;

// ❌ 错误 - 避免混合导出方式
export const API_ENDPOINTS = {};
export default DEFAULT_CONFIG;
export { USER_ROLES };
```

### 代码格式化规范

#### 1. 缩进和空格
```javascript
// ✅ 正确 - 使用2个空格缩进
const user = {
  name: 'John',
  age: 30,
  profile: {
    email: '<EMAIL>',
    phone: '************'
  }
};

// ✅ 正确 - 函数参数换行
const createUser = (
  name,
  email,
  age,
  options = {}
) => {
  // 函数体
};

// ❌ 错误 - 使用制表符或4个空格
const user = {
    name: 'John',
    age: 30
};
```

#### 2. 分号和逗号
```javascript
// ✅ 正确 - 使用分号结尾
const userName = 'admin';
const userAge = 30;

// ✅ 正确 - 对象属性后使用逗号
const user = {
  name: 'John',
  age: 30,
  email: '<EMAIL>',
};

// ✅ 正确 - 数组元素后使用逗号
const colors = [
  'red',
  'green',
  'blue',
];

// ❌ 错误 - 避免尾随逗号不一致
const user = {
  name: 'John',
  age: 30,  // 这里没有逗号
  email: '<EMAIL>'  // 这里也没有逗号
};
```

#### 3. 引号使用
```javascript
// ✅ 正确 - 使用单引号
const message = 'Hello World';
const template = `<div class="user">${userName}</div>`;

// ✅ 正确 - 对象属性使用单引号（如果需要）
const config = {
  'api-url': 'https://api.example.com',
  'timeout': 5000
};

// ❌ 错误 - 避免使用双引号
const message = "Hello World";
```

### 性能优化规范

#### 1. 变量声明优化
```javascript
// ✅ 正确 - 避免重复声明
const { name, age, email } = user;
const { data, pagination } = response;

// ❌ 错误 - 避免重复声明
const name = user.name;
const age = user.age;
const email = user.email;
const data = response.data;
const pagination = response.pagination;
```

#### 2. 条件判断优化
```javascript
// ✅ 正确 - 使用短路求值
const userName = user?.name || 'Unknown';
const config = options?.timeout ?? 5000;

// ✅ 正确 - 使用 includes 检查数组
const isAdmin = userRoles.includes('admin');

// ❌ 错误 - 避免使用 indexOf
const isAdmin = userRoles.indexOf('admin') !== -1;
```

#### 3. 循环优化
```javascript
// ✅ 正确 - 使用 for...of 遍历数组
for (const item of items) {
  console.log(item);
}

// ✅ 正确 - 使用 for...in 遍历对象
for (const key in object) {
  if (object.hasOwnProperty(key)) {
    console.log(key, object[key]);
  }
}

// ❌ 错误 - 避免使用 for...in 遍历数组
for (const index in array) {
  console.log(array[index]);
}
```

这些规范将帮助团队：
- 保持代码风格的一致性
- 提高代码的可读性和可维护性
- 减少代码审查中的争议
- 提升开发效率

## 新增规范说明

### 视图模块子页面结构规范

本次更新新增了 **视图模块子页面结构规范** 章节，主要包含以下内容：

#### 1. 子模块页面组织规范
- **目录结构**: 统一的子模块目录结构，包含 `pages/`、`components/`、`composables/`、`utils/`、`styles/` 等子目录
- **页面分类**: 按功能将子页面分为 `list/`、`detail/`、`create/`、`edit/` 等标准页面类型
- **模块隔离**: 每个模块拥有独立的组件、组合式函数、工具函数和样式文件

#### 2. 路由配置规范
- **命名路由**: 使用命名路由进行页面跳转，避免硬编码路径
- **路由元信息**: 统一的路由元信息配置，包含标题、面包屑、权限等
- **嵌套路由**: 合理的路由嵌套结构，支持模块内页面跳转

#### 3. 页面跳转规范
- **命名路由跳转**: 使用 `router.push({ name: 'RouteName' })` 进行跳转
- **参数传递**: 通过 `params` 和 `query` 传递页面参数
- **返回操作**: 统一的返回上一页和返回列表页操作

#### 4. 组件结构规范
- **主页面组件**: 模块的主入口组件，包含页面头部和内容区域
- **子页面组件**: 标准化的子页面组件结构，包含搜索、表格、表单等
- **组件复用**: 模块内组件的合理复用和组合

#### 5. 状态管理规范
- **模块级状态**: 每个模块独立的状态管理
- **状态隔离**: 避免模块间状态污染
- **状态持久化**: 合理的状态持久化策略

#### 6. 组合式函数规范
- **模块专用**: 每个模块专用的组合式函数
- **功能封装**: 将页面跳转、数据获取等功能封装为可复用的组合式函数
- **错误处理**: 统一的错误处理和用户提示

#### 7. 样式规范
- **模块级样式**: 每个模块独立的样式文件
- **样式隔离**: 使用 scoped 样式避免样式冲突
- **响应式设计**: 统一的响应式设计规范

### 跨模块组件复用规范

本次更新还新增了 **跨模块组件复用规范** 章节，主要包含以下内容：

#### 1. 组件分类与作用域
- **通用组件 (Common)**: 高度抽象的组件，支持多种场景，如表格、模态框、按钮等
- **业务组件 (Business)**: 封装特定业务逻辑的组件，如文件上传、视频播放、图片查看等
- **领域组件 (Domain)**: 特定领域的组件，如视频相关、图片相关、用户相关等
- **模块专用组件 (Module)**: 仅在单个模块中使用的组件

#### 2. 组件复用策略
- **通用组件**: 高度抽象，支持多种配置，如 CommonTable 支持自定义列、分页等
- **业务组件**: 封装业务逻辑，提供灵活的接口，如 FileUploader 支持不同文件类型和验证规则
- **领域组件**: 特定领域的标准化组件，如 VideoPlayer 支持多种视频格式和控制选项

#### 3. 组件迁移与重构规范
- **组件识别**: 通过决策树识别组件应该属于哪个分类
- **迁移流程**: 5步迁移流程，从识别到测试验证
- **重构示例**: 从模块专用组件重构为可复用组件的具体示例

#### 4. 组件使用规范
- **导入路径**: 按组件分类导入，避免从模块目录导入可复用组件
- **使用示例**: 在不同模块中使用同一组件的具体示例
- **配置灵活**: 通过 props 支持不同场景的配置需求

#### 5. 组件文档规范
- **文档模板**: 标准化的组件文档模板，包含描述、特性、参数、事件等
- **索引文件**: 统一的组件导出索引，便于管理和使用
- **使用示例**: 详细的使用示例和注意事项

#### 6. 组件测试规范
- **单元测试**: 组件功能的单元测试，验证 props、事件等
- **集成测试**: 组件在不同场景下的集成测试
- **测试覆盖**: 确保组件在各种使用场景下都能正常工作

#### 1. 子模块页面组织规范
- **目录结构**: 统一的子模块目录结构，包含 `pages/`、`components/`、`composables/`、`utils/`、`styles/` 等子目录
- **页面分类**: 按功能将子页面分为 `list/`、`detail/`、`create/`、`edit/` 等标准页面类型
- **模块隔离**: 每个模块拥有独立的组件、组合式函数、工具函数和样式文件

#### 2. 路由配置规范
- **命名路由**: 使用命名路由进行页面跳转，避免硬编码路径
- **路由元信息**: 统一的路由元信息配置，包含标题、面包屑、权限等
- **嵌套路由**: 合理的路由嵌套结构，支持模块内页面跳转

#### 3. 页面跳转规范
- **命名路由跳转**: 使用 `router.push({ name: 'RouteName' })` 进行跳转
- **参数传递**: 通过 `params` 和 `query` 传递页面参数
- **返回操作**: 统一的返回上一页和返回列表页操作

#### 4. 组件结构规范
- **主页面组件**: 模块的主入口组件，包含页面头部和内容区域
- **子页面组件**: 标准化的子页面组件结构，包含搜索、表格、表单等
- **组件复用**: 模块内组件的合理复用和组合

#### 5. 状态管理规范
- **模块级状态**: 每个模块独立的状态管理
- **状态隔离**: 避免模块间状态污染
- **状态持久化**: 合理的状态持久化策略

#### 6. 组合式函数规范
- **模块专用**: 每个模块专用的组合式函数
- **功能封装**: 将页面跳转、数据获取等功能封装为可复用的组合式函数
- **错误处理**: 统一的错误处理和用户提示

#### 7. 样式规范
- **模块级样式**: 每个模块独立的样式文件
- **样式隔离**: 使用 scoped 样式避免样式冲突
- **响应式设计**: 统一的响应式设计规范

### 规范优势

#### 视图模块子页面结构规范优势
1. **结构清晰**: 统一的目录结构和文件组织方式
2. **易于维护**: 模块化的代码组织，便于维护和扩展
3. **团队协作**: 标准化的开发流程，提高团队协作效率
4. **代码复用**: 合理的组件和函数复用机制
5. **性能优化**: 按需加载和状态管理优化

#### 跨模块组件复用规范优势
1. **组件分类明确**: 按复用范围和功能特性对组件进行分类
2. **避免重复开发**: 通过组件复用减少重复代码开发
3. **统一用户体验**: 相同功能的组件在不同模块中保持一致的用户体验
4. **易于维护**: 组件修改只需在一个地方进行，影响范围可控
5. **提高开发效率**: 新功能开发可以直接使用现有组件
6. **降低测试成本**: 复用组件只需测试一次，降低测试成本

### 布局组件规范

##### 1. 布局组件结构规范

```
src/
├── layouts/
│   ├── index.vue              # 主布局组件
│   ├── components/            # 布局专用组件
│   │   ├── Sidebar/          # 侧边栏组件
│   │   │   ├── index.vue
│   │   │   ├── MenuItem.vue
│   │   │   ├── SubMenu.vue
│   │   │   └── Logo.vue
│   │   ├── Header/           # 顶部栏组件
│   │   │   ├── index.vue
│   │   │   ├── Breadcrumb.vue
│   │   │   ├── UserInfo.vue
│   │   │   └── Notification.vue
│   │   ├── Content/          # 主内容区域
│   │   │   ├── index.vue
│   │   │   └── PageHeader.vue
│   │   └── Footer/           # 底部组件
│   │       └── index.vue
│   ├── composables/          # 布局专用组合式函数
│   │   ├── useLayout.js
│   │   ├── useSidebar.js
│   │   └── useHeader.js
│   └── styles/              # 布局专用样式
│       ├── layout.less
│       ├── sidebar.less
│       └── header.less
```

##### 2. 侧边栏（Sidebar）规范

###### 2.1 侧边栏组件结构
```vue
<!-- layouts/components/Sidebar/index.vue -->
<template>
  <div class="sidebar" :class="{ collapsed: isCollapsed }">
    <!-- Logo 区域 -->
    <div class="sidebar-logo">
      <Logo :collapsed="isCollapsed" />
    </div>
    
    <!-- 菜单区域 -->
    <div class="sidebar-menu">
      <a-menu
        :selected-keys="selectedKeys"
        :open-keys="openKeys"
        :mode="isCollapsed ? 'vertical' : 'inline'"
        :inline-collapsed="isCollapsed"
        @click="handleMenuClick"
        @openChange="handleOpenChange"
      >
        <template v-for="menu in menuList" :key="menu.key">
          <MenuItem :menu="menu" />
        </template>
      </a-menu>
    </div>
    
    <!-- 折叠按钮 -->
    <div class="sidebar-trigger" @click="toggleCollapsed">
      <a-button type="text" size="small">
        <MenuFoldOutlined v-if="!isCollapsed" />
        <MenuUnfoldOutlined v-else />
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useSidebar } from '@/layouts/composables/useSidebar';
import MenuItem from './MenuItem.vue';
import Logo from './Logo.vue';

const route = useRoute();
const router = useRouter();
const { isCollapsed, toggleCollapsed, menuList } = useSidebar();

// 当前选中的菜单项
const selectedKeys = computed(() => [route.name]);

// 当前展开的子菜单
const openKeys = ref([]);

// 菜单点击处理
const handleMenuClick = ({ key }) => {
  router.push({ name: key });
};

// 子菜单展开/收起处理
const handleOpenChange = (keys) => {
  openKeys.value = keys;
};

// 监听路由变化，自动展开对应菜单
watch(
  () => route.path,
  (path) => {
    const matchedKeys = route.matched.map(item => item.name);
    openKeys.value = matchedKeys.filter(key => key !== route.name);
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
.sidebar {
  width: 200px;
  height: 100vh;
  background: #001529;
  transition: width 0.2s;
  
  &.collapsed {
    width: 80px;
  }
  
  .sidebar-logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #1f1f1f;
  }
  
  .sidebar-menu {
    flex: 1;
    overflow-y: auto;
    
    :deep(.ant-menu) {
      background: transparent;
      border: none;
      
      .ant-menu-item {
        color: rgba(255, 255, 255, 0.65);
        
        &:hover {
          color: #fff;
          background: #1890ff;
        }
        
        &.ant-menu-item-selected {
          color: #fff;
          background: #1890ff;
        }
      }
    }
  }
  
  .sidebar-trigger {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #1f1f1f;
    background: #001529;
    
    .ant-btn {
      color: rgba(255, 255, 255, 0.65);
      
      &:hover {
        color: #fff;
      }
    }
  }
}
</style>
```

###### 2.2 菜单项组件
```vue
<!-- layouts/components/Sidebar/MenuItem.vue -->
<template>
  <a-menu-item v-if="!menu.children || menu.children.length === 0" :key="menu.key">
    <template #icon>
      <component :is="menu.icon" />
    </template>
    <span>{{ menu.title }}</span>
  </a-menu-item>
  
  <a-sub-menu v-else :key="menu.key">
    <template #icon>
      <component :is="menu.icon" />
    </template>
    <template #title>{{ menu.title }}</template>
    
    <template v-for="child in menu.children" :key="child.key">
      <MenuItem :menu="child" />
    </template>
  </a-sub-menu>
</template>

<script setup>
defineProps({
  menu: {
    type: Object,
    required: true
  }
});
</script>
```

###### 3. 顶部栏（Header）规范

###### 3.1 顶部栏组件结构
```vue
<!-- layouts/components/Header/index.vue -->
<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <Breadcrumb :breadcrumbs="breadcrumbs" />
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 通知 -->
      <Notification />
      
      <!-- 用户信息 -->
      <UserInfo />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import Breadcrumb from './Breadcrumb.vue';
import Notification from './Notification.vue';
import UserInfo from './UserInfo.vue';

const route = useRoute();

// 面包屑导航
const breadcrumbs = computed(() => {
  return route.meta?.breadcrumb || [];
});
</script>

<style lang="less" scoped>
.header {
  height: 64px;
  padding: 0 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .header-left {
    flex: 1;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}
</style>
```

###### 3.2 面包屑组件
```vue
<!-- layouts/components/Header/Breadcrumb.vue -->
<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
      <router-link v-if="item.path" :to="item.path">
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script setup>
defineProps({
  breadcrumbs: {
    type: Array,
    default: () => []
  }
});
</script>

<style lang="less" scoped>
.breadcrumb {
  font-size: 14px;
  
  :deep(.ant-breadcrumb-link) {
    color: #666;
    
    a {
      color: #1890ff;
      
      &:hover {
        color: #40a9ff;
      }
    }
  }
}
</style>
```

###### 3.3 用户信息组件
```vue
<!-- layouts/components/Header/UserInfo.vue -->
<template>
  <a-dropdown>
    <div class="user-info">
      <a-avatar :src="userInfo.avatar" />
      <span class="username">{{ userInfo.name }}</span>
      <DownOutlined />
    </div>
    
    <template #overlay>
      <a-menu @click="handleMenuClick">
        <a-menu-item key="profile">
          <UserOutlined />
          个人中心
        </a-menu-item>
        <a-menu-item key="settings">
          <SettingOutlined />
          系统设置
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="logout">
          <LogoutOutlined />
          退出登录
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

const store = useStore();
const router = useRouter();

const userInfo = computed(() => store.state.global.user);

const handleMenuClick = ({ key }) => {
  switch (key) {
    case 'profile':
      router.push('/profile');
      break;
    case 'settings':
      router.push('/settings');
      break;
    case 'logout':
      store.dispatch('global/logout');
      router.push('/login');
      message.success('退出成功');
      break;
  }
};
</script>

<style lang="less" scoped>
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
  
  &:hover {
    background: #f5f5f5;
  }
  
  .username {
    font-size: 14px;
    color: #333;
  }
}
</style>
```

###### 4. 主布局组件

```vue
<!-- layouts/index.vue -->
<template>
  <div class="layout">
    <!-- 侧边栏 -->
    <Sidebar class="layout-sidebar" />
    
    <!-- 主内容区域 -->
    <div class="layout-main">
      <!-- 顶部栏 -->
      <Header class="layout-header" />
      
      <!-- 内容区域 -->
      <div class="layout-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import Sidebar from './components/Sidebar/index.vue';
import Header from './components/Header/index.vue';
</script>

<style lang="less" scoped>
.layout {
  display: flex;
  height: 100vh;
  
  .layout-sidebar {
    flex-shrink: 0;
  }
  
  .layout-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .layout-header {
      flex-shrink: 0;
    }
    
    .layout-content {
      flex: 1;
      padding: 24px;
      background: #f0f2f5;
      overflow-y: auto;
    }
  }
}
</style>
```

###### 5. 布局组合式函数

```javascript
// layouts/composables/useLayout.js
import { ref, computed } from 'vue';
import { useStore } from 'vuex';

export const useLayout = () => {
  const store = useStore();
  
  // 侧边栏折叠状态
  const isCollapsed = ref(false);
  
  // 切换侧边栏折叠状态
  const toggleCollapsed = () => {
    isCollapsed.value = !isCollapsed.value;
  };
  
  // 获取菜单列表
  const menuList = computed(() => {
    return store.state.global.menuList || [];
  });
  
  return {
    isCollapsed,
    toggleCollapsed,
    menuList
  };
};

// layouts/composables/useSidebar.js
import { useLayout } from './useLayout';

export const useSidebar = () => {
  const { isCollapsed, toggleCollapsed, menuList } = useLayout();
  
  return {
    isCollapsed,
    toggleCollapsed,
    menuList
  };
};

// layouts/composables/useHeader.js
import { computed } from 'vue';
import { useRoute } from 'vue-router';

export const useHeader = () => {
  const route = useRoute();
  
  // 获取面包屑导航
  const breadcrumbs = computed(() => {
    return route.meta?.breadcrumb || [];
  });
  
  return {
    breadcrumbs
  };
};
```

###### 6. 布局样式规范

```less
// layouts/styles/layout.less
.layout {
  // 响应式布局
  @media (max-width: 768px) {
    .layout-sidebar {
      position: fixed;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s;
      
      &.show {
        transform: translateX(0);
      }
    }
    
    .layout-main {
      margin-left: 0;
    }
  }
}

// layouts/styles/sidebar.less
.sidebar {
  // 菜单项样式
  .ant-menu-item {
    margin: 4px 8px;
    border-radius: 6px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
  
  // 子菜单样式
  .ant-menu-submenu {
    .ant-menu-submenu-title {
      margin: 4px 8px;
      border-radius: 6px;
    }
  }
}

// layouts/styles/header.less
.header {
  // 阴影效果
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  // 响应式
  @media (max-width: 768px) {
    padding: 0 16px;
    
    .header-right {
      gap: 8px;
    }
  }
}
```

###### 7. 布局组件使用规范

```javascript
// 在路由中使用布局
const routes = [
  {
    path: '/',
    component: () => import('@/layouts/index.vue'),
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          breadcrumb: ['首页']
        }
      }
    ]
  }
];

// 在页面中使用布局组件
<template>
  <div class="page">
    <PageHeader title="页面标题" />
    <div class="page-content">
      <!-- 页面内容 -->
    </div>
  </div>
</template>
```

###### 8. 布局组件最佳实践

1. **响应式设计**: 布局组件必须支持移动端适配
2. **主题切换**: 支持明暗主题切换
3. **权限控制**: 菜单项根据用户权限动态显示
4. **状态持久化**: 侧边栏折叠状态等需要持久化
5. **性能优化**: 使用懒加载和虚拟滚动优化大菜单渲染
6. **无障碍访问**: 支持键盘导航和屏幕阅读器
7. **国际化**: 支持多语言切换
8. **自定义配置**: 支持用户自定义布局配置

##### 组件封装思想规范

###### 1. 组件封装原则

###### 1.1 单一职责原则
```javascript
// ✅ 正确 - 组件职责单一，只负责一个功能
// components/Business/FileUploader.vue - 只负责文件上传
<template>
  <div class="file-uploader">
    <a-upload :before-upload="beforeUpload" @change="handleChange">
      <a-button>选择文件</a-button>
    </a-upload>
  </div>
</template>

<script setup>
// 只包含文件上传相关的逻辑
const beforeUpload = (file) => {
  // 文件验证逻辑
};

const handleChange = (info) => {
  // 文件变化处理逻辑
};
</script>

// ❌ 错误 - 组件职责过多，既处理上传又处理预览
<template>
  <div class="file-manager">
    <FileUploader />
    <FilePreview />
    <FileList />
    <FileEdit />
  </div>
</template>
```

###### 1.2 可复用性原则
```javascript
// ✅ 正确 - 组件高度可复用，支持多种配置
// components/Business/DataTable.vue
<template>
  <div class="data-table">
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template v-for="slot in Object.keys($slots)" #[slot]="props">
        <slot :name="slot" v-bind="props" />
      </template>
    </a-table>
  </div>
</template>

<script setup>
const props = defineProps({
  columns: { type: Array, required: true },
  dataSource: { type: Array, default: () => [] },
  loading: { type: Boolean, default: false },
  pagination: { type: [Object, Boolean], default: () => ({}) }
});

const emit = defineEmits(['change']);

const handleTableChange = (pagination, filters, sorter) => {
  emit('change', { pagination, filters, sorter });
};
</script>

// 使用示例 - 在不同场景下复用
<template>
  <!-- 用户列表 -->
  <DataTable
    :columns="userColumns"
    :data-source="userList"
    :loading="userLoading"
    @change="handleUserTableChange"
  >
    <template #action="{ record }">
      <a-space>
        <a @click="editUser(record)">编辑</a>
        <a @click="deleteUser(record)">删除</a>
      </a-space>
    </template>
  </DataTable>

  <!-- 产品列表 -->
  <DataTable
    :columns="productColumns"
    :data-source="productList"
    :loading="productLoading"
    @change="handleProductTableChange"
  >
    <template #image="{ record }">
      <img :src="record.image" :alt="record.name" />
    </template>
  </DataTable>
</template>
```

###### 1.3 组合优于继承原则
```javascript
// ✅ 正确 - 使用组合方式构建复杂组件
// components/Business/UserCard.vue
<template>
  <div class="user-card">
    <UserAvatar :user="user" />
    <UserInfo :user="user" />
    <UserActions :user="user" @edit="handleEdit" @delete="handleDelete" />
  </div>
</template>

<script setup>
import UserAvatar from './UserAvatar.vue';
import UserInfo from './UserInfo.vue';
import UserActions from './UserActions.vue';

const props = defineProps({
  user: { type: Object, required: true }
});

const emit = defineEmits(['edit', 'delete']);

const handleEdit = (user) => {
  emit('edit', user);
};

const handleDelete = (user) => {
  emit('delete', user);
};
</script>

// ❌ 错误 - 使用继承方式，难以维护和扩展
class UserCard extends BaseCard {
  // 继承基类，添加用户特定功能
}
```

###### 2. 组件设计模式

###### 2.1 容器组件与展示组件分离
```javascript
// 容器组件 - 负责数据获取和状态管理
// views/user/UserList.vue
<template>
  <div class="user-list">
    <UserListHeader @search="handleSearch" @add="handleAdd" />
    <UserListTable
      :users="users"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
    />
    <UserListPagination
      :pagination="pagination"
      @change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useUserList } from '@/views/user/composables/useUserList';
import UserListHeader from './components/UserListHeader.vue';
import UserListTable from './components/UserListTable.vue';
import UserListPagination from './components/UserListPagination.vue';

const { users, loading, pagination, fetchUsers, deleteUser } = useUserList();

const handleSearch = (params) => {
  fetchUsers(params);
};

const handleAdd = () => {
  // 跳转到添加页面
};

const handleEdit = (user) => {
  // 跳转到编辑页面
};

const handleDelete = async (user) => {
  await deleteUser(user.id);
  fetchUsers();
};

const handlePageChange = (page) => {
  fetchUsers({ page });
};

onMounted(() => {
  fetchUsers();
});
</script>

// 展示组件 - 只负责渲染，不包含业务逻辑
// views/user/components/UserListTable.vue
<template>
  <a-table
    :columns="columns"
    :data-source="users"
    :loading="loading"
    :pagination="false"
  >
    <template #action="{ record }">
      <a-space>
        <a @click="$emit('edit', record)">编辑</a>
        <a @click="$emit('delete', record)">删除</a>
      </a-space>
    </template>
  </a-table>
</template>

<script setup>
defineProps({
  users: { type: Array, default: () => [] },
  loading: { type: Boolean, default: false }
});

defineEmits(['edit', 'delete']);

const columns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '邮箱', dataIndex: 'email', key: 'email' },
  { title: '操作', key: 'action', slots: { customRender: 'action' } }
];
</script>
```

###### 2.2 高阶组件模式
```javascript
// 高阶组件 - 为组件添加通用功能
// composables/withLoading.js
import { ref } from 'vue';

export const withLoading = (asyncFunction) => {
  return (component) => {
    return {
      ...component,
      setup(props, context) {
        const loading = ref(false);
        const data = ref(null);
        const error = ref(null);

        const execute = async (...args) => {
          loading.value = true;
          error.value = null;
          
          try {
            data.value = await asyncFunction(...args);
          } catch (err) {
            error.value = err;
          } finally {
            loading.value = false;
          }
        };

        return {
          ...component.setup?.(props, context),
          loading,
          data,
          error,
          execute
        };
      }
    };
  };
};

// 使用高阶组件
// views/user/UserDetail.vue
<template>
  <div class="user-detail">
    <a-spin :spinning="loading">
      <div v-if="data">
        <h1>{{ data.name }}</h1>
        <p>{{ data.email }}</p>
      </div>
    </a-spin>
    <div v-if="error" class="error">
      {{ error.message }}
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { withLoading } from '@/composables/withLoading';
import { getUserById } from '@/api/user';

const UserDetailWithLoading = withLoading(getUserById)({
  setup(props) {
    const { loading, data, error, execute } = UserDetailWithLoading.setup(props);
    
    onMounted(() => {
      execute(props.userId);
    });

    return { loading, data, error };
  }
});
</script>
```

###### 3. 组件通信规范

###### 3.1 Props 向下传递，Events 向上传递
```javascript
// ✅ 正确 - 单向数据流
// 父组件
<template>
  <UserList
    :users="users"
    :loading="loading"
    @edit="handleEdit"
    @delete="handleDelete"
  />
</template>

<script setup>
const users = ref([]);
const loading = ref(false);

const handleEdit = (user) => {
  // 处理编辑逻辑
};

const handleDelete = (user) => {
  // 处理删除逻辑
};
</script>

// 子组件
<template>
  <div class="user-list">
    <div v-for="user in users" :key="user.id">
      <span>{{ user.name }}</span>
      <button @click="$emit('edit', user)">编辑</button>
      <button @click="$emit('delete', user)">删除</button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  users: { type: Array, required: true },
  loading: { type: Boolean, default: false }
});

defineEmits(['edit', 'delete']);
</script>
```

###### 3.2 使用 Provide/Inject 进行深层组件通信
```javascript
// 祖先组件
<script setup>
import { provide, ref } from 'vue';

const theme = ref('light');
const toggleTheme = () => {
  theme.value = theme.value === 'light' ? 'dark' : 'light';
};

provide('theme', {
  theme,
  toggleTheme
});
</script>

// 深层子组件
<script setup>
import { inject } from 'vue';

const { theme, toggleTheme } = inject('theme');
</script>
```

##### 文件代码行数约定

###### 1. 文件大小控制原则

###### 1.1 文件行数限制
```
- Vue 组件文件: 不超过 400 行
- JavaScript 工具文件: 不超过 500 行
- 样式文件: 不超过 500 行
- 配置文件: 不超过 300 行
- 测试文件: 不超过 400 行
```

###### 1.2 文件拆分策略
```javascript
// 当文件超过限制时，按以下策略拆分：

// 1. 组件文件拆分
// 原文件: UserManagement.vue (400行)
// 拆分后:
// - UserManagement.vue (主组件，100行)
// - components/UserList.vue (150行)
// - components/UserForm.vue (120行)
// - composables/useUserManagement.js (80行)

// 2. 工具文件拆分
// 原文件: utils.js (300行)
// 拆分后:
// - utils/date.js (日期相关工具)
// - utils/string.js (字符串相关工具)
// - utils/array.js (数组相关工具)
// - utils/object.js (对象相关工具)

// 3. 样式文件拆分
// 原文件: styles.less (600行)
// 拆分后:
// - styles/variables.less (变量定义)
// - styles/mixins.less (混入定义)
// - styles/components.less (组件样式)
// - styles/layout.less (布局样式)
```

###### 2. 代码复杂度控制

###### 2.1 函数复杂度控制
```javascript
// ✅ 正确 - 函数职责单一，复杂度低
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString();
};

// ❌ 错误 - 函数职责过多，复杂度高
const processUserData = (user) => {
  // 验证用户数据
  if (!user.email || !validateEmail(user.email)) {
    throw new Error('Invalid email');
  }
  
  // 格式化用户数据
  const formattedUser = {
    ...user,
    name: user.name.trim(),
    email: user.email.toLowerCase(),
    createdAt: formatDate(user.createdAt)
  };
  
  // 保存到数据库
  return saveUser(formattedUser);
};

// ✅ 正确 - 拆分为多个小函数
const validateUser = (user) => {
  if (!user.email || !validateEmail(user.email)) {
    throw new Error('Invalid email');
  }
  return user;
};

const formatUser = (user) => {
  return {
    ...user,
    name: user.name.trim(),
    email: user.email.toLowerCase(),
    createdAt: formatDate(user.createdAt)
  };
};

const processUserData = async (user) => {
  const validatedUser = validateUser(user);
  const formattedUser = formatUser(validatedUser);
  return await saveUser(formattedUser);
};
```

###### 2.2 组件复杂度控制
```javascript
// ✅ 正确 - 组件职责单一，复杂度低
// components/UserAvatar.vue (50行)
<template>
  <div class="user-avatar">
    <img v-if="user.avatar" :src="user.avatar" :alt="user.name" />
    <div v-else class="avatar-placeholder">
      {{ user.name.charAt(0).toUpperCase() }}
    </div>
  </div>
</template>

<script setup>
defineProps({
  user: { type: Object, required: true }
});
</script>

// ❌ 错误 - 组件职责过多，复杂度高
// components/UserCard.vue (200行)
<template>
  <div class="user-card">
    <!-- 用户头像 -->
    <UserAvatar :user="user" />
    
    <!-- 用户信息 -->
    <div class="user-info">
      <h3>{{ user.name }}</h3>
      <p>{{ user.email }}</p>
      <p>{{ user.role }}</p>
    </div>
    
    <!-- 用户统计 -->
    <div class="user-stats">
      <div class="stat">
        <span class="label">登录次数</span>
        <span class="value">{{ user.loginCount }}</span>
      </div>
      <div class="stat">
        <span class="label">最后登录</span>
        <span class="value">{{ formatDate(user.lastLogin) }}</span>
      </div>
    </div>
    
    <!-- 用户操作 -->
    <div class="user-actions">
      <a-button @click="handleEdit">编辑</a-button>
      <a-button @click="handleDelete">删除</a-button>
      <a-button @click="handleReset">重置密码</a-button>
    </div>
  </div>
</template>

<script setup>
// 大量的业务逻辑...
const handleEdit = () => { /* ... */ };
const handleDelete = () => { /* ... */ };
const handleReset = () => { /* ... */ };
const formatDate = (date) => { /* ... */ };
</script>

// ✅ 正确 - 拆分为多个小组件
// components/UserCard.vue (主组件，80行)
<template>
  <div class="user-card">
    <UserAvatar :user="user" />
    <UserInfo :user="user" />
    <UserStats :user="user" />
    <UserActions
      :user="user"
      @edit="$emit('edit', user)"
      @delete="$emit('delete', user)"
      @reset="$emit('reset', user)"
    />
  </div>
</template>

<script setup>
defineProps({
  user: { type: Object, required: true }
});

defineEmits(['edit', 'delete', 'reset']);
</script>

// components/UserInfo.vue (40行)
// components/UserStats.vue (50行)
// components/UserActions.vue (60行)
```

###### 3. 代码组织规范

###### 3.1 文件结构规范
```javascript
// Vue 组件文件结构
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 1. 导入语句
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

// 2. Props 定义
const props = defineProps({
  // props 定义
});

// 3. Emits 定义
const emit = defineEmits(['change', 'submit']);

// 4. 响应式数据
const loading = ref(false);
const data = ref([]);

// 5. 计算属性
const filteredData = computed(() => {
  // 计算逻辑
});

// 6. 方法定义
const handleSubmit = () => {
  // 方法逻辑
};

// 7. 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style lang="less" scoped>
/* 样式定义 */
</style>
```

###### 3.2 导入语句规范
```javascript
// ✅ 正确 - 按类型分组导入
// 1. 第三方库导入
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

// 2. 本地模块导入
import { useUser } from '@/composables/useUser';
import { formatDate } from '@/utils/date';

// 3. 组件导入
import UserCard from '@/components/UserCard.vue';
import UserForm from '@/components/UserForm.vue';

// 4. 类型导入（如果使用 TypeScript）
import type { User } from '@/types/user';

// ❌ 错误 - 混合导入，难以维护
import { ref } from 'vue';
import UserCard from '@/components/UserCard.vue';
import { useRouter } from 'vue-router';
import { formatDate } from '@/utils/date';
```

###### 4. 代码质量检查

###### 4.1 ESLint 配置
```javascript
// .eslintrc.js
module.exports = {
  rules: {
    // 限制函数复杂度
    'complexity': ['error', 10],
    
    // 限制函数参数数量
    'max-params': ['error', 4],
    
    // 限制函数行数
    'max-lines-per-function': ['error', 50],
    
    // 限制文件行数
    'max-lines': ['error', 400],
    
    // 限制嵌套深度
    'max-depth': ['error', 4],
    
    // 限制圈复杂度
    'cyclomatic-complexity': ['error', 10]
  }
};
```

###### 4.2 代码审查清单
```javascript
// 代码审查时检查以下项目：
const codeReviewChecklist = [
  '文件行数是否超过限制？',
  '函数复杂度是否过高？',
  '组件职责是否单一？',
  '是否有重复代码？',
  '是否有未使用的代码？',
  '是否有硬编码的值？',
  '是否有适当的错误处理？',
  '是否有适当的注释？',
  '是否有适当的测试？'
];
```

###### 5. 重构指导原则

###### 5.1 何时需要重构
```javascript
// 当出现以下情况时，考虑重构：
const refactorTriggers = [
  '文件行数超过限制',
  '函数复杂度超过 10',
  '组件职责不单一',
  '存在重复代码',
  '代码难以理解',
  '测试覆盖率低',
  '性能问题明显'
];
```

###### 5.2 重构步骤
```javascript
// 重构步骤：
const refactorSteps = [
  '1. 识别需要重构的代码',
  '2. 编写测试用例',
  '3. 提取公共函数/组件',
  '4. 拆分复杂函数/组件',
  '5. 优化代码结构',
  '6. 运行测试验证',
  '7. 更新文档'
];
```

### 使用建议

1. **新项目**: 建议在新项目中直接采用此规范
2. **现有项目**: 可以逐步迁移现有项目到新规范
3. **团队培训**: 建议团队成员学习并掌握此规范
4. **持续改进**: 根据项目实际情况持续优化规范

这些规范将帮助团队：
1. **控制代码复杂度**: 避免过度复杂的代码
2. **提高可维护性**: 文件大小适中，便于维护
3. **提升开发效率**: 清晰的代码结构，便于理解
4. **保证代码质量**: 通过工具和审查确保代码质量
5. **便于团队协作**: 统一的代码组织方式