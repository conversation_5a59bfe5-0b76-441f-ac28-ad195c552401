<!--
 * @Author: yuzhouisme
 * @Date: 2025-03-25 
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-23 09:31:31
 * @FilePath: \platform-face-web\src\components\Comparison\ComparisonParamsPopover\index.vue
 * @Description: 将“编辑比对参数表单”包装在弹出窗中，并且会请求“可选的算法列表”、“可选的人像组/库列表”等数据，简化调用。
 * 
-->
<template>
  <div class="container">
    <slot name="button" :handle-confirm="handleConfirm" :disabled="requirePortraitFirstDisable"
      :handle-button-mouse-enter="handleButtonMouseEnter" :handle-button-mouse-leave="handleButtonMouseLeave"
      :is-button-hovered="buttonHovered">
      <CustomButtonWithTooltip :icon-name="comparisonType" icon-size="14px" width="200px" height="40px"
        @mouseenter="handleButtonMouseEnter" @mouseleave="handleButtonMouseLeave" @click="handleConfirm">
        {{
          buttonText !== ''
            ? buttonText
            : comparisonType === 'image-to-library'
              ? '一键大库检索'
              : '一键同人预判'
        }}
      </CustomButtonWithTooltip>
    </slot>

    <transition name="fade">
      <div v-if="showPopover" class="popover" :class="[placement, { 'separated': separated }]"
        @mouseenter="handlePopoverMouseEnter" @mouseleave="handlePopoverMouseLeave">
        <!-- 比对参数表单 -->
        <ComparisonParamsForm ref="formRef" :hidden-submit-button="true" :default-params="defaultParams"
          :comparison-type="comparisonType" :algorithm-options="algorithmOptions"
          :portrait-group-options="portraitGroupOptions" :require-portrait-first="requirePortraitFirst"
          @comparison-change="comparisonChange" />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getAlgorithms } from '@/api/algorithm';
import { getAllPortraitGroup } from '@/api/portrait';
import { cloneDeep, isEqual } from 'lodash';

import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';
import ComparisonParamsForm from '@/components/Comparison/ComparisonParamsForm/index.vue';

const props = defineProps({
  // 新增props控制分离模式
  separated: {
    type: Boolean,
    default: false
  },
  // 分离模式下悬浮框的水平偏移量
  separatedOffsetX: {
    type: String,
    default: '-200px'
  },
  buttonText: {
    type: String,
    default: "",
  },
  placement: {
    // 弹窗的位置
    type: String,
    default: 'top',
    validator: (value) => ['top', 'bottom'].includes(value),
  },
  comparisonType: {
    // 模式
    type: String,
    default: 'image-to-library',
    validator: (value) =>
      ['image-to-library', 'image-to-image'].includes(value),
  },
  defaultValues: {
    type: Object,
    default: () => ({}),
  },
  // 优先选择人像组
  requirePortraitFirst: {
    type: Boolean,
    default: false,
  },
  // 根据传入的这个用来限制人像组内部有哪些选项是不可以用的
  usedPortraitLibraryGroupInfos: {
    type: Array,
    default: () => [],
  },
});

const buttonHovered = ref(false);
const popoverHovered = ref(false);

// 延时隐藏的定时器
let hideTimer = null;

const showPopover = computed(() => buttonHovered.value || popoverHovered.value);

// 清楚定时器
const clearTimer = () => {
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }
};

// 重置定时器
const resetTimer = () => {
  clearTimer();
  hideTimer = setTimeout(() => {
    buttonHovered.value = false;
    popoverHovered.value = false;
  }, 200);
};

const handleButtonMouseEnter = () => {
  clearTimer();
  // 分离模式下，如果悬浮框已显示，再次悬停按钮则关闭悬浮框
  if (props.separated && showPopover.value) {
    buttonHovered.value = false;
    popoverHovered.value = false;
  } else {
    buttonHovered.value = true;
  }
};

const handleButtonMouseLeave = () => {
  if (!props.separated || !popoverHovered.value) {
    resetTimer();
  }
};

const handlePopoverMouseEnter = () => {
  clearTimer();
  popoverHovered.value = true;
};

const handlePopoverMouseLeave = () => {
  resetTimer();
};

const formRef = ref(null);

const emits = defineEmits(['submit-success', 'comparisonChange']);

const handleConfirm = () => {
  if (formRef.value && typeof formRef.value.getFormData === 'function') {
    const currentFormData = formRef.value.getFormData();
    const currentFormDataCopy = cloneDeep(currentFormData)
    const isFormChanged = !isEqual(currentFormDataCopy, defaultParams.value)
    if (isFormChanged) {
      currentFormDataCopy.strategyId = ''
    }
    currentFormDataCopy.portraitLibraryIds = [currentFormDataCopy.portraitLibraryId]
    console.log('当前表单数据：', currentFormDataCopy);

    emits('submit-success', currentFormDataCopy);
  }
};

const requirePortraitFirstDisable = computed(() => {
  if (formRef.value && formRef.value?.formData?.portraitLibraryIds) {
    return !formRef.value?.formData?.portraitLibraryIds?.length > 0;
  }
  return true;
});

const defaultParams = computed(() => {
  let item = {
    ...props.defaultValues,
    portraitLibraryIds: props.defaultValues?.portraitLibraryGroupId
      ? [props.defaultValues.portraitLibraryGroupId]
      : [],
  };
  delete item?.portraitLibraryGroupId
  return item
});



const algorithmOptions = ref([]); // 算法列表可选项
const portraitGroupOptions = ref([]); // 人像组列表可选项

const fetchAlgorithms = async () => {
  try {
    const response = await getAlgorithms();
    algorithmOptions.value = response.data.map(item => ({
      value: item.code,
      label: item.name,
    }));
  } catch (error) {
    console.error('获取算法列表失败', error);
  }
};

const fetchPortraitGroupList = async () => {
  try {
    const response = await getAllPortraitGroup();
    const cascaderData = transformData(response.data);

    portraitGroupOptions.value = cascaderData;
  } catch (error) {
    console.error('查询人像组列表失败', error);
  }
};

// 处理数据请求
onMounted(() => {
  fetchAlgorithms();
  fetchPortraitGroupList();
});

const usedPortraitLibraryIds = computed(() => {
  return props.usedPortraitLibraryGroupInfos.map((item) => {
    return item?.libraryGroupId;
  });
});

// 转换人像库数据到 a-cascader 上
const transformData = (data) => {
  return data.map((item) => {
    const newItem = {
      ...item,
      value: item.id,
      label: item.name,
      disabled: item.status ? item.status === 'disable' : false,
    };

    // 如果有传usedPortraitLibraryGroupInfos，就把里面对应的newItem改为disabled且加上（已比对）
    if (usedPortraitLibraryIds.value.includes(item.id)) {
      newItem.label = newItem.label + '（已比对）';
      newItem.disabled = true;
    }

    // 如果存在 libraries 字段，则将其转换为 children
    if (item.libraries && Array.isArray(item.libraries)) {
      newItem.children = item.libraries.map((lib) => {
        return {
          ...lib,
          value: lib.id,
          label: lib.name,
          disabled: lib.status ? lib.status === 'disable' : false,
        };
      });

      if (newItem.children.every((child) => child.disabled)) {
        newItem.disabled = true;
      }
    }

    return newItem;
  });
};



const comparisonChange = (formdata) => {
  emits('comparisonChange', formdata)
}


</script>

<style scoped lang="scss">
.container {
  position: relative;
  display: inline-block;
  z-index: 4;
}

.popover {
  position: absolute;
  width: 336px;
  background: var(--color-primary-bg-image);
  border-radius: 8px;
  box-shadow: 5px 10px 10px rgba(0, 0, 0, 0.3);
  z-index: 999;

  // 原有居中定位
  &:not(.separated) {
    left: calc(50%);
    transform: translateX(-50%);
  }

  // 分离模式定位
  &.separated {
    left: v-bind('props.separatedOffsetX');
  }
}

.top {
  bottom: 100%;
}

.bottom {
  top: 100%;
  height: fit-content;
}

// 新增分离模式动画
.separated {
  transition: opacity 0.2s, transform 0.2s;

  &.fade-enter-from,
  &.fade-leave-to {
    opacity: 0;
    transform: translateX(-10px);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
