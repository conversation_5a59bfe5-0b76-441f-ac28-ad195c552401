<!--
 * @Author: yuzhouisme
 * @Date: 2025-03-17 
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 16:07:15
 * @FilePath: \platform-face-web\src\components\Common\FaceCanvas\index.vue
 * @Description:人脸框绘制和展示组件
-->
<template>
  <div ref="faceCanvasContainer" class="face-canvas-container">
    <canvas ref="faceCanvas" @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp" @wheel.prevent="handleWheel">
    </canvas>
    <canvas ref="cropCanvas" style="display: none">
    </canvas>
    <!-- 已有人脸框 -->
    <FaceBox v-for="(face, index) in faceBoxes" :key="face.key" :face="face" :is-selected="isBoxSelected(face.key)"
      :scale="scale" :offset="offset" :is-drawing="isDrawing" :index="index + 1" :custom-style="customFaceBoxStyle"
      @click="onBoxClick" />

    <!-- 手动框选的人脸框 -->
    <FaceBox v-for="face in manualFaceBoxes" :key="face.key" :face="face" :is-selected="isBoxSelected(face.key)"
      :scale="scale" :offset="offset" :is-drawing="isDrawing" :show-index="false" :custom-style="customFaceBoxStyle"
      @click="onBoxClick" />

    <!-- 绘制中的临时选框 -->
    <FaceBox v-if="isDrawing" :face="currentRect" :is-new="true" :is-drawing="true" :show-check-icon="false"
      :show-index="false" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useCanvasDraw } from './composables/useCanvasDraw';
import { useZoomPan } from './composables/useZoomPan';
import { useCanvasRenderer } from './composables/useCanvasRenderer';
import { useFaceCanvasLogic } from './composables/useFaceCanvasLogic';
import { message } from 'ant-design-vue';
import FaceBox from './components/FaceBox.vue';

/**
 * FaceCanvas组件属性定义
 */
const props = defineProps({
  /** 图像源地址 */
  imageSrc: {
    type: String,
    required: true,
  },
  /** 已检测到的人脸框数组 */
  faceBoxes: {
    type: Array,
    default: () => [],
  },
  /** 手动绘制的人脸框数组 */
  manualFaceBoxes: {
    type: Array,
    default: () => [],
  },
  /** 当前选中的人脸框数组 */
  selectedItems: {
    type: Array,
    default: () => [],
  },
  /** 是否可编辑（允许绘制和选择） */
  editable: {
    type: Boolean,
    default: true,
  },
  /** 自定义人脸框样式配置 */
  customFaceBoxStyle: {
    type: Object,
    default: () => null,
    validator: (value) => {
      if (!value) return true;
      // 验证自定义样式对象的结构
      const validKeys = ['borderColor', 'borderWidth', 'borderStyle', 'showSelection', 'showIndex', 'backgroundColor'];
      return Object.keys(value).every(key => validKeys.includes(key));
    }
  }
})

/**
 * FaceCanvas组件事件定义
 * @event boxDrawn - 绘制完成人脸框时触发
 * @event boxSelected - 选择人脸框时触发
 */
const emit = defineEmits(['boxDrawn', 'boxSelected'])

const internalSelected = ref([]);

watch(
  () => props.selectedItems,
  (newVal) => {
    internalSelected.value = [...newVal];
  },
  { immediate: true, deep: true }
);

// DOM引用
const faceCanvasContainer = ref(null);
const faceCanvas = ref(null);
const cropCanvas = ref(null);

// 引入绘制和缩放逻辑
const { isDrawing, currentRect, onMouseDown, onMouseMove, onMouseUp } = useCanvasDraw()
const { scale, offset, isPanning, onMouseDown: onPanStart, onMouseMove: onPanMove, onMouseUp: onPanEnd, setZoomPanState } = useZoomPan()
const { image } = useCanvasRenderer()

// 引入FaceCanvas核心逻辑
const {
  imageLoadError,
  imageLoading,
  createAdjustCanvasSize,
  createLoadImage,
  createRedraw,
  createImageProcessing,
  convertToOriginalCoordinates,
  cleanup
} = useFaceCanvasLogic();

/**
 * 检查人脸框是否被选中
 * @param {string} key - 人脸框的唯一标识
 * @returns {boolean} 是否被选中
 */
const isBoxSelected = (key) => {
  return internalSelected.value.findIndex((f) => f.key === key) > -1;
};

// 创建核心函数实例
const refs = { faceCanvasContainer, faceCanvas, cropCanvas, image };
const state = { scale, offset };

const { redraw, debouncedRedraw } = createRedraw(refs, state);
const adjustCanvasSize = createAdjustCanvasSize(refs, setZoomPanState, redraw);
const { loadImage, debouncedAdjustCanvasSize } = createLoadImage(refs, adjustCanvasSize);
const { cropCurrentRect, validateDrawnRegion } = createImageProcessing(refs);

onMounted(() => {
  faceCanvasContainer.value.addEventListener('wheel', handleWheel, { passive: false });
});

onUnmounted(() => {
  // 清理FaceCanvas相关资源
  cleanup(refs, debouncedAdjustCanvasSize);

  // 清理wheel事件监听器
  if (faceCanvasContainer.value) {
    faceCanvasContainer.value.removeEventListener('wheel', handleWheel);
  }
});

watch(() => props.imageSrc, (newVal) => {
  if (newVal) {
    loadImage(newVal);
  }
}, { immediate: true });

/**
 * 处理鼠标滚轮事件（缩放功能暂时禁用）
 * @param {WheelEvent} e - 滚轮事件对象
 */
const handleWheel = (e) => {
  if (!props.editable) return;
  // 处理滚轮事件（暂时禁用）
  // onWheel(e);
  // redraw();
};

/**
 * 处理鼠标按下事件
 * @param {MouseEvent} e - 鼠标事件对象
 */
const handleMouseDown = (e) => {
  if (!props.editable) return;

  if (e.shiftKey) {
    // Shift+鼠标：处理平移
    onPanStart(e);
  } else {
    // 普通鼠标：处理绘制
    onMouseDown(e);
  }
};

/**
 * 处理鼠标移动事件
 * @param {MouseEvent} e - 鼠标事件对象
 */
const handleMouseMove = (e) => {
  if (!props.editable) return;

  if (isPanning.value) {
    onPanMove(e);
    // 使用防抖重绘提升性能
    debouncedRedraw();
  } else {
    onMouseMove(e);
  }
};

/**
 * 处理鼠标松开事件
 * @param {MouseEvent} e - 鼠标事件对象
 */
const handleMouseUp = (e) => {
  if (!props.editable) return;

  if (isPanning.value) {
    onPanEnd();
  } else {
    const newFace = onMouseUp(e);
    if (newFace) {
      // 转换为原始图像坐标
      const newFaceOriginal = convertToOriginalCoordinates(newFace, state);

      // 验证绘制区域
      const containedFaces = validateDrawnRegion(newFaceOriginal, props.faceBoxes);
      if (containedFaces.length === 0) {
        message.error("选区不包含任何人脸，请重新绘制！");
        return;
      } else if (containedFaces.length > 1) {
        message.error("选区包含多个脸部，请重新绘制！");
        return;
      }

      // 裁剪图像并发射事件
      const croppedImageData = cropCurrentRect(newFaceOriginal);
      emit('boxDrawn', { face: newFaceOriginal, croppedImage: croppedImageData });
    }
  }
};

/**
 * 处理人脸框点击事件
 * @param {string} key - 人脸框的唯一标识
 */
const onBoxClick = (key) => {
  if (!props.editable) return;
  if (isDrawing.value) return;

  emit('boxSelected', key)
};

// 图标尺寸相关计算
const baseIconSize = 12;

/**
 * 计算图标尺寸
 * @returns {number} 图标尺寸
 */
const iconSize = computed(() => {
  // 当前图标尺寸随着 scale 变化（暂时固定）
  // return baseIconSize * scale.value;
  return baseIconSize;
});

/**
 * 计算图标底部偏移
 * @returns {string} CSS底部偏移值
 */
const iconBottom = computed(() => {
  return `-${iconSize.value + 4}px`
})

/**
 * 计算序号底部偏移
 * @returns {string} CSS底部偏移值
 */
const iconIndexBottom = computed(() => {
  return `-${iconSize.value}px`
})

</script>

<style lang="scss" scoped>
// FaceCanvas 专用颜色变量
$face-box-manual-color: #4DBFFF; // 手动绘制的颜色
$face-box-drawing-bg: rgba(77, 191, 255, 0.15); // 手动绘制时的填充色（浅蓝色）
$face-check-icon-size: 20px;
$face-index-offset: -8px;

.face-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
}

canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.face-box {
  position: absolute;
  box-sizing: border-box;
  border-radius: 8px;
}

/* 正在绘制中的临时框 */
.face-box.drawing {
  border: 1px dashed $face-box-manual-color !important;
  background-color: $face-box-drawing-bg !important;
  pointer-events: none;
}

.face-check-icon {
  position: absolute;
  right: 0;
  bottom: v-bind(iconBottom);
  width: $face-check-icon-size;
  height: $face-check-icon-size;
}

.face-index {
  position: absolute;
  right: $face-index-offset;
  bottom: v-bind(iconIndexBottom);
}
</style>