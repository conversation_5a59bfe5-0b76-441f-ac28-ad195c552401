# BaseResultFilter 通用结果过滤器组件

## 📖 组件概述

BaseResultFilter 是一个高度可配置的通用结果过滤器组件，通过配置化的方式支持不同类型的结果过滤器。该组件经过重构，采用模块化设计，提高了代码的可维护性和复用性。

## ✨ 主要功能

- 🎯 **配置化设计**：通过 config 对象完全控制组件的显示内容和行为
- 🔧 **模块化架构**：拆分为多个子组件，职责清晰，易于维护
- 🎨 **复用性强**：复用了 ComparisonParamsForm 中的 AlgorithmField 组件
- 📱 **响应式布局**：支持不同屏幕尺寸的自适应显示
- 🔄 **状态管理**：完整的表单状态管理和数据同步
- 🎪 **事件系统**：丰富的事件回调支持

## 🏗️ 组件架构

```
src/components/Comparison/BaseResultFilter/
├── index.vue                           # 主组件 (184行)
├── components/                         # 子组件目录
│   ├── FilterSections.vue             # 统计信息和自定义按钮区域 (73行)
│   ├── FormControls.vue               # 表单控件区域 (95行)
│   └── TrapezoidSection.vue           # 梯形区域 (95行)
├── composables/                       # 逻辑复用
│   ├── useFilterForm.js               # 表单数据管理 (78行)
│   ├── useAlgorithmOptions.js         # 算法选项计算 (30行)
│   └── useStatsData.js                # 统计数据处理 (18行)
├── styles/
│   └── filter-styles.scss             # 共享样式文件 (95行)
├── resultFilterConfigs.js             # 配置文件
└── README.md                          # 组件文档
```

## 📋 Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| config | Object | {} | 组件配置对象，控制各区域的显示和行为 |
| defaultParams | Object | 见下方 | 默认表单参数 |
| portraitGroupOptions | Array | [] | 人像组选项列表 |
| statsData | Object | {} | 统计数据对象 |

### defaultParams 默认值
```javascript
{
  portraitGroupId: '',
  algorithmCodes: ['ci_an'],
  numberOfResults: 50,
  comparisonThreshold: 0.7,
}
```

## 🎛️ Config 配置说明

### 左侧标题区域 (leftSection)
```javascript
leftSection: {
  icon: 'portrait-icon',        // 图标名称
  title: '张三',                // 标题文本
  text: '附加文本'              // 附加文本
}
```

### 梯形区域 (trapezoidSection)
```javascript
trapezoidSection: {
  bgColor: '#f0f0f0',          // 背景色
  showPortraitGroup: true,      // 显示人像组选择
  portraitGroupLabel: '人像组', // 人像组标签
  customButton: {              // 自定义按钮配置
    buttonBg: '#24394e',
    hoverBg: '#2a4a5f',
    fontColor: '#ffffff',
    hoverFontColor: '#4dbfff',
    iconName: 'custom-icon',
    text: '按钮文本'
  }
}
```

### 统计信息区域 (statsSection)
```javascript
statsSection: {
  items: [
    { 
      label: '入库照片', 
      key: 'stockPhotosNum',     // 对应 statsData 中的键
      defaultValue: 0            // 默认值
    },
    // ... 更多统计项
  ]
}
```

### 自定义按钮区域 (customButtons)
```javascript
customButtons: [
  {
    buttonBg: '#24394e',
    hoverBg: '#2a4a5f',
    fontColor: '#ffffff',
    hoverFontColor: '#4dbfff',
    iconName: 'button-icon',
    text: '按钮文本'
  }
]
```

### 功能开关
```javascript
{
  showAlgorithmSelect: true,     // 显示算法选择
  algorithmLabel: '比对算法',    // 算法选择标签
  showResultsSelect: true,       // 显示返回结果数选择
  resultsLabel: '返回结果',      // 返回结果标签
  showThresholdSelect: true,     // 显示阈值选择
  thresholdLabel: '设定阈值',    // 阈值选择标签
  showFilterButton: true         // 显示过滤按钮
}
```

## 🚀 使用方式

### 1. 基础用法

```vue
<template>
  <BaseResultFilter
    :config="filterConfig"
    :default-params="defaultParams"
    :portrait-group-options="portraitGroups"
    :stats-data="statsData"
    @submit-success="handleSubmit" />
</template>

<script setup>
import BaseResultFilter from '@/components/Comparison/BaseResultFilter/index.vue';

const filterConfig = {
  leftSection: {
    icon: 'portrait-icon',
    title: '张三'
  },
  showAlgorithmSelect: true,
  showResultsSelect: true,
  showThresholdSelect: true
};

const defaultParams = {
  portraitGroupId: '',
  algorithmCodes: ['ci_an'],
  numberOfResults: 50,
  comparisonThreshold: 0.7
};

const handleSubmit = (data) => {
  console.log('表单数据:', data.params);
  console.log('是否变更:', data.changed);
};
</script>
```

### 2. 使用预设配置

```vue
<template>
  <BaseResultFilter
    :config="moreScoresConfig"
    :portrait-group-options="portraitGroups"
    :stats-data="statsData"
    @submit-success="handleSubmit" />
</template>

<script setup>
import BaseResultFilter from '@/components/Comparison/BaseResultFilter/index.vue';
import { moreScoresConfig } from '@/components/Comparison/BaseResultFilter/resultFilterConfigs.js';
</script>
```

## 📤 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| submit-success | { params, changed } | 表单数据变化时触发 |

### submit-success 事件参数
```javascript
{
  params: {
    portraitGroupId: '',
    algorithmCodes: ['ci_an'],
    numberOfResults: 50,
    comparisonThreshold: 0.7
  },
  changed: true  // 表单是否发生变化
}
```

## 🔧 暴露的方法

通过 ref 可以访问以下方法：

```javascript
const filterRef = ref();

// 获取当前表单数据
const formData = filterRef.value.getFormData();

// 直接访问响应式表单数据
const reactiveFormData = filterRef.value.formData;
```

## 💡 最佳实践

1. **合理使用配置**：根据实际需求配置显示的功能模块，避免不必要的复杂性
2. **统一样式管理**：使用共享样式文件确保样式一致性
3. **事件处理**：及时处理 submit-success 事件，保持数据同步
4. **性能优化**：大量数据时考虑使用虚拟滚动或分页
5. **可访问性**：确保组件支持键盘导航和屏幕阅读器

## 🔄 重构说明

### 重构前后对比
- **重构前**：单一组件 430 行，逻辑复杂，难以维护
- **重构后**：模块化设计，主组件 184 行，职责清晰

### 主要改进
1. **组件拆分**：按功能拆分为多个子组件
2. **逻辑提取**：使用 composables 提取可复用逻辑
3. **组件复用**：复用 AlgorithmField 组件，减少代码重复
4. **样式优化**：提取共享样式，提高一致性

### 迁移指南
原有的 API 保持不变，可以无缝替换使用。

## 🐛 常见问题

### Q: 如何自定义算法选项？
A: 通过 portraitGroupOptions 或 defaultParams.algorithmCodes 配置算法选项。

### Q: 如何监听表单数据变化？
A: 监听 submit-success 事件，该事件会在表单数据变化时自动触发。

### Q: 如何自定义样式？
A: 可以通过覆盖 CSS 变量或使用深度选择器自定义样式。

## 📝 更新日志

### v2.0.0 (2025-07-23)
- 🎉 重构组件架构，采用模块化设计
- ✨ 复用 AlgorithmField 组件
- 🔧 提取 composables 逻辑
- 📚 完善文档和类型定义
- 🎨 优化样式结构
