# LoadMoreList 加载更多列表组件

## 📖 组件概述

LoadMoreList 是一个通用的列表组件，支持多种布局模式（垂直、水平、网格）和加载更多功能。通过插槽机制提供了极高的灵活性，可以适配各种不同的列表项内容，支持滚动加载和点击加载两种模式。

## ✨ 主要功能

- 📋 **多种布局**：支持垂直、水平、网格三种布局模式
- 🔄 **加载更多**：支持滚动加载和点击加载两种方式
- 🎯 **插槽机制**：通过插槽自定义列表项内容
- 🎨 **选中状态**：支持列表项选中状态管理
- 📱 **响应式**：完全支持Vue 3的响应式系统
- 🎪 **双向绑定**：使用v-model进行选中项绑定
- 🔧 **灵活配置**：支持自定义间距、列数、样式等

## 🏗️ 组件结构

```
src/components/Common/LoadMoreList/
├── index.vue                    # 主组件 (267行)
└── README.md                    # 组件文档
```

## 📋 Props 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| data | Array | - | ✅ | 数据列表 |
| hasMore | Boolean | false | ❌ | 是否还有更多数据 |
| selectedId | String/Number | null | ❌ | 外部传入的选中项ID |
| loading | Boolean | false | ❌ | 加载状态 |
| customSelectedClass | String | '' | ❌ | 自定义选中项样式类 |
| handleClickInParent | Boolean | true | ❌ | 是否由父组件处理点击事件 |
| keyName | String | 'id' | ❌ | 用于唯一标识列表项的字段名 |
| layoutType | String | 'vertical' | ❌ | 布局类型：vertical/horizontal/grid |
| gapSize | String | '16px' | ❌ | 网格布局时的间距 |
| gridColumns | Number | 3 | ❌ | 网格布局时的列数 |
| scrollLoad | Boolean | true | ❌ | 是否使用滚动加载 |
| showBottomTitle | Boolean | true | ❌ | 是否展示底部标题 |
| verticalGap | String | '10px' | ❌ | 垂直布局时的间距 |

### Props 详细说明

#### layoutType
- **可选值**: `'vertical'` | `'horizontal'` | `'grid'`
- **说明**: 
  - `vertical`: 垂直列表，适合常规列表展示
  - `horizontal`: 水平列表，适合横向滚动展示
  - `grid`: 网格布局，适合卡片式展示

#### scrollLoad
- **说明**: 
  - `true`: 滚动到底部自动加载更多（适用于垂直和网格布局）
  - `false`: 点击"加载更多"按钮加载（适用于水平布局）

## 🎪 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| loadMore | - | 需要加载更多数据时触发 |
| update:selectedId | item | 选中项变化时触发，用于v-model双向绑定 |

## 🎨 插槽 Slots

### default 默认插槽

**作用域插槽参数**:
- `item`: 当前列表项的数据
- `isSelected`: 当前项是否被选中
- `index`: 当前项的索引（不包含first插槽）

### first 第一项专属插槽

**作用域插槽参数**:
- `item`: 第一项的数据
- `isSelected`: 第一项是否被选中

**使用场景**: 当第一项需要特殊样式或布局时使用

## 🚀 使用方式

### 1. 基础垂直列表

```vue
<template>
  <LoadMoreList
    :data="listData"
    :has-more="hasMore"
    :loading="loading"
    v-model:selected-id="selectedId"
    @load-more="handleLoadMore">
    <template #default="{ item, isSelected, index }">
      <div class="list-item" :class="{ selected: isSelected }">
        <img :src="item.avatar" alt="头像" />
        <span>{{ item.name }}</span>
      </div>
    </template>
  </LoadMoreList>
</template>

<script setup>
import { ref } from 'vue';
import LoadMoreList from '@/components/Common/LoadMoreList/index.vue';

const listData = ref([
  { id: 1, name: '张三', avatar: '/avatar1.jpg' },
  { id: 2, name: '李四', avatar: '/avatar2.jpg' }
]);
const hasMore = ref(true);
const loading = ref(false);
const selectedId = ref(null);

const handleLoadMore = async () => {
  loading.value = true;
  // 模拟加载数据
  setTimeout(() => {
    listData.value.push(
      { id: Date.now(), name: '新用户', avatar: '/default.jpg' }
    );
    loading.value = false;
    hasMore.value = listData.value.length < 50; // 假设最多50条
  }, 1000);
};
</script>
```

### 2. 网格布局

```vue
<template>
  <LoadMoreList
    :data="gridData"
    :has-more="hasMore"
    :loading="loading"
    layout-type="grid"
    :grid-columns="4"
    gap-size="20px"
    @load-more="handleLoadMore">
    <template #default="{ item, isSelected }">
      <div class="grid-item" :class="{ selected: isSelected }">
        <img :src="item.image" alt="图片" />
        <p>{{ item.title }}</p>
      </div>
    </template>
  </LoadMoreList>
</template>
```

### 3. 水平列表（点击加载）

```vue
<template>
  <LoadMoreList
    :data="horizontalData"
    :has-more="hasMore"
    :loading="loading"
    layout-type="horizontal"
    :scroll-load="false"
    @load-more="handleLoadMore">
    <template #default="{ item, isSelected }">
      <div class="horizontal-item" :class="{ selected: isSelected }">
        <img :src="item.thumbnail" alt="缩略图" />
        <span>{{ item.label }}</span>
      </div>
    </template>
  </LoadMoreList>
</template>
```

### 4. 使用第一项专属插槽

```vue
<template>
  <LoadMoreList
    :data="listData"
    :has-more="hasMore"
    @load-more="handleLoadMore">
    <!-- 第一项特殊样式 -->
    <template #first="{ item, isSelected }">
      <div class="first-item special-style" :class="{ selected: isSelected }">
        <div class="featured-badge">推荐</div>
        <img :src="item.avatar" alt="头像" />
        <h3>{{ item.name }}</h3>
        <p>{{ item.description }}</p>
      </div>
    </template>
    
    <!-- 其他项常规样式 -->
    <template #default="{ item, isSelected, index }">
      <div class="regular-item" :class="{ selected: isSelected }">
        <img :src="item.avatar" alt="头像" />
        <span>{{ item.name }}</span>
      </div>
    </template>
  </LoadMoreList>
</template>
```

### 5. 自定义选中样式

```vue
<template>
  <LoadMoreList
    :data="listData"
    :selected-id="selectedId"
    custom-selected-class="my-selected-style"
    @update:selected-id="handleSelect">
    <template #default="{ item, isSelected }">
      <div class="item">{{ item.name }}</div>
    </template>
  </LoadMoreList>
</template>

<style scoped>
.my-selected-style {
  background-color: #1890ff;
  color: white;
  border-radius: 4px;
}
</style>
```

## 🎯 核心方法

### handleScroll(event)
处理滚动事件，当滚动到底部时触发加载更多。

### handleClick(item, event)
处理列表项点击事件，管理选中状态。

### loadMore()
触发加载更多数据的方法。

## 🎨 样式定制

组件提供了三种布局的基础样式：

```scss
// 垂直列表
.vertical-list {
  display: flex;
  flex-direction: column;
  gap: var(--vertical-gap);
  overflow-y: auto;
}

// 水平列表
.horizontal-list {
  display: flex;
  flex-direction: row;
  gap: 32px;
  overflow-x: auto;
}

// 网格列表
.grid-list {
  display: grid;
  grid-template-columns: repeat(var(--grid-columns), 1fr);
  gap: var(--gap-size);
  overflow: auto;
}
```

## 🔧 暴露的方法和属性

```javascript
defineExpose({ 
  listContainer // 列表容器的DOM引用
});
```

## ⚠️ 注意事项

1. **数据格式**：确保数据数组中每个对象都包含keyName指定的唯一标识字段
2. **布局选择**：根据使用场景选择合适的布局类型
3. **加载模式**：垂直和网格布局建议使用滚动加载，水平布局建议使用点击加载
4. **性能优化**：大量数据时考虑虚拟滚动或分页加载
5. **响应式**：网格布局在小屏幕上可能需要调整列数

## 🚀 扩展建议

1. **虚拟滚动**：支持大量数据的虚拟滚动
2. **拖拽排序**：支持列表项的拖拽重排
3. **多选模式**：支持多选功能
4. **搜索过滤**：集成搜索和过滤功能
5. **动画效果**：为加载和选中状态添加过渡动画
