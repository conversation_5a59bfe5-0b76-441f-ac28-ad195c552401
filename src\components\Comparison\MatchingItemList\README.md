# MatchingItemList 匹配结果列表组件

## 📖 组件概述

MatchingItemList 是一个专门用于展示人脸比对匹配结果的水平滚动列表组件。它以卡片形式展示匹配到的人像信息，包括人像图片、匹配得分、排名、姓名等关键信息，支持选中状态管理和交互操作，是人脸识别比对系统中的核心展示组件。

## ✨ 主要功能

- 🖼️ **人像展示**：基于 AvatarImage 组件展示人像图片，支持人脸智能居中显示
- 🏆 **排名得分**：显示匹配排名（TOP N）和算法得分信息
- 📊 **多算法支持**：支持显示多种算法的得分结果
- 📝 **文本匹配**：支持文本信息匹配度展示和详情查看
- 🎯 **选中管理**：支持单选模式，提供选中状态视觉反馈
- 🔄 **滚动定位**：提供 scrollToItem 方法支持程序化滚动定位
- 🎨 **交互操作**：支持人像库详情查看、更多比分查看等操作

## 🏗️ 组件结构

```
MatchingItemList
├── LoadMoreList (水平布局容器)
│   └── 匹配项卡片
│       ├── AvatarImage (人像图片)
│       │   ├── statusTopLeft (排名和得分)
│       │   └── statusTopRight (文本匹配度)
│       └── 底部信息区
│           ├── 姓名和操作按钮
│           └── 其他算法得分 (可选)
```

## 📋 Props 参数

### 必需参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `data` | Array | - | 匹配结果数据列表（必需） |

### 可选参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showOtherScore` | Boolean | true | 是否显示其他算法的分数 |
| `algorithmCode` | String | - | 当前使用的算法代码 |

## 📤 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `matchingBtnClick` | (type: string, item: object) | 匹配按钮点击事件，type 为操作类型，item 为当前项数据 |

## 🎯 暴露方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| `scrollToItem` | (id: string) | 滚动到指定 ID 的匹配项并选中 |

## 📊 数据结构

### data 数组项结构

```javascript
{
  // 人像基本信息
  portrait: {
    portraitId: 'string',           // 人像ID（用作唯一标识）
    xm: 'string',                   // 姓名
    standardPortraitImages: [{
      imageId: 'string',            // 图片ID
      coordinate: [x, y, w, h]      // 人脸坐标 [左上角x, 左上角y, 宽度, 高度]
    }]
  },
  
  // 匹配得分信息
  score: number,                    // 主算法得分
  scoreRank: number,                // 排名
  
  // 文本匹配信息（可选）
  infoMatching: {
    score: number,                  // 文本匹配得分
    items: [{
      type: 'string',               // 匹配字段类型
      inputInfo: 'string',          // 输入信息
      portraitInfo: 'string',       // 人像库信息
      matchProbability: number      // 匹配概率 (0-1)
    }]
  },
  
  // 附加算法得分（可选）
  attachedAlgorithmCodes: [{
    algorithmCode: 'string',        // 算法代码
    score: number                   // 得分
  }]
}
```

## 🚀 使用方式

### 1. 基础用法

```vue
<template>
  <MatchingItemList 
    :data="matchingResults" 
    @matching-btn-click="handleMatchingBtnClick" />
</template>

<script setup>
import MatchingItemList from '@/components/Comparison/MatchingItemList/index.vue';

const matchingResults = [
  {
    portrait: {
      portraitId: '12345',
      xm: '张三',
      standardPortraitImages: [{
        imageId: 'img-001',
        coordinate: [100, 50, 200, 250]
      }]
    },
    score: 0.8567,
    scoreRank: 1
  }
  // ... 更多匹配结果
];

const handleMatchingBtnClick = (type, item) => {
  if (type === 'moreScore') {
    // 处理查看更多得分逻辑
    console.log('查看更多得分:', item);
  }
};
</script>
```

### 2. 隐藏其他算法得分

```vue
<template>
  <MatchingItemList 
    :data="matchingResults" 
    :show-other-score="false"
    algorithm-code="ci_an" />
</template>
```

### 3. 程序化滚动定位

```vue
<template>
  <MatchingItemList 
    ref="matchingListRef"
    :data="matchingResults" />
  <button @click="scrollToTarget">滚动到目标项</button>
</template>

<script setup>
import { ref } from 'vue';

const matchingListRef = ref();

const scrollToTarget = () => {
  // 滚动到指定 portraitId 的匹配项
  matchingListRef.value.scrollToItem('target-portrait-id');
};
</script>
```

## 🎨 样式特性

### 选中状态样式
- 选中项会显示蓝色阴影边框 (`box-shadow: 0 3px 6px #4975AC`)
- 人像图片边框颜色会变为选中色 (`#4975AC`)

### 布局特点
- 采用水平滚动布局，每个匹配项宽度固定为 404px
- 项目间距为 24px
- 支持平滑滚动定位

## 🔧 依赖组件

- **LoadMoreList**: 提供水平滚动列表容器
- **AvatarImage**: 展示人像图片，支持人脸居中和状态徽章
- **AlgorithmScoreItem**: 展示算法得分信息
- **CustomButtonWithTooltip**: 提供带提示的操作按钮
- **Divider**: 提供分割线组件

## 💡 使用建议

1. **数据完整性**: 确保传入的 data 数组中每个项目都包含必需的 portrait 信息
2. **图片加载**: 建议预处理图片 URL，确保图片能正常加载显示
3. **交互反馈**: 合理处理 matchingBtnClick 事件，提供良好的用户交互体验
4. **性能优化**: 对于大量数据，考虑实现虚拟滚动或分页加载
5. **响应式设计**: 在不同屏幕尺寸下测试组件的显示效果

## 🎯 应用场景

- 人脸识别比对结果展示
- 人像库搜索结果列表
- 算法性能对比展示
- 身份验证匹配结果
- 安防监控人员识别
