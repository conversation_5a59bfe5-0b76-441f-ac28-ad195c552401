/**
 * 背景元素渲染器 - 负责渲染各种背景元素
 * 支持参考圆、虚线圆、区域标记等多种背景元素
 */

/**
 * 参考圆渲染器
 */
export class ReferenceCircleRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染参考圆
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} centerNode - 中心节点
     * @param {Object} innerNode - 内圈参考节点
     * @param {Object} outerNode - 外圈参考节点
     */
    render(container, centerNode, innerNode, outerNode) {
        const config = this.configManager.get('background.referenceCircles');

        if (!config.enabled) {
            return [];
        }

        const circles = [];
        const style = config.style;

        // 计算半径
        const innerRadius = innerNode ? innerNode.layoutRadius : 0;
        const outerRadius = outerNode ? outerNode.layoutRadius : 0;

        // 渲染内圈
        if (innerRadius > 0) {
            const innerCircle = container
                .append('circle')
                .attr('r', innerRadius)
                .attr('fill', style.fill || 'none')
                .attr('stroke', style.stroke || '#8593A7')
                .attr('stroke-width', style.strokeWidth || 1)
                .attr('class', 'reference-circle inner');

            circles.push(innerCircle);
        }

        // 渲染外圈
        if (outerRadius > 0 && outerRadius !== innerRadius) {
            const outerCircle = container
                .append('circle')
                .attr('r', outerRadius)
                .attr('fill', style.fill || 'none')
                .attr('stroke', style.stroke || '#8593A7')
                .attr('stroke-width', style.strokeWidth || 1)
                .attr('class', 'reference-circle outer');

            circles.push(outerCircle);
        }

        return circles;
    }
}

/**
 * 虚线圆渲染器
 */
export class DashedCircleRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染虚线圆
     * @param {d3.Selection} container - D3容器选择器
     * @param {Array} nodes - 节点数组
     */
    render(container, nodes) {
        const config = this.configManager.get('background.dashedCircle');

        if (!config.enabled || !nodes || nodes.length === 0) {
            return null;
        }

        const firstNode = nodes[0];
        if (!firstNode.layoutRadius || !firstNode.visualRadius) {
            return null;
        }

        const dashedRadius = firstNode.layoutRadius - firstNode.visualRadius;

        if (dashedRadius <= 0) {
            return null;
        }

        const style = config.style;

        return container
            .append('circle')
            .attr('r', dashedRadius)
            .attr('fill', style.fill || 'none')
            .attr('stroke', style.stroke || '#8593A7')
            .attr('stroke-dasharray', style.strokeDasharray || '5,5')
            .attr('stroke-width', style.strokeWidth || 1)
            .attr('class', 'dashed-circle');
    }
}

/**
 * 区域标记渲染器
 */
export class ZoneMarkerRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染区域标记
     * @param {d3.Selection} container - D3容器选择器
     * @param {Array} zones - 区域配置数组
     */
    render(container, zones = []) {
        const config = this.configManager.get('background.zoneMarkers');

        if (!config || !config.enabled || !zones.length) {
            return [];
        }

        const markers = [];

        zones.forEach((zone, index) => {
            const marker = this.renderZone(container, zone, index);
            if (marker) {
                markers.push(marker);
            }
        });

        return markers;
    }

    /**
     * 渲染单个区域
     */
    renderZone(container, zone, index) {
        const { type, radius, color, label, opacity = 0.1 } = zone;

        let marker;

        switch (type) {
            case 'circle':
                marker = container
                    .append('circle')
                    .attr('r', radius)
                    .attr('fill', color)
                    .attr('fill-opacity', opacity)
                    .attr('stroke', color)
                    .attr('stroke-width', 1)
                    .attr('class', `zone-marker zone-${index}`);
                break;

            case 'ring':
                const { innerRadius, outerRadius } = zone;
                marker = container
                    .append('circle')
                    .attr('r', outerRadius)
                    .attr('fill', 'none')
                    .attr('stroke', color)
                    .attr('stroke-width', outerRadius - innerRadius)
                    .attr('stroke-opacity', opacity)
                    .attr('class', `zone-marker zone-${index}`);
                break;

            default:
                return null;
        }

        // 添加标签
        if (label && marker) {
            container
                .append('text')
                .attr('x', 0)
                .attr('y', -radius - 10)
                .attr('text-anchor', 'middle')
                .attr('font-size', '12px')
                .attr('fill', color)
                .attr('class', `zone-label zone-${index}`)
                .text(label);
        }

        return marker;
    }
}

/**
 * 背景渲染器工厂
 */
export class BackgroundRendererFactory {
    constructor(configManager) {
        this.configManager = configManager;
        this.renderers = {
            referenceCircles: new ReferenceCircleRenderer(configManager),
            dashedCircle: new DashedCircleRenderer(configManager),
            zoneMarkers: new ZoneMarkerRenderer(configManager),
        };
    }

    /**
     * 获取指定类型的背景渲染器
     * @param {string} type - 背景类型
     */
    getRenderer(type) {
        return this.renderers[type];
    }

    /**
     * 渲染所有启用的背景元素
     * @param {d3.Selection} container - D3容器
     * @param {Object} context - 渲染上下文
     */
    renderBackground(container, context = {}) {
        const { centerNode, nodes = [], width = 800, height = 600, zones = [] } = context;

        const backgroundElements = {};

        // 渲染区域标记
        backgroundElements.zoneMarkers = this.renderers.zoneMarkers.render(container, zones);

        // 渲染参考圆
        const innerNode = nodes.length > 2 ? nodes[2] : nodes.length > 0 ? nodes[0] : null;
        const outerNode = nodes.length > 10 ? nodes[10] : nodes.length > 0 ? nodes[nodes.length - 1] : null;
        backgroundElements.referenceCircles = this.renderers.referenceCircles.render(container, centerNode, innerNode, outerNode);

        // 渲染虚线圆
        backgroundElements.dashedCircle = this.renderers.dashedCircle.render(container, nodes);

        return backgroundElements;
    }

    /**
     * 更新配置管理器
     * @param {ConfigManager} newConfigManager - 新的配置管理器
     */
    updateConfigManager(newConfigManager) {
        this.configManager = newConfigManager;
        Object.values(this.renderers).forEach((renderer) => {
            renderer.configManager = newConfigManager;
        });
    }
}
