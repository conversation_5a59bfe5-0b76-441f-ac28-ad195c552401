const state = {
  // 排除需要缓存的组件名称
  excludeKeepAlive: [
    // 人像详情
    'PortraitRepositoryDetailPage',
    // 视频归档结果页面
    'VideoArchiveResultPage'
  ],
  setting: {
    layout: 'header-sidebar-layout'
  },
  // 菜单当前展开的路由
  openKeys: [],
  // 菜单
  menus: [],
  // 当前用户信息
  currentUser: {},
  // 【图片智能导库-TXT导库】实时推送的任务进度数据
  portraitTaskProgress: {},
  // 所有算法模型下来框选项
  algorithmModelOptions: [],
  // 所有算法模型名称，用“，”隔开
  allAlgModelNameString: '',
  // 所有人像库列表
  portraitRepositoryList: [],
  // 已被授权的人像库列表
  authorizedPortraitRepositoryList: [],
  // 用户手动上传的视频数
  uploadedVideoFileLength: 0,
  // 图片工具
  imagePluginState: {
    showImageEdit: false,
    showImageBreak: false
  },
  // 平台标签
  platformTags: {
    dataSource: [],
    pagination: { current: 0, pageSize: 200, total: 0 }
  },
  // 用户个人标签
  personalTags: {
    dataSource: [],
    pagination: { current: 0, pageSize: 200, total: 0 }
  },
  // 实时消息数据
  notices: [
    {
      id: 'bbb',
      userNoticeId: 'abc-bb',
      title: '目标人物查找命中！',
      description: '在视频中找到了，去看看吧',
      // 后面会有分类型展示
      // 消息类型： 1 - 通知， 2 - 消息， 3 - 代办
      type: 2,
      userName: '马英九',
      imageId: '4857080943171_1f14181d4c7f43eea0c3169f64e615cf',
      key: 'bbb'
    }
  ],
  // 菜单分页标签
  globalMenuTabsData: {
    activeTab: null,
    dataSource: {
      '/home': {
        path: '/home',
        meta: { title: '首页' }
      }
    }
  },
  userTags: []
};

export default state;
