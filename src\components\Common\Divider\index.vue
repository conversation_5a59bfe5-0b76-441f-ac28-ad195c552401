<!--
 * @Author: yuzhouisme
 * @Date: 2025-03-17 
 * @LastEditors: yuzhouisme
 * @LastEditTime: 2025-07-16 16:42:25
 * @FilePath: \platform-face-web\src\components\Common\Divider\index.vue
 * @Description:分割线组件
 * 是一个轻量级的分割线组件，用于在页面中创建视觉分隔，支持水平和垂直两种方向，提供灵活的样式配置。
-->
<template>
  <div :class="['divider', direction]" :style="dividerStyle"></div>
</template>
<script setup>
import { computed } from 'vue'
const props = defineProps({
  direction: {
    type: String,
    default: 'horizontal',
    validator: value => ['horizontal', 'vertical'].includes(value)
  },
  type: {
    type: String,
    default: 'solid',
    validator: value => ['solid', 'dashed'].includes(value)
  },
  color: {
    type: String,
    default: 'rgba(184, 221, 255, .12)',
  },
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: '100%',
  },
  marginSize: {
    type: String,
    default: '8px'
  }
})

const dividerStyle = computed(() => {
  const color = props.color;
  const lineWidth = '.5px'
  if (props.direction === 'horizontal') {
    return {
      borderTop: `${lineWidth} ${props.type} ${color}`,
      width: props.width,
    }
  } else {
    return {
      borderLeft: `${lineWidth} ${props.type} ${color}`,
      height: props.height,
    }
  }
})
</script>

<style scoped>
.divider {
  margin: v-bind(marginSize) 0;
}

.divider.vertical {
  margin: 0 v-bind(marginSize);
}
</style>