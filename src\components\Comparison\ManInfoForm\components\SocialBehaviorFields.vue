<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 17:37:36
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 11:26:00
 * @FilePath: \platform-face-web\src\components\Comparison\ManInfoForm\components\SocialBehaviorFields.vue
 * @Description: 社会行为字段组件
 * 包含关键标签、关联事件、关系人管理等社会行为相关字段
-->
<template>
  <div class="social-behavior-fields">
    <!-- 关键标签 -->
    <a-form-item label="关键标签">
      <TagSelector v-model="formData.tagIds" :on-search="handleSearchTags" :on-create="handleCreateTag" :max-tags="10"
        tag-type="primary" placeholder="请输入关键标签名称" @change="handleTagChange" />
    </a-form-item>

    <!-- 关联事件 -->
    <div v-for="(relatedCase, index) in formData.relatedCases" :key="index">
      <div class="inner-row">
        <a-form-item label="关联事件">
          <a-input v-model:value="relatedCase.caseName" placeholder="请输入关联事件名称" />
        </a-form-item>

        <a-form-item label="事件时间">
          <a-range-picker v-model:value="relatedCase.times" format="YYYY/MM/DD" />
        </a-form-item>

        <div class="button-container">
          <template v-if="formData.relatedCases.length > 1">
            <a class="link" @click="removeRelatedCase(index)">删除</a>
          </template>
          <template v-if="index === formData.relatedCases.length - 1">
            <a class="link" @click="addRelatedCase">新增一条</a>
          </template>
        </div>
      </div>

      <a-form-item label="事件性质">
        <a-input v-model:value="relatedCase.caseDescription" placeholder="请输入关联事件性质" />
      </a-form-item>

      <!-- 关系人管理 -->
      <div style="margin-top:8px;">
        <a-form-item label="关系人">
          <div class="d-flex align-items-center">
            <a-input v-model:value="formData.searchInvolvesPersons" placeholder="请输入关系人名称或关键信息" class="flex-1" />
            <Divider direction="vertical" height="16px" />
            <SvgIcon icon-class="add" class="cursor-pointer" @click="addInvolvedCasePeople(index)" />
          </div>
        </a-form-item>

        <!-- 关系人头像展示 -->
        <div v-if="relatedCase.involvedCasePeoples?.length" class="portrait-container agn-item">
          <div v-for="item in relatedCase.involvedCasePeoples" :key="item.imageId" class="item">
            <img :src="getImageWebURL(item.imageId)" />
            <div class="name agn-item">
              <a-tooltip placement="top" :title="item.name">
                <span class="css-ellipsis">{{ item.name }}</span>
              </a-tooltip>
              <SvgIcon icon-class="man-info" size="16px" style="margin-left: 6px;" />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="portrait-container agn-item">
          <div class="item">
            <div class="empty-icon">
              <SvgIcon icon-class="subject-identity-placeholder" size="50px" />
            </div>
            <div class="name agn-item">
              <span class="css-ellipsis">关系人</span>
              <SvgIcon icon-class="man-info" size="16px" style="margin-left: 6px;" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { DEFAULT_CONFIG } from '@/constants';
import { useTagSelector } from '@/composables/useTagSelector';

import TagSelector from '@/components/Business/TagSelector/index.vue';

const props = defineProps({
  /**
   * 表单数据
   */
  formData: {
    type: Object,
    required: true
  },
});

const emit = defineEmits(['add-involved-case-people']);

// 常量
const { getImageWebURL } = DEFAULT_CONFIG;

const { handleSearchTags, handleCreateTag, handleTagChange } = useTagSelector();

/**
 * 添加关联事件
 */
const addRelatedCase = () => {
  props.formData.relatedCases.push({
    caseDescription: "",
    caseEndTime: "",
    caseName: "",
    caseStartTime: "",
    involvedCasePeoples: [],
    searchInvolvesPersons: '',
    times: []
  });
};

/**
 * 删除关联事件
 */
const removeRelatedCase = (index) => {
  props.formData.relatedCases.splice(index, 1);
};

/**
 * 添加关系人
 */
const addInvolvedCasePeople = (index) => {
  emit('add-involved-case-people', index);
};
</script>

<style scoped>
.social-behavior-fields {
  /* 继承父组件样式 */
}

.inner-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.button-container {
  width: 100px;
  display: flex;
  justify-content: flex-start;
  margin-left: 8px;
  gap: 8px;
}

.link {
  color: #00bfff;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}

.portrait-container {
  padding-left: 80px;
  margin-top: 8px;
}

.portrait-container .item {
  width: 86px;
  margin-right: 40px;
  text-align: center;
}

.portrait-container .item img {
  display: block;
  width: 86px;
  height: 86px;
  border-radius: 50%;
  object-fit: contain;
  background: black;
}

.portrait-container .item .name {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  height: 22px;
  justify-content: center;
}

.portrait-container .item .empty-icon {
  width: 86px;
  height: 86px;
  border-radius: 50%;
  background: #242934;
  display: flex;
  align-items: center;
  justify-content: center;
  border: solid 5px #181c27;
}
</style>
