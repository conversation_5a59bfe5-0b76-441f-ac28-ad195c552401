// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core';

export {};

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AButton: (typeof import('ant-design-vue/es'))['Button'];
    AConfigProvider: (typeof import('ant-design-vue/es'))['ConfigProvider'];
    ADrawer: (typeof import('ant-design-vue/es'))['Drawer'];
    ADropdown: (typeof import('ant-design-vue/es'))['Dropdown'];
    AForm: (typeof import('ant-design-vue/es'))['Form'];
    AFormItem: (typeof import('ant-design-vue/es'))['FormItem'];
    AIChatDialog: (typeof import('./src/components/AIChatDialog.vue'))['default'];
    AInput: (typeof import('ant-design-vue/es'))['Input'];
    ALayout: (typeof import('ant-design-vue/es'))['Layout'];
    ALayoutHeader: (typeof import('ant-design-vue/es'))['LayoutHeader'];
    ALayoutSider: (typeof import('ant-design-vue/es'))['LayoutSider'];
    AlgorithmScoreItem: (typeof import('./src/components/Business/AlgorithmScoreItem.vue'))['default'];
    AlgorithmScoreItemSkeleton: (typeof import('./src/components/Business/AlgorithmScoreItemSkeleton.vue'))['default'];
    AlgorithmTripleComparisonBar: (typeof import('./src/components/Graph/AlgorithmTripleComparisonBar.vue'))['default'];
    AlgorithmTripleVenn: (typeof import('./src/components/Graph/AlgorithmTripleVenn.vue'))['default'];
    AMenu: (typeof import('ant-design-vue/es'))['Menu'];
    AMenuItem: (typeof import('ant-design-vue/es'))['MenuItem'];
    AModal: (typeof import('ant-design-vue/es'))['Modal'];
    APopconfirm: (typeof import('ant-design-vue/es'))['Popconfirm'];
    APopover: (typeof import('ant-design-vue/es'))['Popover'];
    ArrowDownOutlined: (typeof import('@ant-design/icons-vue'))['ArrowDownOutlined'];
    ASelect: (typeof import('ant-design-vue/es'))['Select'];
    ASelectOption: (typeof import('ant-design-vue/es'))['SelectOption'];
    AssociatedEvent: (typeof import('./src/components/AssociatedEvent.vue'))['default'];
    ATextarea: (typeof import('ant-design-vue/es'))['Textarea'];
    ATooltip: (typeof import('ant-design-vue/es'))['Tooltip'];
    AUpload: (typeof import('ant-design-vue/es'))['Upload'];
    AvatarImage: (typeof import('./src/components/Common/AvatarImage/index.vue'))['default'];
    BadgeWrapper: (typeof import('./src/components/BadgeWrapper.vue'))['default'];
    BaseContainerPage: (typeof import('./src/components/BaseContainerPage.vue'))['default'];
    BaseResultFilter: (typeof import('./src/components/BaseResultFilter.vue'))['default'];
    BaseResultHeader: (typeof import('./src/components/BaseResultHeader.vue'))['default'];
    ButtonWrapper: (typeof import('./src/components/ButtonWrapper.vue'))['default'];
    CloseCircleOutlined: (typeof import('@ant-design/icons-vue'))['CloseCircleOutlined'];
    CompareResultsBarGraph: (typeof import('./src/components/Graph/CompareResultsBarGraph.vue'))['default'];
    ComparisonMagnifier: (typeof import('./src/components/Modal/ComparisonMagnifier.vue'))['default'];
    ComparisonParamsPopover: (typeof import('./src/components/Form/ComparisonParamsPopover.vue'))['default'];
    ComparisonTextMatching: (typeof import('./src/components/Modal/ComparisonTextMatching.vue'))['default'];
    CustomButtonWithTooltip: (typeof import('./src/components/CustomButtonWithTooltip.vue'))['default'];
    CustomContainer: (typeof import('./src/components/CustomContainer.vue'))['default'];
    CustomContainerHeader: (typeof import('./src/components/CustomContainerHeader.vue'))['default'];
    CustomPagination: (typeof import('./src/components/Business/CustomPagination.vue'))['default'];
    DatabaseConnectionForm: (typeof import('./src/components/Form/DatabaseConnectionForm.vue'))['default'];
    Divider: (typeof import('./src/components/Divider.vue'))['default'];
    DownOutlined: (typeof import('@ant-design/icons-vue'))['DownOutlined'];
    DrawerWrapper: (typeof import('./src/components/DrawerWrapper.vue'))['default'];
    EditOutlined: (typeof import('@ant-design/icons-vue'))['EditOutlined'];
    FaceBoxEditor: (typeof import('./src/components/FaceBoxEditor.vue'))['default'];
    FaceCanvas: (typeof import('./src/components/FaceCanvas.vue'))['default'];
    FaceCenteredImage: (typeof import('./src/components/FaceCenteredImage.vue'))['default'];
    FileUploadManager: (typeof import('./src/components/FileUploadManager.vue'))['default'];
    FilterComparedResult: (typeof import('./src/components/Business/FilterComparedResult.vue'))['default'];
    FilterSelect: (typeof import('./src/components/FilterSelect.vue'))['default'];
    GraphDiv: (typeof import('./src/components/Graph/GraphDiv.vue'))['default'];
    GroupedImageDisplay: (typeof import('./src/components/Business/GroupedImageDisplay.vue'))['default'];
    IconButton: (typeof import('./src/components/IconButton.vue'))['default'];
    IdentityImage: (typeof import('./src/components/Business/IdentityImage.vue'))['default'];
    IdentityImageCarousel: (typeof import('./src/components/Business/IdentityImageCarousel.vue'))['default'];
    Image: (typeof import('./src/components/Image.vue'))['default'];
    Image2: (typeof import('./src/components/Image2.vue'))['default'];
    ImageComparisonParamsForm: (typeof import('./src/components/Form/ImageComparisonParamsForm.vue'))['default'];
    ImageInfoForm: (typeof import('./src/components/Form/ImageInfoForm.vue'))['default'];
    ImageMagnifier: (typeof import('./src/components/ImageMagnifier.vue'))['default'];
    ImagePlaceHolder: (typeof import('./src/components/ImagePlaceHolder.vue'))['default'];
    ImagePreviewer: (typeof import('./src/components/ImagePreviewer.vue'))['default'];
    ImageRing: (typeof import('./src/components/ImageRing.vue'))['default'];
    ImageRingWithStatus: (typeof import('./src/components/ImageRingWithStatus.vue'))['default'];
    ImageRingWithStatusSkeleton: (typeof import('./src/components/ImageRingWithStatusSkeleton.vue'))['default'];
    ImageTracing: (typeof import('./src/components/Modal/ImageTracing.vue'))['default'];
    ImageUpload: (typeof import('./src/components/ImageUpload.vue'))['default'];
    ImageUploader: (typeof import('./src/components/ImageUploader.vue'))['default'];
    ImageUploaderManager: (typeof import('./src/components/ImageUploaderManager.vue'))['default'];
    ImportLibraryPortraitUsedFile: (typeof import('./src/components/Form/ImportLibraryPortraitUsedFile.vue'))['default'];
    InfoItem: (typeof import('./src/components/InfoItem.vue'))['default'];
    InfoLabel: (typeof import('./src/components/InfoLabel.vue'))['default'];
    ItemCarousel: (typeof import('./src/components/ItemCarousel.vue'))['default'];
    ItemSwitcherDropdown: (typeof import('./src/components/ItemSwitcherDropdown.vue'))['default'];
    LeftOutlined: (typeof import('@ant-design/icons-vue'))['LeftOutlined'];
    LibraryComparisonParamsForm: (typeof import('./src/components/Form/LibraryComparisonParamsForm.vue'))['default'];
    LoadingOutlined: (typeof import('@ant-design/icons-vue'))['LoadingOutlined'];
    LoadingSpinner: (typeof import('./src/components/LoadingSpinner.vue'))['default'];
    LoadMoreList: (typeof import('./src/components/LoadMoreList.vue'))['default'];
    LogoTitle: (typeof import('./src/components/LogoTitle.vue'))['default'];
    LongitudinalMultiFaceImageEditor: (typeof import('./src/components/Business/LongitudinalMultiFaceImageEditor.vue'))['default'];
    ManInfoForm: (typeof import('./src/components/Form/ManInfoForm.vue'))['default'];
    ManualAddLibraryPortraitForm: (typeof import('./src/components/Form/ManualAddLibraryPortraitForm.vue'))['default'];
    ManuallyAddPortrait: (typeof import('./src/components/ManuallyAddPortrait.vue'))['default'];
    MatchingCarousel: (typeof import('./src/components/Business/MatchingCarousel.vue'))['default'];
    MatchingCarouselSkeleton: (typeof import('./src/components/Business/MatchingCarouselSkeleton.vue'))['default'];
    MatchingItem: (typeof import('./src/components/Business/MatchingItem.vue'))['default'];
    MatchingItemList: (typeof import('./src/components/Business/MatchingItemList.vue'))['default'];
    MatchingResult: (typeof import('./src/components/Business/MatchingResult.vue'))['default'];
    MatchingResultHeader: (typeof import('./src/components/Business/MatchingResultHeader.vue'))['default'];
    MatchingResultTitle: (typeof import('./src/components/Business/MatchingResultTitle.vue'))['default'];
    MatchingVennDiagramCard: (typeof import('./src/components/Business/MatchingVennDiagramCard.vue'))['default'];
    ModalWrapper: (typeof import('./src/components/ModalWrapper.vue'))['default'];
    MultiCenterGraph: (typeof import('./src/components/Graph/RelationGraph/MultiCenterGraph.vue'))['default'];
    MultiFaceImageEditor: (typeof import('./src/components/Modal/MultiFaceImageEditor.vue'))['default'];
    NewFileUploadManager: (typeof import('./src/components/NewFileUploadManager.vue'))['default'];
    NumberUnit: (typeof import('./src/components/NumberUnit.vue'))['default'];
    OverflowTags: (typeof import('./src/components/OverflowTags.vue'))['default'];
    PagableList: (typeof import('./src/components/PagableList.vue'))['default'];
    PaginatedMatchGrid: (typeof import('./src/components/Business/PaginatedMatchGrid.vue'))['default'];
    PartitionedList: (typeof import('./src/components/PartitionedList.vue'))['default'];
    PersonalFigure: (typeof import('./src/components/Common/PersonalProfile/PersonalFigure.vue'))['default'];
    PersonalInfo: (typeof import('./src/components/Common/PersonalProfile/PersonalInfo.vue'))['default'];
    PinnedRowsWaterfall: (typeof import('./src/components/Modal/PinnedRowsWaterfall.vue'))['default'];
    PlusOutlined: (typeof import('@ant-design/icons-vue'))['PlusOutlined'];
    PortraitList: (typeof import('./src/components/PortraitList.vue'))['default'];
    PortraitProfile: (typeof import('./src/components/Business/PortraitProfile.vue'))['default'];
    PortraitProfileSkeleton: (typeof import('./src/components/Business/PortraitProfileSkeleton.vue'))['default'];
    ProcessingModeSelector: (typeof import('./src/components/ProcessingModeSelector.vue'))['default'];
    RadialRelationGraph: (typeof import('./src/components/Graph/RadialRelationGraph.vue'))['default'];
    RightOutlined: (typeof import('@ant-design/icons-vue'))['RightOutlined'];
    RouterLink: (typeof import('vue-router'))['RouterLink'];
    RouterView: (typeof import('vue-router'))['RouterView'];
    SearchInput: (typeof import('./src/components/SearchInput.vue'))['default'];
    SelectableDecorator: (typeof import('./src/components/SelectableDecorator.vue'))['default'];
    SingleCenterGraph: (typeof import('./src/components/Graph/RelationGraph/SingleCenterGraph.vue'))['default'];
    SlidingBar: (typeof import('./src/components/SlidingBar.vue'))['default'];
    SubjectInfoForm: (typeof import('./src/components/Form/SubjectInfoForm.vue'))['default'];
    SubjectItem: (typeof import('./src/components/Business/SubjectItem.vue'))['default'];
    SvgIcon: (typeof import('./src/components/SvgIcon.vue'))['default'];
    Tabs: (typeof import('./src/components/Tabs.vue'))['default'];
    TagSelector: (typeof import('./src/components/TagSelector.vue'))['default'];
    TemplateImportForm: (typeof import('./src/components/Form/TemplateImportForm.vue'))['default'];
    ThumbnailDecorator: (typeof import('./src/components/ThumbnailDecorator.vue'))['default'];
    Timeline: (typeof import('./src/components/Graph/Timeline.vue'))['default'];
    Toolbar: (typeof import('./src/components/Toolbar/Toolbar.vue'))['default'];
    ToolbarComparisonParamsContent: (typeof import('./src/components/Toolbar/ToolbarComparisonParamsContent.vue'))['default'];
    ToolbarImageInfoContent: (typeof import('./src/components/Toolbar/ToolbarImageInfoContent.vue'))['default'];
    ToolbarManInfoContent: (typeof import('./src/components/Toolbar/ToolbarManInfoContent.vue'))['default'];
    ToolbarManInfoItem: (typeof import('./src/components/Toolbar/ToolbarManInfoItem.vue'))['default'];
    ToolbarModalWrapper: (typeof import('./src/components/Toolbar/ToolbarModalWrapper.vue'))['default'];
    ToolbarObjectSelector: (typeof import('./src/components/Toolbar/ToolbarObjectSelector.vue'))['default'];
    TwoPanelLayout: (typeof import('./src/components/TwoPanelLayout.vue'))['default'];
    VideoControls: (typeof import('./src/components/VideoControls.vue'))['default'];
    '​VideoFrameLabelForm​​': (typeof import('./src/components/Form/​VideoFrameLabelForm​​.vue'))['default'];
    VideoInfoForm: (typeof import('./src/components/Form/VideoInfoForm.vue'))['default'];
    VideoItem: (typeof import('./src/components/Business/VideoItem.vue'))['default'];
    VideoPlayer: (typeof import('./src/components/VideoPlayer.vue'))['default'];
    VideoPlayerPlaceHolder: (typeof import('./src/components/VideoPlayerPlaceHolder.vue'))['default'];
    'VideoProcessingConfigForm​': (typeof import('./src/components/Form/VideoProcessingConfigForm​.vue'))['default'];
    VideoProcessingMode: (typeof import('./src/components/VideoProcessingMode.vue'))['default'];
  }
}
