const DEFAULT_CONFIG = {
  enableMenuAccessControl: true,
  isAuthRequired: true,
  whitePages: ['/user/login', '/not-found', '/unauthorized'],

  
  uploadVideoAPI: 'http://**************:6792/api/v2/videos',
  // uploadVideoAPI: 'http://localhost:6790/api/v2/videos',
  // uploadVideoAPI: 'http://localhost:6790/api/v2/videos',
  uploadVideoHeaders: { bucketId: '39ba7d84-15c0-4aeb-9716-711a5454c70c' },
  uploadImageAPI: 'http://**************:6790/api/v2/images/upload',
  // uploadImageAPI: 'http://localhost:6790/api/v2/images/upload',
  uploadImageHeaders: { bucketId: '8b8c1248-7c68-4968-bf36-ca5986075177' },

  uploadFileAPI: 'http://**************:7791/portrait-import-jobs/upload',
  uploadFileHeaders: { },

  getVideoWebUrl: (videoId) => {
    return `http://**************:6792/api/v2/videos/download/${videoId}?bucketId=${DEFAULT_CONFIG.uploadVideoHeaders.bucketId}`;
    // return `http://localhost:6790/api/v2/videos/download/${videoId}?bucketId=${DEFAULT_CONFIG.uploadVideoHeaders.bucketId}`
  },
  getVideoThumbnailUrl: (thumbnailImageId) => {
    return `http://**************:9005/processed-videos${thumbnailImageId}`;
  },
  getVideoStreamingUrl: (streamingURI) => {
    return `http://**************:9005/processed-videos${streamingURI}`;
  },
  getImageWebURL: (imageId) => {
    return `http://**************:6790/api/v2/images/download/${imageId}?bucketId=${DEFAULT_CONFIG.uploadImageHeaders.bucketId}`;
    // return `http://localhost:6790/api/v2/images/${imageId}?bucketId=${DEFAULT_CONFIG.uploadImageHeaders.bucketId}`;
  }
};

export default DEFAULT_CONFIG;
