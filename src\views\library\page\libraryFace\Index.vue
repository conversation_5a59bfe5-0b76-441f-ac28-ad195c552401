<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-28 11:01:30
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 16:12:10
 * @FilePath: \platform-face-web\src\views\library\page\libraryFace\Index.vue
 * @Description: 人脸检测页面
 * 
 * 
-->
<template>
    <div class="library-face-container">
        <DualColumnLayout :loading="loading" :showStatus="showStatus">
            <template #left-content>
                <ImageUpload v-if="!imageId" @onChange="handleImageUpload"></ImageUpload>
                <AvatarImage v-else :src="getImageWebURL(imageId)" ringColor="transparent" :ringThickness="0"
                    borderRadius="8px" width="100%" height="100%"></AvatarImage>
            </template>
            <template #right-content>
                <div class="result-content">
                    <a-empty v-if="!faceBoxes" description="暂无结果，请先上传图片" :image="ImageEmpty"></a-empty>
                    <FaceCanvas v-else :imageSrc="getImageWebURL(imageId)" :faceBoxes="faceBoxes" :editable="false"
                        :customFaceBoxStyle="customFaceBoxStyle">
                    </FaceCanvas>
                </div>
            </template>
            <template #action-button>
                <CustomButtonWithTooltip width="188px" height="42px" @click="handleDetection">开始检测
                </CustomButtonWithTooltip>
            </template>
        </DualColumnLayout>
    </div>
</template>

<script setup>
import { ref } from "vue";
import { imageDetection } from '@/api/library/library.js';
import { DEFAULT_CONFIG } from '@/constants';

import ImageUpload from "../../components/ImageUpload.vue";
import DualColumnLayout from "../../components/DualColumnLayout.vue";
import ImageEmpty from "@/assets/svg/empty/image-empty.svg"

const { getImageWebURL } = DEFAULT_CONFIG || {};

const loading = ref(false);
const showStatus = ref(false)
const imageId = ref(null);
const faceBoxes = ref(null);

// 自定义人脸框样式配置 - 绿色边框，不显示选择状态和索引
const customFaceBoxStyle = ref({
    borderColor: '#00E875',      // 绿色边框
    borderWidth: '2px',          // 边框宽度
    borderStyle: 'dashed',        // 实线边框
    showSelection: false,        // 不显示选择状态图标
    showIndex: false,           // 不显示索引号
    backgroundColor: 'transparent' // 透明背景
});
const handleImageUpload = (uploadImageId) => {
    imageId.value = uploadImageId;
};

const handleDetection = async () => {
    try {
        loading.value = true
        showStatus.value = true
        const payload = {
            imageIds: [imageId.value],
        };
        const response = await imageDetection(payload);
        const data = response.data;
        faceBoxes.value = data?.[0].detectedFaces.map(face => {
            return {
                x: face.coordinate[0],
                y: face.coordinate[1],
                width: face.coordinate[2],
                height: face.coordinate[3],
                key: face.faceIndex,
            }
        })
    } catch (error) {
        console.error('检测错误', error)
    } finally {
        loading.value = false
    }
}
</script>

<style lang="scss" scoped>
.library-face-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    box-sizing: border-box;
}

.result-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: #000000;
    font-size: 14px;
    color: #8593A7;
}
</style>