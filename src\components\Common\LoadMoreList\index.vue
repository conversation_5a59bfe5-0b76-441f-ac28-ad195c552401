<!--
 * @Author: yuzhouisme
 * @Date: 2025-07-24 15:47:18
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 15:54:38
 * @FilePath: \platform-face-web\src\components\Common\LoadMoreList\index.vue
 * @Description: 是一个通用的列表组件，支持多种布局模式（垂直、水平、网格）和加载更多功能
-->
<template>
  <div ref="listContainer" :class="containerClass" :style="containerStyle" v-on="scrollEvent">
    <!-- 第一项专属插槽 -->
    <template v-if="$slots.first">
      <div class="list-first-item" :class="getItemClass(dataList[0][keyName])"
        @click="(event) => handleClick(dataList[0], event)">
        <slot name="first" :item="dataList[0]" :is-selected="dataList[0][keyName] === selectedItemId" />
      </div>
    </template>

    <!-- 其余项 -->
    <div v-for="(item, idx) in dataList.slice(firstSlotUsed ? 1 : 0)" :key="item[keyName]" class="list-item"
      :class="getItemClass(item[keyName])" @click="(event) => handleClick(item, event)">
      <slot name="default" :item="item" :is-selected="item[keyName] === selectedItemId"
        :index="firstSlotUsed ? idx + 1 : idx" />
    </div>

    <div v-if="showBottomTitle">
      <div v-if="showLoadMore" class="load-more" @click="clickLoadMore">
        <span>{{ loadMoreText }}</span>
      </div>
      <div v-else class="load-more">
        <span></span>
      </div>
    </div>
    <div v-if="loading" class="loading"></div>
  </div>
</template>

<script setup>
import { ref, computed, watch, useSlots } from 'vue';

const props = defineProps({
  // 数据列表
  data: {
    type: Array,
    required: true,
  },
  // 是否还有更多数据（有则触发加载更多）
  hasMore: {
    type: Boolean,
    default: false,
  },
  // 外部传入的选中项 id（或其它字段值，根据 keyName）
  selectedId: {
    type: [String, Number],
    default: null,
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 自定义选中项样式类
  customSelectedClass: {
    type: String,
    default: '',
  },
  // 是否由父组件处理点击事件（处理选中状态等）
  handleClickInParent: {
    type: Boolean,
    default: true,
  },
  // 用于唯一标识列表项的字段名（默认 "id"）
  keyName: {
    type: String,
    default: 'id',
  },
  // 布局类型：vertical（垂直）、horizontal（水平）、grid（网格）
  layoutType: {
    type: String,
    default: 'vertical',
    validator: (v) => ['vertical', 'horizontal', 'grid'].includes(v),
  },
  // 网格布局时的间距
  gapSize: {
    type: String,
    default: '16px',
  },
  // 网格布局时的列数
  gridColumns: {
    type: Number,
    default: 3,
  },
  // 是否使用滚动加载（适用于垂直和网格布局）；水平布局一般采用点击加载
  scrollLoad: {
    type: Boolean,
    default: true,
  },
  // 是否展示
  showBottomTitle: {
    type: Boolean,
    default: true
  },
  // 垂直分布的时候gap
  verticalGap: {
    type: String,
    default: '10px'
  }
});

const emit = defineEmits(['loadMore', 'update:selectedId']);

const listContainer = ref(null)

const firstSlotUsed = computed(() => !!useSlots().first && props.data.length > 0)

// 内部选中项，响应外部传入的 selectedId
const selectedItemId = ref(props.selectedId);
watch(
  () => props.selectedId,
  (newVal) => {
    selectedItemId.value = newVal;
  }
);

// 数据列表
const dataList = computed(() => props.data || []);

// 当滚动加载开启时，生成 scroll 事件处理对象
const scrollEvent = computed(() => {
  return props.scrollLoad ? { scroll: handleScroll } : undefined;
});

// 根据 layoutType 返回不同容器类名
const containerClass = computed(() => {
  if (props.layoutType === 'vertical') {
    return 'vertical-list';
  } else if (props.layoutType === 'horizontal') {
    return 'horizontal-list';
  } else if (props.layoutType === 'grid') {
    return 'grid-list';
  }
  return '';
});

// 针对 grid 布局，内联设置 grid-template-columns 与 gap
const containerStyle = computed(() => {
  if (props.layoutType === 'grid') {
    return {
      gridTemplateColumns: `repeat(${props.gridColumns}, 1fr)`,
      gap: props.gapSize,
    };
  }
  return {};
});

// 当有更多且不在加载状态时显示加载更多区域
const showLoadMore = computed(() => props.hasMore && !props.loading);

// 不同布局可采用不同提示文字（例如水平布局采用点击加载）
const loadMoreText = computed(() =>
  props.layoutType === 'horizontal' ? '加载更多' : '暂无更多...'
);

// 点击 load-more 时触发加载（仅在未使用滚动加载时有效）
const clickLoadMore = () => {
  if (!props.scrollLoad) {
    loadMore();
  }
};

// 加载更多数据（由父组件监听 loadMore 事件处理具体逻辑）
const loadMore = async () => {
  emit('loadMore');
};

// 选中项逻辑
const handleSelect = (item) => {
  selectedItemId.value = item[props.keyName];
  emit('update:selectedId', item);
};

// 点击项时处理选中逻辑（如果由父组件处理，则阻止冒泡）
const handleClick = (item, event) => {
  if (props.handleClickInParent) {
    event.stopPropagation();
    handleSelect(item);
  }
  // 同时允许 slot 内部进一步处理点击事件
};

// 返回选中项的样式类
const getItemClass = (itemId) =>
  itemId === selectedItemId.value ? props.customSelectedClass : '';

// 滚动事件：当容器滚动到底部时触发加载更多
const handleScroll = (event) => {
  const container = event.target;
  const isBottom =
    container.scrollHeight - container.scrollTop <= container.clientHeight + 1;
  if (isBottom && !props.loading && props.hasMore) {
    loadMore();
  }
};

defineExpose({ listContainer })
</script>

<style lang="scss" scoped>
/* 垂直列表样式 */
.vertical-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: v-bind(verticalGap);
  overflow-y: auto;
  padding: 12px;
  scrollbar-gutter: stable;

  .list-item {
    width: calc(100% - 8px);
  }
}

/* 水平列表样式 */
.horizontal-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 32px;
  overflow-x: auto;
  white-space: nowrap;
}

/* 网格列表样式 */
.grid-list {
  width: 100%;
  max-height: 100%;
  display: grid;
  overflow: auto;
  padding: 4px;
  /* grid-template-columns 与 gap 由内联 style 控制 */
}

/* 公共样式 */
.list-item {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: var(--font-size-small);
  color: #B0BBCC;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 150px;
  color: #666;
  font-size: var(--font-size-small);
}
</style>
