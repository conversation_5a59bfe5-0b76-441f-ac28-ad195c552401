{
  "root": true,
  "env": {
    "browser": true,
    "es2021": false,
    "node": true
  },
  // "extends": ["eslint:recommended", "plugin:vue/vue3-recommended", "prettier"],
  "extends": ["prettier"],
  "overrides": [],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["vue", "prettier"],
  "rules": {
    "prettier/prettier": "error",
    "vue/multi-word-component-names": "off",
    "vue/no-v-model-argument": "off"
  }
}
