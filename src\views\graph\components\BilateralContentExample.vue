<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-30 14:28:11
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-30 16:29:29
 * @FilePath: \platform-face-web\src\views\graph\components\BilateralContentExample.vue
 * @Description: BilateralContainer组件使用示例
-->
<template>
  <div class="example-container">
    <h2>左右布局组件示例</h2>

    <div class="bilateral-wrapper">
      <BilateralContainer :min-width="200" :initial-left-ratio="0.6" class="bilateral-content">
        <!-- 左侧内容插槽 -->
        <template #left>
          <div class="left-content">
            <h3>左侧面板</h3>
            <p>这是左侧面板的内容，可以包含任何组件。</p>

            <!-- 模拟一些内容 -->
            <div class="content-item" v-for="i in 20" :key="i">
              <h4>项目 {{ i }}</h4>
              <p>这是第 {{ i }} 个项目的内容描述。这里可以放置各种组件，比如表格、表单、图表等。</p>
              <div class="item-actions">
                <button class="btn btn-primary">编辑</button>
                <button class="btn btn-secondary">删除</button>
              </div>
            </div>
          </div>
        </template>

        <!-- 右侧内容插槽 -->
        <template #right>
          <div class="right-content">
            <h3>右侧面板</h3>
            <p>这是右侧面板的内容，同样可以包含任何组件。</p>

            <!-- 模拟一些内容 -->
            <div class="content-item" v-for="i in 15" :key="i">
              <h4>详情 {{ i }}</h4>
              <p>这是第 {{ i }} 个详情的内容。右侧面板通常用于显示详细信息、配置选项等。</p>
              <div class="detail-info">
                <span class="label">状态:</span>
                <span class="value">活跃</span>
              </div>
              <div class="detail-info">
                <span class="label">创建时间:</span>
                <span class="value">2025-07-30</span>
              </div>
            </div>
          </div>
        </template>
      </BilateralContainer>
    </div>
  </div>
</template>

<style scoped>
.example-container {
  padding: 20px;
  height: 80vh;
  box-sizing: border-box;
}

.bilateral-wrapper {
  height: calc(80vh - 100px);
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.bilateral-content {
  height: 100%;
}

.left-content,
.right-content {
  height: 100%;
  
}

.left-content h3,
.right-content h3 {
  margin: 0 0 16px 0;
  color: #fff;
  font-size: 18px;
}

.content-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.content-item h4 {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 16px;
}

.content-item p {
  margin: 0 0 12px 0;
  color: #6c757d;
  line-height: 1.5;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.detail-info {
  display: flex;
  margin-bottom: 8px;
}

.detail-info .label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.detail-info .value {
  color: #6c757d;
}
</style>