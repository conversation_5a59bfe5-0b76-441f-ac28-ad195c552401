::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #384b6e 0%, #6ed0ff 100%);
  border-radius: 8px;
  min-height: 24px;
}
::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #6ed0ff 0%, #384b6e 100%);
}
::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 8px;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #6ed0ff #232b3b;
}

// =====================
// 公共常用样式
// =====================

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

// 单行文本溢出省略
.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
// 多行文本溢出省略（2行）
.text-ellipsis-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}
// 多行文本溢出省略（3行）
.text-ellipsis-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

// 禁止选中
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

// flex常用工具类
.flex {
  display: flex;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-column {
  display: flex;
  flex-direction: column;
}

// 通用按钮样式
.btn-primary {
  background: linear-gradient(90deg, #667eea 10%, #764ba2 90%);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0.5em 1.5em;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px #667eea33;
  &:hover {
    background: linear-gradient(90deg, #764ba2 10%, #667eea 90%);
    box-shadow: 0 4px 16px #764ba244;
  }
}
.btn-danger {
  background: #ff4d4f;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0.5em 1.5em;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px #ff4d4f33;
  &:hover {
    background: #d9363e;
    box-shadow: 0 4px 16px #ff4d4f44;
  }
}
.btn-link {
  background: none;
  color: #667eea;
  border: none;
  padding: 0;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  &:hover {
    color: #764ba2;
  }
}

// 通用卡片阴影
.card-shadow {
  box-shadow: 0 4px 24px 0 rgba(30, 60, 120, 0.12), 0 1.5px 8px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  background: #232946;
}

// 响应式隐藏/显示
.hidden-xs {
  @media (max-width: 575px) {
    display: none !important;
  }
}
.hidden-sm {
  @media (max-width: 767px) {
    display: none !important;
  }
}
.hidden-md {
  @media (max-width: 991px) {
    display: none !important;
  }
}
.hidden-lg {
  @media (max-width: 1199px) {
    display: none !important;
  }
}
.visible-xs {
  display: none !important;
  @media (max-width: 575px) {
    display: block !important;
  }
}
.visible-sm {
  display: none !important;
  @media (max-width: 767px) {
    display: block !important;
  }
}
.visible-md {
  display: none !important;
  @media (max-width: 991px) {
    display: block !important;
  }
}
.visible-lg {
  display: none !important;
  @media (max-width: 1199px) {
    display: block !important;
  }
}

// 通用分割线
.divider {
  height: 1px;
  background: #384b6e;
  margin: 16px 0;
  border: none;
}

// 通用圆角
.rounded {
  border-radius: 8px !important;
}

// 通用阴影
.shadow {
  box-shadow: 0 2px 8px rgba(30, 60, 120, 0.12);
}

// 通用背景色
.bg-dark {
  background: #232946 !important;
  color: #fff !important;
}
.bg-light {
  background: #f5f6fa !important;
  color: #232946 !important;
}

// 淡入
.fade-in {
  animation: fadeIn 0.5s;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// 缩放弹入
.scale-in {
  animation: scaleIn 0.4s;
}
@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95);}
  to { opacity: 1; transform: scale(1);}
}

// 上下左右间距
.mt-8 { margin-top: 8px !important; }
.mb-8 { margin-bottom: 8px !important; }
.ml-8 { margin-left: 8px !important; }
.mr-8 { margin-right: 8px !important; }
.pt-8 { padding-top: 8px !important; }
.pb-8 { padding-bottom: 8px !important; }
.pl-8 { padding-left: 8px !important; }
.pr-8 { padding-right: 8px !important; }
// 你可以按需添加 4/12/16/24/32 等常用间距

.font-12 { font-size: 12px !important; }
.font-14 { font-size: 14px !important; }
.font-16 { font-size: 16px !important; }
.font-bold { font-weight: bold !important; }
.font-light { font-weight: 300 !important; }

.text-success { color: #52c41a !important; }
.text-warning { color: #faad14 !important; }
.text-danger  { color: #ff4d4f !important; }
.text-info    { color: #1890ff !important; }
.bg-success   { background: #f6ffed !important; color: #52c41a !important; }
.bg-warning   { background: #fffbe6 !important; color: #faad14 !important; }
.bg-danger    { background: #fff1f0 !important; color: #ff4d4f !important; }
.bg-info      { background: #e6f7ff !important; color: #1890ff !important; }

.avatar {
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #eee;
  object-fit: cover;
}
.avatar-sm { width: 24px; height: 24px; }
.avatar-lg { width: 64px; height: 64px; }

.overlay {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  z-index: 9999;
}

.loading-spin {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 2px solid #6ed0ff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}
@keyframes spin {
  to { transform: rotate(360deg);}
}

.form-item {
  margin-bottom: 20px;
}

.img-fluid {
  max-width: 100%;
  height: auto;
  display: block;
}

.mask-gradient-bottom {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    left: 0; right: 0; bottom: 0;
    height: 40px;
    background: linear-gradient(180deg, transparent, #232946 90%);
    pointer-events: none;
  }
}

.list-unstyled {
  list-style: none;
  margin: 0;
  padding: 0;
}

.transition { transition: all 0.2s; }
.transition-bg { transition: background 0.2s; }
.transition-color { transition: color 0.2s; }

.border {
  border: 1px solid #384b6e !important;
}
.border-top    { border-top: 1px solid #384b6e !important; }
.border-bottom { border-bottom: 1px solid #384b6e !important; }
.border-left   { border-left: 1px solid #384b6e !important; }
.border-right  { border-right: 1px solid #384b6e !important; }
.border-none   { border: none !important; }

.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed    { position: fixed !important; }
.sticky   { position: sticky !important; }
.top-0    { top: 0 !important; }
.left-0   { left: 0 !important; }
.right-0  { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.center-absolute {
  position: absolute !important;
  left: 50%; top: 50%;
  transform: translate(-50%, -50%);
}

.opacity-0   { opacity: 0 !important; }
.opacity-50  { opacity: 0.5 !important; }
.opacity-80  { opacity: 0.8 !important; }
.opacity-100 { opacity: 1 !important; }

.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-move    { cursor: move !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }
.min-w-0 { min-width: 0 !important; }
.min-h-0 { min-height: 0 !important; }

.rounded-sm  { border-radius: 4px !important; }
.rounded-md  { border-radius: 8px !important; }
.rounded-lg  { border-radius: 16px !important; }
.rounded-pill { border-radius: 999px !important; }

.shadow-sm { box-shadow: 0 1px 4px rgba(30,60,120,0.08) !important; }
.shadow-md { box-shadow: 0 4px 16px rgba(30,60,120,0.12) !important; }
.shadow-lg { box-shadow: 0 8px 32px rgba(30,60,120,0.18) !important; }

.z-1 { z-index: 1 !important; }
.z-10 { z-index: 10 !important; }
.z-100 { z-index: 100 !important; }
.z-1000 { z-index: 1000 !important; }

ul {
  list-style: none;
}

.z-1 {
  z-index: 1;
}

.w-100 {
  width: 100% !important;
}
.h-100 {
  height: 100% !important;
}

.position-absolute {
  position: absolute !important;
}

.left {
  left: 0;
}

.bottom {
  bottom: 0;
}

.right {
  right: 0;
}

/** border **/
.border-2x {
  border-width: 2px !important;
}
.border {
  border: 1px solid #f1f1f1 !important;
}
.border-bottom {
  border-bottom: 1px solid #f1f1f1;
}
.border-top {
  border-top: 1px solid #f1f1f1 !important;
}
.border-left {
  border-left: 1px solid #f1f1f1 !important;
}
.border-right {
  border-right: 1px solid #f1f1f1 !important;
}
.dotted-border {
  border-bottom: 2px dotted #e6e6e6;
}
.rounded {
  border-radius: 4px !important;
}
.rounded-right,
.rounded-top {
  border-top-right-radius: 4px !important;
}
.rounded-left,
.rounded-top {
  border-top-left-radius: 4px !important;
}
.rounded-bottom,
.rounded-right {
  border-bottom-right-radius: 4px !important;
}
.rounded-bottom,
.rounded-left {
  border-bottom-left-radius: 4px !important;
}
.border-top-0 {
  border-top: 0 !important;
}

/** bg **/
.bg-color {
  color: #495057;
}
.bg-white {
  background-color: #fff !important;
}
.bg-body {
  background-color: #f8f8fd !important;
}
.bg-gray-lighter {
  background-color: #f8f9fa !important;
}

/** padding && margin **/
.p-0 {
  padding: 0 !important;
}
.p-1 {
  padding: 4px !important;
}
.p-2 {
  padding: 8px !important;
}
.p-3 {
  padding: 16px !important;
}
.p-4 {
  padding: 20px !important;
}
.pl-1,
.px-1 {
  padding-left: 4px !important;
}
.pl-2,
.px-2 {
  padding-left: 8px !important;
}
.pl-3,
.px-3 {
  padding-left: 16px !important;
}
.pl-4,
.px-4 {
  padding-left: 20px !important;
}
.pl-6,
.px-6 {
  padding-left: 32px !important;
}
.pr-0 {
  padding-right: 0 !important;
}
.pr-1,
.px-1 {
  padding-right: 4px !important;
}
.pr-2,
.px-2 {
  padding-right: 8px !important;
}
.pr-3,
.px-3 {
  padding-right: 16px !important;
}
.pr-4,
.px-4 {
  padding-right: 20px !important;
}
.pr-6,
.px-6 {
  padding-right: 32px !important;
}
.pt-0,
.py-0 {
  padding-top: 0 !important;
}
.pt-1,
.py-1 {
  padding-top: 4px !important;
}
.pt-2,
.py-2 {
  padding-top: 8px !important;
}
.pt-3,
.py-3 {
  padding-top: 16px !important;
}
.pt-4,
.py-4 {
  padding-top: 20px !important;
}
.pt-5,
.py-5 {
  padding-top: 24px !important;
}
.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}
.pb-1,
.py-1 {
  padding-bottom: 4px !important;
}
.pb-2,
.py-2 {
  padding-bottom: 8px !important;
}
.pb-3,
.py-3 {
  padding-bottom: 16px !important;
}
.pb-4,
.py-4 {
  padding-bottom: 20px !important;
}
.pb-5,
.py-5 {
  padding-bottom: 24px !important;
}
.mt-0,
.my-0 {
  margin-top: 0 !important;
}
.mt-1,
.my-1 {
  margin-top: 4px !important;
}
.mt-2,
.my-2 {
  margin-top: 8px !important;
}
.mt-3,
.my-3 {
  margin-top: 16px !important;
}
.mt-4,
.my-4 {
  margin-top: 20px !important;
}
.mt-5,
.my-5 {
  margin-top: 24px !important;
}
.mt-6,
.my-6 {
  margin-top: 32px !important;
}
.mt-7,
.my-7 {
  margin-top: 48px !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}
.mb-1,
.my-1 {
  margin-bottom: 4px !important;
}
.mb-2,
.my-2 {
  margin-bottom: 8px !important;
}
.mb-3,
.my-3 {
  margin-bottom: 16px !important;
}
.mb-4,
.my-4 {
  margin-bottom: 20px !important;
}
.mb-5,
.my-5 {
  margin-bottom: 24px !important;
}
.mb-6,
.my-6 {
  margin-bottom: 32px !important;
}
.mb-7,
.my-7 {
  margin-bottom: 48px !important;
}

.ml-1,
.mx-1 {
  margin-left: 4px !important;
}
.ml-2,
.mx-2 {
  margin-left: 8px !important;
}
.ml-3,
.mx-3 {
  margin-left: 16px !important;
}
.mr-1,
.mx-1 {
  margin-right: 4px !important;
}
.mr-2,
.mx-2 {
  margin-right: 8px !important;
}
.mr-3,
.mx-3 {
  margin-right: 16px !important;
}
.g-1 {
  gap: 4px !important;
}
.g-2 {
  gap: 8px !important;
}
.g-3 {
  gap: 16px !important;
}

// img, svg {
//   vertical-align: middle;
// }

.position-relative {
  position: relative !important;
}

/** flex **/
.d-flex {
  display: flex !important;
}
.flex-column {
  flex-direction: column !important;
}
.flex-1 {
  flex: 1 1 auto;
}
.d-grid {
  display: grid !important;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between !important;
}
.justify-content-end {
  justify-content: flex-end !important;
}
.align-items-center {
  align-items: center !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.row {
  display: flex;
  flex-wrap: wrap;
}
.no-gutters {
  margin-right: 0;
  margin-left: 0;
}
.width-fit-content {
  width: fit-content;
}
.hover-underline:hover {
  text-decoration: underline;
}

/** font **/
.font-size-h3 {
  font-size: 22px;
}
.font-size-h4 {
  font-size: 19px;
}
.font-w400 {
  font-weight: 400 !important;
}
.font-weight-bold {
  font-weight: 500 !important;
}
.font-w600 {
  font-weight: 600 !important;
}
.font-w700 {
  font-weight: 700 !important;
}
.font-size-md {
  font-size: 16px !important;
  font-weight: 400;
}
.font-size-sm {
  font-size: 13px !important;
}
.font-size-xs {
  font-size: 11px !important;
}
.font-size-xxs {
  font-size: 9px !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.color-text-secondary {
  color: #9c9c9c !important;
}
.text-danger {
  color: #e04f1a !important;
}
.text-muted {
  color: #6c757d;
}
.text-gray-500 {
  color: #adb5bd;
}
.text-dark-gray {
  color: #8a8d93;
}
.text-warning {
  color: #ffb119;
}
.text-success {
  color: #82b54b;
}
.gray-highlight {
  color: #495057;
  background-color: #e4e4e4;
}

.text-uppercase {
  letter-spacing: 1px;
  text-transform: uppercase;
}
.text-capitalize {
  text-transform: capitalize !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.w-fit-content {
  width: fit-content;
}

/** animated **/
.animated {
  animation-duration: 1.2s;
  animation-fill-mode: both;
}
.fadeIn {
  animation-name: fadeIn;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.hover-underline:hover {
  text-decoration: underline;
}

.agn-item {
  display: flex;
  align-items: center;
}

.s-ben {
  justify-content: space-between;
}

.spinner-component {
  display: none;
  &.show {
    display: block;
  }
}

.dragging {
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

.skeleton-box {
  background-color: #3a3a3a;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.skeleton-text {
  display: inline-block;
  width: 50px;
  height: 12px;
  border-radius: 4px;
  background-color: #3a3a3a;
  animation: shimmer 3s infinite;
}

.skeleton-icon {
  width: 30px;
  height: 30px;
  background-color: #2b2b2b;
  border-radius: 50%;
  animation: shimmer 3s infinite;
}

.skeleton-divider {
  width: 1px;
  height: 16px;
  background-color: #3a3a3a;
}

.page-top-nav {
  position: absolute;
  display: flex;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 16px;
  z-index: 2;

  .tab-underline {
    bottom: -13px !important;
  }
}

.glow-text {
  position: relative;
  color: #7FB0F6;
  font-weight: bold;
  text-shadow:
    0 0 8px #7FB0F6,
    0 0 16px #7FB0F6,
    0 0 24px #7FB0F6;
  overflow: hidden;
}
.glow-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0) 10%,
    rgba(255,255,255,0) 20%,
    rgba(255,255,255,0) 30%,
    rgba(255,255,255,0) 70%,
    rgba(255,255,255,0.4) 80%,
    rgba(255,255,255,0.25) 90%,
    rgba(255,255,255,0) 100%
  );
  pointer-events: none;
  will-change: transform, opacity;
}