/**
 * 连线渲染器 - 负责所有连线相关的渲染和交互
 */

import * as d3 from 'd3';

/**
 * 单中心图连线渲染器
 */
export class SingleCenterLinkRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染单中心图的连线
     * @param {d3.Selection} container - 连线容器
     * @param {Array} linkKeyArr - 连线数据数组
     * @param {Map} nodeMap - 节点映射
     * @param {number} maxVisibleScore - 最大可见分数
     * @returns {d3.Selection} 连线选择器
     */
    renderLinks(container, linkKeyArr, nodeMap, maxVisibleScore) {
        // 使用D3数据绑定绘制连线
        const linkLines = container
            .selectAll('.link')
            .data(linkKeyArr, (d) => d.__key)
            .enter()
            .append('line')
            .attr('class', 'link')
            .attr('stroke', this.configManager.get('links.default.stroke'))
            .attr('stroke-width', this.configManager.get('links.default.strokeWidth'));

        // 更新连线位置和透明度
        this.updateLinkPositions(linkLines, nodeMap, maxVisibleScore);

        return linkLines;
    }

    /**
     * 更新连线位置和透明度
     * @param {d3.Selection} linkLines - 连线选择器
     * @param {Map} nodeMap - 节点映射
     * @param {number} maxVisibleScore - 最大可见分数
     */
    updateLinkPositions(linkLines, nodeMap, maxVisibleScore) {
        const visibleOpacity = this.configManager.get('links.default.opacity.visible');
        const hiddenOpacity = this.configManager.get('links.default.opacity.hidden');

        linkLines
            .attr('x1', (d) => {
                const sourceNode = nodeMap.get(d.source);
                return sourceNode ? sourceNode.x : 0;
            })
            .attr('y1', (d) => {
                const sourceNode = nodeMap.get(d.source);
                return sourceNode ? sourceNode.y : 0;
            })
            .attr('x2', (d) => {
                const targetNode = nodeMap.get(d.target);
                return targetNode ? targetNode.x : 0;
            })
            .attr('y2', (d) => {
                const targetNode = nodeMap.get(d.target);
                return targetNode ? targetNode.y : 0;
            })
            .attr('stroke-opacity', (d) => {
                // 如果maxVisibleScore为0或负数，显示所有连线
                if (maxVisibleScore <= 0) {
                    return visibleOpacity;
                }
                // 否则根据分数决定可见性
                return d.score >= maxVisibleScore ? visibleOpacity : hiddenOpacity;
            });
    }

    /**
     * 高亮连线
     * @param {string} nodeId - 节点ID
     * @param {d3.Selection} linkLines - 连线选择器
     * @param {Object} nodeLinkMap - 节点连线映射
     * @param {number} maxVisibleScore - 最大可见分数
     * @param {string} currentClickNodeId - 当前点击的节点ID
     * @returns {string|null} 更新后的点击节点ID
     */
    highlightLinkedLines(nodeId, linkLines, nodeLinkMap, maxVisibleScore, currentClickNodeId) {
        const visibleOpacity = this.configManager.get('links.default.opacity.visible');
        const hiddenOpacity = this.configManager.get('links.default.opacity.hidden');
        const highlightOpacity = this.configManager.get('links.default.opacity.highlight');
        const dimmedOpacity = this.configManager.get('links.default.opacity.dimmed');

        // 恢复所有状态
        if (nodeId === currentClickNodeId) {
            // 恢复到初始状态：使用与初始渲染完全相同的逻辑
            linkLines.style('stroke-opacity', (d) => {
                // 如果maxVisibleScore为0或负数，显示所有连线
                if (maxVisibleScore <= 0) {
                    return visibleOpacity;
                }
                // 否则根据分数决定可见性
                return d.score >= maxVisibleScore ? visibleOpacity : hiddenOpacity;
            });
            return null; // 清除点击状态
        }

        // 获取该节点关联的所有连线复合键
        const linkedKeys = nodeLinkMap[nodeId] || new Set();

        // 批量更新透明度
        linkLines.style('stroke-opacity', (d) => {
            if (d.score < maxVisibleScore && !linkedKeys.has(d.__key)) return hiddenOpacity;
            return linkedKeys.has(d.__key) ? highlightOpacity : dimmedOpacity;
        });

        return nodeId; // 返回新的点击节点ID
    }
}

/**
 * 多中心图连线渲染器
 */
export class MultiCenterLinkRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染多中心图的所有连线
     * @param {d3.Selection} container - 连线容器
     * @param {Object} linkGroups - 分组的连线数据
     * @param {Array} nodes - 节点数组
     */
    renderLinks(container, linkGroups, nodes) {
        const nodeMap = this.createNodeMap(nodes);

        // 渲染中心节点间连线
        this.renderCenterLinks(container, linkGroups.centerLinks, nodeMap);

        // 渲染跨组连线
        this.renderInterGroupLinks(container, linkGroups.interGroup, nodeMap);

        // 渲染组内连线
        this.renderIntraGroupLinks(container, linkGroups.intraGroup, nodeMap);
    }

    /**
     * 创建节点映射
     * @param {Array} nodes - 节点数组
     * @returns {Map} 节点映射
     */
    createNodeMap(nodes) {
        const nodeMap = new Map();
        nodes.forEach((node) => {
            if (node.isCenter) {
                nodeMap.set(node.photoId, node);
            } else {
                nodeMap.set(node.portrait.portraitId, node);
            }
        });
        return nodeMap;
    }

    /**
     * 渲染中心节点间连线
     * @param {d3.Selection} container - 容器
     * @param {Array} centerLinks - 中心连线数据
     * @param {Map} nodeMap - 节点映射
     */
    renderCenterLinks(container, centerLinks, nodeMap) {
        const centerLinksGroup = container
            .append('g')
            .attr('class', 'center-links')
            .selectAll('.center-link')
            .data(centerLinks, (d) => d.__key)
            .join('line')
            .attr('class', 'center-link')
            .attr('stroke', '#4DBFFF')
            .attr('stroke-width', 2);

        this.updateLinkPositions(centerLinksGroup, nodeMap);
    }

    /**
     * 渲染跨组连线
     * @param {d3.Selection} container - 容器
     * @param {Array} interGroupLinks - 跨组连线数据
     * @param {Map} nodeMap - 节点映射
     */
    renderInterGroupLinks(container, interGroupLinks, nodeMap) {
        const interGroupLinksGroup = container
            .append('g')
            .attr('class', 'inter-group-links')
            .selectAll('.inter-link')
            .data(interGroupLinks)
            .join('line')
            .attr('class', 'inter-link')
            .attr('stroke', '#768191')
            .attr('stroke-width', 1)
            .attr('opacity', 0.74);

        this.updateLinkPositions(interGroupLinksGroup, nodeMap);
    }

    /**
     * 渲染组内连线
     * @param {d3.Selection} container - 容器
     * @param {Array} intraGroupLinks - 组内连线数据
     * @param {Map} nodeMap - 节点映射
     */
    renderIntraGroupLinks(container, intraGroupLinks, nodeMap) {
        const intraGroupLinksGroup = container
            .append('g')
            .attr('class', 'intra-group-links')
            .selectAll('.intra-link')
            .data(intraGroupLinks)
            .join('line')
            .attr('class', 'intra-link')
            .attr('stroke', '#8593A7')
            .attr('stroke-width', 1);

        this.updateLinkPositions(intraGroupLinksGroup, nodeMap);
    }

    /**
     * 更新连线位置（多中心图版本）
     * @param {d3.Selection} linkLines - 连线选择器
     * @param {Map} nodeMap - 节点映射
     */
    updateLinkPositions(linkLines, nodeMap) {
        linkLines
            .attr('x1', (d) => {
                const sourceNode = nodeMap.get(d.source);
                return sourceNode ? sourceNode.x : 0;
            })
            .attr('y1', (d) => {
                const sourceNode = nodeMap.get(d.source);
                return sourceNode ? sourceNode.y : 0;
            })
            .attr('x2', (d) => {
                const targetNode = nodeMap.get(d.target);
                return targetNode ? targetNode.x : 0;
            })
            .attr('y2', (d) => {
                const targetNode = nodeMap.get(d.target);
                return targetNode ? targetNode.y : 0;
            });
    }
}

/**
 * 连线渲染器工厂
 */
export class LinkRendererFactory {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 创建单中心图连线渲染器
     * @returns {SingleCenterLinkRenderer}
     */
    createSingleCenterRenderer() {
        return new SingleCenterLinkRenderer(this.configManager);
    }

    /**
     * 创建多中心图连线渲染器
     * @returns {MultiCenterLinkRenderer}
     */
    createMultiCenterRenderer() {
        return new MultiCenterLinkRenderer(this.configManager);
    }
}
