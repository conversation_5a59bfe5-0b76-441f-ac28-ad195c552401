import { computed } from 'vue';
import { algorithmNames } from '@/api/algorithm.js';

/**
 * 算法选项计算 composable
 * 管理算法选项的计算逻辑
 */
export function useAlgorithmOptions(props, formData) {
  // 计算算法选项
  const computedAlgorithmOptions = computed(() => {
    if (props.config.showAlgorithmSelect) {
      // 如果有选中的人像组，使用人像组的算法
      const selectedGroup = props.portraitGroupOptions.find(
        grp => grp.libraryGroupId === formData.value.portraitGroupId
      );
      if (selectedGroup && selectedGroup.algorithmCodes) {
        return selectedGroup.algorithmCodes.map(code => ({
          label: algorithmNames[code] || code,
          value: code
        }));
      }
      // 否则使用默认参数中的算法
      if (props.defaultParams.algorithmCodes) {
        return props.defaultParams.algorithmCodes.map(code => ({
          label: algorithmNames[code] || code,
          value: code
        }));
      }
    }
    return [];
  });

  return {
    computedAlgorithmOptions
  };
}
