<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 16:18:03
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-23 16:29:15
 * @FilePath: \platform-face-web\src\components\Comparison\BaseResultFilter\components\TrapezoidSection.vue
 * @Description: 梯形区域组件-包含自定义按钮或人像组选择的梯形区域
 * 
 * 
-->
<template>
  <div v-if="config.trapezoidSection" class="trapezoid-section">
    <a-form ref="formRef" :model="formData" :colon="false">
      <div class="filter-item trapezoid"
        :style="{ backgroundColor: config.trapezoidSection.bgColor || 'var(--color-bg-1)' }">
        <!-- 自定义按钮 -->
        <CustomButtonWithTooltip v-if="config.trapezoidSection.customButton" :override-style="true"
          :button-bg="config.trapezoidSection.customButton.buttonBg"
          :hover-bg="config.trapezoidSection.customButton.hoverBg"
          :font-color="config.trapezoidSection.customButton.fontColor"
          :hover-font-color="config.trapezoidSection.customButton.hoverFontColor"
          :icon-name="config.trapezoidSection.customButton.iconName">
          <span class="label">{{ config.trapezoidSection.customButton.text }}</span>
        </CustomButtonWithTooltip>

        <!-- 人像组选择 -->
        <template v-else-if="config.trapezoidSection.showPortraitGroup">
          <span class="label">{{ config.trapezoidSection.portraitGroupLabel || '人像组' }}</span>
          <a-select :value="formData.portraitGroupId" placeholder="请选择" style="width: 200px"
            :get-popup-container="getPopupContainer" @change="handlePortraitGroupChange">
            <a-select-option v-for="grp in portraitGroupOptions" :key="grp.libraryGroupId" :value="grp.libraryGroupId">
              {{ grp.libraryGroupName }}
            </a-select-option>
          </a-select>
        </template>
      </div>
    </a-form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  formData: {
    type: Object,
    required: true
  },
  portraitGroupOptions: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(['update:portraitGroupId']);

// 表单引用
const formRef = ref(null);

// 获取弹窗容器
const getPopupContainer = (triggerNode) => {
  return document.querySelector('.popover') || document.body;
};

// 处理人像组变化
const handlePortraitGroupChange = (value) => {
  emits('update:portraitGroupId', value);
};
</script>

<style lang="scss" scoped>
.trapezoid-section {
  // 梯形区域样式
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 20px;

  .label {
    // 标签样式
  }
}

.trapezoid {
  height: 45px;
  position: relative;
  background-color: var(--color-bg-1);
  clip-path: polygon(15% 0, 100% 0, 85% 100%, 0 100%);
  padding: 5px 45px;
  z-index: 1;

  :deep(.ant-select-selector) {
    background-color: var(--color-bg-1) !important;
    background: var(--color-bg-1) !important;
  }

  :deep(.ant-select) {
    border: 2px solid transparent !important;
  }

  :deep(.ant-select-focused) {
    border: 2px solid var(--color-bg-1) !important;

    .ant-select-selector {
      box-shadow: none !important;
    }
  }
}
</style>
