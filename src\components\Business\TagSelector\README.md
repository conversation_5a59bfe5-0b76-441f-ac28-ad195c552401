# TagSelector 标签选择器组件

## 组件描述

TagSelector 是一个功能完整的标签选择器组件，支持搜索、添加、删除标签等功能。组件采用现代化的设计，提供良好的用户体验和可访问性。

## 功能特性

- ✅ 支持搜索标签建议
- ✅ 支持创建新标签
- ✅ 支持删除已选标签
- ✅ 支持最大标签数量限制
- ✅ 支持键盘导航（上下箭头、回车、ESC）
- ✅ 支持防抖搜索
- ✅ 支持多种标签样式主题
- ✅ 支持禁用状态
- ✅ 支持响应式设计
- ✅ 完善的错误处理和用户提示
- ✅ 支持标签去重

## Props 参数

| 参数名        | 类型     | 默认值           | 必填 | 说明                      |
| ------------- | -------- | ---------------- | ---- | ------------------------- |
| modelValue    | Array    | []               | 是   | v-model 绑定的标签数组    |
| onSearch      | Function | -                | 是   | 搜索标签的API函数         |
| onCreate      | Function | null             | 否   | 创建新标签的API函数       |
| placeholder   | String   | '请输入标签名称' | 否   | 输入框占位符              |
| disabled      | Boolean  | false            | 否   | 是否禁用                  |
| maxTags       | Number   | 0                | 否   | 最大标签数量，0表示无限制 |
| debounceDelay | Number   | 300              | 否   | 防抖延迟时间（毫秒）      |
| tagType       | String   | 'default'        | 否   | 标签样式类型              |
| allowCreate   | Boolean  | true             | 否   | 是否允许创建新标签        |
| uniqueKey     | String   | 'id'             | 否   | 标签去重字段              |

### tagType 可选值

- `default` - 默认样式（灰色）
- `primary` - 主要样式（蓝色）
- `success` - 成功样式（绿色）
- `warning` - 警告样式（橙色）
- `error` - 错误样式（红色）

## Events 事件

| 事件名            | 参数                         | 说明                             |
| ----------------- | ---------------------------- | -------------------------------- |
| update:modelValue | tags: Array                  | 标签数组变化时触发               |
| search            | query: String, result: Array | 搜索时触发                       |
| create            | tag: Object                  | 创建新标签时触发                 |
| remove            | tag: Object                  | 删除标签时触发                   |
| change            | tags: Array                  | 标签变化时触发（包括添加和删除） |

## 暴露的方法

通过 ref 可以访问以下方法：

| 方法名 | 参数 | 说明               |
| ------ | ---- | ------------------ |
| focus  | -    | 聚焦到输入框       |
| blur   | -    | 失焦输入框         |
| clear  | -    | 清空输入内容和建议 |

## 使用示例

### 基础用法

```vue
<template>
  <TagSelector v-model="selectedTags" :on-search="handleSearch" :on-create="handleCreate" placeholder="请输入标签名称" />
</template>

<script setup>
import { ref } from 'vue';
import { useTagSelector } from '@/composables/useTagSelector';
import TagSelector from '@/components/Business/TagSelector/index.vue';

const { handleSearchTags, handleCreateTag, handleTagChange } = useTagSelector();

const selectedTags = ref([]);
</script>
```

### 高级用法

```vue
<template>
  <TagSelector
    v-model="selectedTags"
    :on-search="handleSearch"
    :on-create="handleCreate"
    :max-tags="5"
    :debounce-delay="500"
    tag-type="primary"
    unique-key="id"
    placeholder="最多选择5个标签"
    @change="handleTagChange"
    @create="handleTagCreate"
    @remove="handleTagRemove" />
</template>

<script setup>
import { ref } from 'vue';

const selectedTags = ref([
  { id: 1, name: '前端开发' },
  { id: 2, name: 'Vue.js' },
]);

const handleTagChange = (tags) => {
  console.log('标签变化:', tags);
};

const handleTagCreate = (tag) => {
  console.log('创建标签:', tag);
};

const handleTagRemove = (tag) => {
  console.log('删除标签:', tag);
};
</script>
```

### 只读模式

```vue
<template>
  <TagSelector v-model="selectedTags" :on-search="handleSearch" :allow-create="false" disabled placeholder="只读模式" />
</template>
```

## 数据格式

### 标签对象格式

```javascript
{
  id: 1,                    // 唯一标识
  name: '标签名称',          // 标签名称
  description: '标签描述',   // 可选：标签描述
  color: '#1890ff',         // 可选：标签颜色
  isTemp: false            // 可选：是否为临时标签
}
```

### API 函数返回格式

onSearch 函数应返回标签数组：

```javascript
[
  { id: 1, name: '前端开发', description: '前端相关技术' },
  { id: 2, name: 'Vue.js', description: 'Vue框架' },
];
```

onCreate 函数应返回新创建的标签对象：

```javascript
{ id: 3, name: '新标签', description: '新创建的标签' }
```

## 注意事项

1. **onSearch 函数是必需的**，用于获取标签建议
2. **标签去重**基于 uniqueKey 字段，默认为 'id'
3. **防抖搜索**可以通过 debounceDelay 调整延迟时间
4. **键盘导航**支持上下箭头选择建议，回车确认，ESC 关闭
5. **错误处理**组件内部会处理 API 错误并显示用户友好的提示
6. **响应式设计**在移动端会自动调整样式

## 样式定制

组件使用 CSS 变量，可以通过覆盖变量来定制样式：

```css
.tag-selector {
  --tag-bg-color: #f0f0f0;
  --tag-text-color: #333;
  --tag-border-color: #d9d9d9;
}
```

## 兼容性

- Vue 3.2+
- Ant Design Vue 4.0+
- 现代浏览器（IE11+）
