/*
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-16 15:45:56
 * @FilePath: \vue3-js-template-master\src\api\test\demo.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import http from '@/utils/service';

// POST 示例
// export const addLibraries = http.post('/api/libraries', { name: '张三' }).then(resp => {
//   // resp 是反序列化后的 pb 对象
// });



import { PortraitServiceClient } from '@/proto/Portrait_serviceServiceClientPb';
import { CreateLibraryRequest } from '@/proto/portrait_service_pb';

const client = new PortraitServiceClient('http://**************:7790');

const req = new CreateLibraryRequest();
req.setName('张三');

client.createLibrary(req, {}).then(res => {
  console.log('创建成功:', res.toObject());
}).catch(err => {
  console.error('gRPC Error:', err);
});