<!--
 * @Author: yuzhou<PERSON>e
 * @Date: 2025-07-16 16:57:15
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-17 15:26:45
 * @FilePath: \platform-face-web\src\components\Common\ThumbnailDecorator\index.vue
 * @Description: 缩略图装饰器组件 - 在主内容上叠加显示缩略图，支持点击预览
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="thumbnail-decorator">
    <!-- 主内容插槽 -->
    <slot></slot>

    <!-- 缩略图容器 -->
    <div v-if="showOriginalPhoto" class="thumbnail-wrapper" :class="{ 'thumbnail-loading': isImageLoading }">
      <img ref="thumbnailRef" :src="thumbnailSrc" :alt="alt" class="thumbnail" draggable="false"
        @click="debouncedHandleImageClick" @load="handleImageLoad" @error="handleImageError" />
    </div>

    <!-- 图片预览器 -->
    <ImagePreviewer :images="[thumbnailSrc]" v-model:visible="modalVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ImagePreviewer from '@/components/Common/ImagePreviewer/index.vue';
import { debounce } from '@/utils/tool.js';

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 是否显示缩略图 */
  showOriginalPhoto: {
    type: Boolean,
    default: true
  },
  /** 缩略图源地址 */
  thumbnailSrc: {
    type: String,
    required: true,
  },
  /** 图片alt属性 */
  alt: {
    type: String,
    default: '缩略图'
  },
  /** 缩略图宽度 */
  imageWidth: {
    type: String,
    required: true,
  },
  /** 缩略图高度 */
  imageHeight: {
    type: String,
    required: true,
  },
  /** 距离底部的距离 */
  bottom: {
    type: String,
    default: '0px',
  },
  /** 距离右侧的距离 */
  right: {
    type: String,
    default: '0px',
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'image-click',
  'image-load',
  'image-error'
]);

// 响应式数据
const modalVisible = ref(false);
const isImageLoading = ref(true);
const thumbnailRef = ref(null);
const intersectionObserver = ref(null);

/**
 * 处理图片点击事件
 * @param {Event} e - 点击事件对象
 */
const handleImageClick = (e) => {
  e.preventDefault();
  modalVisible.value = true;
  emit('image-click', e);
};

/**
 * 防抖处理的图片点击事件
 */
const debouncedHandleImageClick = debounce(handleImageClick, 200);

/**
 * 处理图片加载完成
 */
const handleImageLoad = () => {
  isImageLoading.value = false;
  emit('image-load');
};

/**
 * 处理图片加载错误
 */
const handleImageError = () => {
  isImageLoading.value = false;
  emit('image-error');
};

/**
 * 初始化懒加载观察器
 */
const initLazyLoading = () => {
  if (!thumbnailRef.value || !('IntersectionObserver' in window)) {
    return;
  }

  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // 图片进入视口，开始加载
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            intersectionObserver.value.unobserve(img);
          }
        }
      });
    },
    {
      rootMargin: '50px'
    }
  );

  intersectionObserver.value.observe(thumbnailRef.value);
};

// 生命周期钩子
onMounted(() => {
  initLazyLoading();
});

onUnmounted(() => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }
});
</script>

<style scoped lang="scss">
.thumbnail-decorator {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.thumbnail-wrapper {
  position: absolute;
  bottom: v-bind(bottom);
  right: v-bind(right);
  width: v-bind(imageWidth);
  height: v-bind(imageHeight);
  border: 1px solid #768191;
  border-radius: 4px;
  z-index: 3;
  overflow: hidden;

  &.thumbnail-loading {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
</style>
