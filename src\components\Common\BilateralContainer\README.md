# BilateralContent 左右布局组件

一个支持可调节宽度和独立滚动条的左右布局组件。

## 特性

- ✅ 左右各占一半空间（默认）
- ✅ 中间可拖拽的分栏线条
- ✅ 两边独立的滚动条
- ✅ 使用插槽实现两边内容
- ✅ 支持最小宽度限制
- ✅ 响应式设计
- ✅ 美观的拖拽手柄

## 使用方法

### 基本用法

```vue
<template>
  <BilateralContent>
    <template #left>
      <!-- 左侧内容 -->
      <div>左侧面板内容</div>
    </template>
    
    <template #right>
      <!-- 右侧内容 -->
      <div>右侧面板内容</div>
    </template>
  </BilateralContent>
</template>

<script>
import BilateralContent from './BilateralContent.vue'

export default {
  components: {
    BilateralContent
  }
}
</script>
```

### 高级用法

```vue
<template>
  <BilateralContent 
    :min-width="200" 
    :initial-left-ratio="0.6"
  >
    <template #left>
      <!-- 左侧内容 -->
    </template>
    
    <template #right>
      <!-- 右侧内容 -->
    </template>
  </BilateralContent>
</template>
```

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `minWidth` | Number | 200 | 最小宽度限制（像素） |
| `initialLeftRatio` | Number | 0.5 | 初始左侧宽度比例（0-1） |

## 插槽

| 插槽名 | 说明 |
|--------|------|
| `left` | 左侧面板内容 |
| `right` | 右侧面板内容 |

## 样式特性

- 自定义滚动条样式
- 拖拽时的视觉反馈
- 平滑的过渡动画
- 响应式布局

## 注意事项

1. 组件需要设置高度才能正常工作
2. 建议在容器中设置 `height: 100%` 或固定高度
3. 拖拽时会阻止文本选择，拖拽结束后恢复正常
4. 组件会自动处理窗口大小变化

## 示例

查看 `BilateralContentExample.vue` 文件获取完整的使用示例。 