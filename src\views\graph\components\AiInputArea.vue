<template>
  <div class="chat-input-area">
    <div class="chat-input-area-content">
      <div class="chat-input-area-content-input">
        <a-textarea v-model="inputValue" placeholder="请输入，Enter键发送，Shift+Enter键换行"
          :auto-size="{ minRows: 3, maxRows: 4 }" @keydown="handleKeyDown" />
      </div>
      <div class="chat-input-area-content-buttons">
        <div>
          <a-button>给我讲讲</a-button>
          <a-button>以图搜图</a-button>
        </div>
        <div class="flex">
          <CommonUpload :multiple="true" :before-upload="handleBeforeUpload">
          <a-button>上传</a-button>
          </CommonUpload>
          <a-button style="margin-left: 8px;">发送</a-button>
        </div>
      </div>
      <div class="chat-input-area-images">
        <transition-group name="image-fade" tag="div" class="images-container">
          <div v-for="(item, index) in imageList" :key="item.id" class="image-item">
            <img :src="item.url" :alt="item.name" @click="handleImageClick(index)">
            <div class="delete-btn" @click="handleRemoveImage(index)">×</div>
          </div>
        </transition-group>
      </div>
      
      <!-- 视频列表 -->
      <div v-if="videoList.length > 0" class="chat-input-area-videos">
        <transition-group name="video-fade" tag="div" class="videos-container">
          <div v-for="(item, index) in videoList" :key="item.id" class="video-item">
            <video :src="item.url" class="video-preview" @click.stop="handleVideoClick(item)" muted></video>
            <div class="play-overlay" @click.stop="handleVideoClick(item)">
              <div class="play-icon">▶</div>
            </div>
            <div class="delete-btn" @click="handleRemoveVideo(index)">×</div>
            <div class="file-name">{{ item.name }}</div>
          </div>
        </transition-group>
      </div>
      
      <!-- 其他文件列表 -->
      <div v-if="fileList.length > 0" class="chat-input-area-files">
        <transition-group name="file-fade" tag="div" class="files-container">
          <div v-for="(item, index) in fileList" :key="item.id" class="file-item">
            <div class="file-icon">📄</div>
            <div class="file-name">{{ item.name }}</div>
            <div class="file-size">{{ formatFileSize(item.size) }}</div>
            <div class="delete-btn" @click="handleRemoveFile(index)">×</div>
          </div>
        </transition-group>
    </div>
    </div>
    <ImagePreviewer v-model:visible="showPreview" :images="showImageList" :initial-index="currentImageIndex"
      @change="handleImageChange" />
    <VideoPlayer 
      v-model:visible="showVideoPreview" 
      :videos="showVideoList"
      :initial-index="currentVideoIndex"
    />
  </div>
</template>

<script setup>

import { reactive, ref, computed, nextTick } from 'vue'
const inputValue = ref('')
const imageList = reactive([])
const videoList = reactive([])
const fileList = reactive([])
const currentImageIndex = ref(0)
const showPreview = ref(false)
const showVideoPreview = ref(false)
const currentVideoIndex = ref(0)

const showImageList = computed(() => {
  return imageList.map(item => item.url)
})

const showVideoList = computed(() => {
  return videoList.map(item => ({
    url: item.url,
    title: item.name,
    size: item.size,
    type: item.type
  }))
})

function handleImageClick(index) {
  currentImageIndex.value = index
  showPreview.value = true
}

function handleVideoClick(video) {
  
  // 找到视频在列表中的索引
  const index = videoList.findIndex(item => item.id === video.id)
  currentVideoIndex.value = index >= 0 ? index : 0
  
  // 先关闭弹窗，确保状态重置
  showVideoPreview.value = false
  
  // 使用nextTick确保DOM更新后再设置新数据
  nextTick(() => {
    showVideoPreview.value = true
  })
}

function handleImageChange(index) {
  currentImageIndex.value = index
}
function handleKeyDown(e) {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    if (inputValue.value.trim()) {
      // 这里可以添加发送逻辑
      // 例如 emit('send', inputValue.value)
      inputValue.value = ''
    }
  }
}

// 处理文件上传前的逻辑
function handleBeforeUpload(file) {
  // 判断文件类型
  const fileType = file.type
  const isImage = fileType.startsWith('image/')
  const isVideo = fileType.startsWith('video/')
  
  // 创建本地预览URL
  const reader = new FileReader()
  reader.onload = (e) => {
    const newFile = {
      url: e.target.result,
      id: Date.now(),
      name: file.name,
      type: fileType,
      size: file.size
    }
    
    // 根据文件类型分别存储
    if (isImage) {
      imageList.push(newFile)
    } else if (isVideo) {
      videoList.push(newFile)
    } else {
      fileList.push(newFile)
    }
  }
  reader.readAsDataURL(file)

  // 返回false阻止默认上传行为，因为我们使用本地预览
  return false
}

// 处理删除图片
function handleRemoveImage(index) {
  imageList.splice(index, 1)
}

// 处理删除视频
function handleRemoveVideo(index) {
  videoList.splice(index, 1)
}

// 处理删除文件
function handleRemoveFile(index) {
  fileList.splice(index, 1)
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

</script>

<style lang="scss" scoped>
.chat-input-area {
  border: 2px solid #9c9c9c;
  border-radius: 10px;
  padding: 15px;

  &-content {
    &-buttons {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      padding-bottom: 20px;
      border-bottom: 1px solid #9c9c9c;

      .ant-btn {
        margin-left: 8px;

        &:first-of-type {
          margin-left: 0;
        }

        ;
      }
    }
  }

  &-images {
    margin-top: 20px;

    .images-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .image-item {
      position: relative;
      width: 100px;
      height: 100px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px;
        cursor: pointer;
        transition: 0.3s all;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.7);
        }
      }

      .delete-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        background: rgba(255, 0, 0, 0.8);
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 18px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: 0.3s all;
        z-index: 10;
        opacity: 0;
        visibility: hidden;

        &:hover {
          background: rgba(255, 0, 0, 1);
          transform: scale(1.1);
        }
      }

      &:hover .delete-btn {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  &-videos {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #9c9c9c;

    h4 {
      margin-bottom: 10px;
      font-size: 16px;
      color: #333;
    }

    .videos-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .video-item {
      position: relative;
      width: 150px;
      height: 100px;
      border: 1px solid #ccc;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f0f0;

      .video-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .play-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 5;
        transition: 0.3s all;

        .play-icon {
          font-size: 40px;
          color: white;
          opacity: 0.8;
          transition: 0.3s all;
        }

        &:hover {
          background: rgba(0, 0, 0, 0.7);

          .play-icon {
            opacity: 1;
            transform: scale(1.1);
          }
        }
      }

      .delete-btn {
        position: absolute;
        top: 0px;
        right: 0px;
        width: 20px;
        height: 20px;
        background: rgba(255, 0, 0, 0.8);
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 18px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: 0.3s all;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;

        &:hover {
          background: rgba(255, 0, 0, 1);
          transform: scale(1.1);
        }
      }

      &:hover .delete-btn {
        opacity: 1;
        visibility: visible;
      }

      .file-name {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 5px 10px;
        font-size: 12px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
      }
    }
  }

  &-files {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #9c9c9c;

    h4 {
      margin-bottom: 10px;
      font-size: 16px;
      color: #333;
    }

    .files-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .file-item {
      position: relative;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: #f0f0f0;
      border-radius: 8px;
      border: 1px solid #ccc;

      .file-icon {
        font-size: 24px;
        color: #555;
      }

      .file-name {
        flex-grow: 1;
        font-size: 14px;
        color: #333;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .file-size {
        font-size: 12px;
        color: #666;
      }

      .delete-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        background: rgba(255, 0, 0, 0.8);
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 18px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: 0.3s all;
        z-index: 10;
        opacity: 0;
        visibility: hidden;

        &:hover {
          background: rgba(255, 0, 0, 1);
          transform: scale(1.1);
        }
      }

      &:hover .delete-btn {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  // 图片过渡动画
  .image-fade-enter-active,
  .image-fade-leave-active {
    transition: all 0.5s ease;
  }

  .image-fade-enter-from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  .image-fade-leave-to {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }

  .image-fade-move {
    transition: transform 0.5s ease;
  }

  // 视频过渡动画
  .video-fade-enter-active,
  .video-fade-leave-active {
    transition: all 0.5s ease;
  }

  .video-fade-enter-from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  .video-fade-leave-to {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }

  .video-fade-move {
    transition: transform 0.5s ease;
  }

  // 文件过渡动画
  .file-fade-enter-active,
  .file-fade-leave-active {
    transition: all 0.5s ease;
  }

  .file-fade-enter-from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  .file-fade-leave-to {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }

  .file-fade-move {
    transition: transform 0.5s ease;
  }
}

/* 强力覆盖所有相关输入框背景和边框 */
:deep(.ant-input-textarea),
:deep(.ant-input-textarea .ant-input),
:deep(.ant-input-textarea textarea),
:deep(.ant-input),
:deep(textarea) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  resize: none !important;
  padding: 0;
}
</style>