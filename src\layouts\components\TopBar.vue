<template>
  <header class="top-bar-wrap" :style="topBarStyle">
    <div class="top-bar-left">
      <slot name="left"><top-bar-left /></slot>
    </div>
    <div class="top-bar-center">
      <slot name="center">
        <top-bar-center />
      </slot>
    </div>
    <div class="top-bar-right">
      <slot name="right"><top-bar-right /></slot>
    </div>
  </header>
</template>

<script setup>
import TopBarLeft from './TopBarLeft.vue';
import TopBarCenter from './TopBarCenter.vue';
import TopBarRight from './TopBarRight.vue';
import { computed } from 'vue';
import theme from '@/theme';

const topBarStyle = computed(() => ({
  background: theme.topBarBg,
  color: theme.topBarColor,
}));
</script>

<style lang="scss" scoped>
.top-bar-wrap {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  background: transparent;

  .top-bar-left,
  .top-bar-center,
  .top-bar-right {
    display: flex;
    align-items: center;
    height: 100%;
  }
  .top-bar-left {
    flex: 0 0 220px;
    justify-content: flex-start;
    padding-left: 16px;
  }
  .top-bar-center {
    flex: 1;
    justify-content: center;
    font-size: 1.1rem;
    color: #b6cfff;
  }
  .top-bar-right {
    flex: 0 0 220px;
    justify-content: flex-end;
    padding-right: 16px;
  }
}
</style> 