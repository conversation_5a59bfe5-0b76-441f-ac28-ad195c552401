import { pathToRegexp } from 'path-to-regexp';
import auth from '@/utils/auth';
import { DEFAULT_CONFIG } from '@/constants';

const getRoutes = (routerData, path = '', newRouterData = []) => {
  if (!routerData || !routerData.length) return newRouterData;

  routerData.forEach((route) => {
    // 跳过 path-to-regexp 不支持的 path
    if (route.path.includes('*')) {
      return;
    }
    const newPath = path && !path.endsWith('/') ? `${path}/${route.path}` : `${path}${route.path}`;
    newRouterData.push({
      path: newPath,
      regexp: pathToRegexp(newPath),
    });

    if (route.children) {
      getRoutes(route.children, newPath, newRouterData);
    }
  });

  return newRouterData;
};

const checkRoute = (routerData, actualPath) => {
  return routerData.some((route) => route.regexp.test(actualPath));
};

/**
 * 路由守卫
 * @param {*} router 路由实例
 * @param {*} param1 包含 to, _from, next 的对象
 */
const layoutRouterBeforeEach = async (router, { to, _from, next }) => {
  const routerData = getRoutes(router.options.routes);

  if (!checkRoute(routerData, to.path)) {
    next('/not-found');
    return;
  }

  if (!DEFAULT_CONFIG.isAuthRequired) {
    next();
    return;
  }

  if (to.path.includes('user')) {
    next();
    return;
  }

  // // 权限检查
  // let authCheckRedirectUrl = null;
  // await auth.checkAuth({
  //   currentLocation: to,
  //   onFailure: ({ redirectUrl }) => {
  //     authCheckRedirectUrl = redirectUrl;
  //   },
  // });

  // if (authCheckRedirectUrl) {
  //   if (authCheckRedirectUrl.startsWith('http')) {
  //     window.location.href = authCheckRedirectUrl;
  //   } else {
  //     next(authCheckRedirectUrl);
  //   }
  // } else {
  //   next();
  // }
  next();
};

export default layoutRouterBeforeEach;

