<!--
 * @Author: y<PERSON><PERSON>ism<PERSON>
 * @Date: 2025-07-24 10:39:04
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 14:40:37
 * @FilePath: \platform-face-web\src\components\Business\TagSelector\components\TagInput.vue
 * @Description: TagSelector 输入框组件
 * 负责搜索输入和添加标签功能
 * 
-->
<template>
  <div class="tag-input">
    <a-input ref="inputRef" :value="searchText" :placeholder="placeholder" :disabled="disabled" :loading="loading"
      allow-clear @input="handleInput" @keydown.enter.prevent="handleAddTag" @focus="handleInputFocus"
      @blur="handleInputBlur">
      <template #prefix>
        <SvgIcon icon-class="search" size="14px" />
      </template>
      <template #suffix>
        <SvgIcon v-if="!disabled && searchText.trim()" icon-class="add" size="14px" class="tag-input__add-icon"
          @click.stop="handleAddTag" />
      </template>
    </a-input>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import SvgIcon from '@/components/Common/SvgIcon/index.vue';

// Props 定义
const props = defineProps({
  // 搜索文本
  searchText: {
    type: String,
    default: ''
  },

  // 输入框占位符
  placeholder: {
    type: String,
    default: '请输入标签名称'
  },

  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },

  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
});

// 事件定义
const emit = defineEmits([
  'input',
  'add-tag',
  'focus',
  'blur'
]);

// 输入框引用
const inputRef = ref(null);

// 事件处理方法
const handleInput = (e) => {
  const value = e.target.value;
  emit('input', value);
};

const handleAddTag = () => {
  emit('add-tag');
};

const handleInputFocus = () => {
  emit('focus');
};

const handleInputBlur = () => {
  emit('blur');
};

// 暴露给父组件的方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
});
</script>

<style lang="scss" scoped>
.tag-input {
  position: relative;
  width: 100%;

  &__add-icon {
    cursor: pointer;
    color: #1890ff;
    transition: color 0.2s;

    &:hover {
      color: #40a9ff;
    }
  }
}

// 深度选择器样式
:deep(.ant-input-affix-wrapper) {
  transition: all 0.2s;

  &:hover {
    border-color: #40a9ff;
  }

  &:focus,
  &.ant-input-affix-wrapper-focused {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

:deep(.ant-input) {
  &::placeholder {
    color: #bfbfbf;
  }
}
</style>
