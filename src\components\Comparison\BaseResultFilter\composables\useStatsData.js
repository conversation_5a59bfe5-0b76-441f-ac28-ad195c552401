/**
 * 统计数据处理 composable
 * 管理统计数据的获取和处理逻辑
 */
export function useStatsData(props) {
  // 获取统计数据值
  const getStatValue = (stat) => {
    if (stat.key && props.statsData[stat.key] !== undefined) {
      return props.statsData[stat.key];
    }
    return stat.defaultValue || 0;
  };

  // 获取弹窗容器
  const getPopupContainer = (triggerNode) => {
    return document.querySelector('.popover') || document.body;
  };

  return {
    getStatValue,
    getPopupContainer
  };
}
