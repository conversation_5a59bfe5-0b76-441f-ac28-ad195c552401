export default [
  {
    path: '/:pathMatch(.*)*', // 防止浏览器刷新时路由未找到警告提示:No match found for location with path "/xxx"
    meta: {
      title: '404',
    },
    component: () => import('@/components/Error/404.vue'),
  },
  {
    path: '/not-found', // 防止浏览器刷新时路由未找到警告提示:No match found for location with path "/xxx"
    meta: {
      title: 'NotFound',
    },
    component: () => import('@/components/Error/404.vue'),
  },
  {
    path: '/403',
    meta: {
      title: '403',
    },
    component: () => import('@/components/Error/403.vue'),
  },
]; 