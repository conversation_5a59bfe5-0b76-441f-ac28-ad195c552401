/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-17 16:15:54
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-17 16:23:41
 * @FilePath: \platform-face-web\src\components\Common\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineAsyncComponent } from 'vue'

export default {
  install(app) {
    // 自动导入Common目录下所有.vue文件
    const modules = import.meta.glob('./**/*.vue')
    Object.entries(modules).forEach(([path, loader]) => {
      // 组件名：如果文件名为Index.vue，则用目录名，否则目录名+文件名
      const match = path.match(/\.\/([\w-]+)\/([\w-]+)\.vue$/)
      let name = ''
      if (match) {
        if (match[2].toLowerCase() === 'index') {
          name = match[1]
        } else {
          name = match[1] + match[2].replace(/^([a-z])/, (s) => s.toUpperCase())
        }
      } else {
        // 兼容Common目录下一级.vue文件
        const single = path.match(/\.\/([\w-]+)\.vue$/)
        if (single) name = single[1]
      }
      if (name) {
        app.component(name, defineAsyncComponent(loader))
      }
    })
  }
}
