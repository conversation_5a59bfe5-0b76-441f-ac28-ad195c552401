/**
 * 图表工具函数集合
 * 提供柱状图组件的通用工具函数
 */

/**
 * 计算柱子间隔
 * @param {number} chartWidth - 图表宽度
 * @param {number} rectWidth - 每个矩形的宽度
 * @param {number} totalRects - 矩形总数
 * @param {number} groups - 组数
 * @param {number} rectsPerGroup - 每组矩形数
 * @returns {number} 间隔值
 */
export const calculateInterval = (chartWidth, rectWidth, totalRects, groups, rectsPerGroup) => {
  const num = chartWidth - rectWidth * totalRects;
  const interval = num / (rectsPerGroup * groups + groups - 2);
  return interval;
};

/**
 * 添加参考线
 * @param {d3.Selection} g - D3选择器
 * @param {Array} lines - 参考线配置数组
 * @param {number} chartWidth - 图表宽度
 * @param {number} altitudeHeight - 高度的一半（用于计算Y坐标）
 */
export const addReferenceLines = (g, lines, chartWidth, altitudeHeight = 100) => {
  lines.forEach((line) => {
    const offset = line?.offset ? line.offset : 0;
    g.append('line')
      .attr('x1', line.x1 || 0)
      .attr('x2', line.x2 || chartWidth)
      .attr('y1', line.value * altitudeHeight + offset)
      .attr('y2', line.value * altitudeHeight + offset)
      .attr('stroke', line.color)
      .attr('stroke-width', line.width)
      .attr('stroke-dasharray', line.dashArray);
  });
};

/**
 * 创建渐变定义
 * @param {d3.Selection} svg - SVG选择器
 * @returns {Object} 渐变对象集合
 */
export const createGradients = (svg) => {
  const defs = svg.append('defs');

  // 绿色渐变
  const greenGradient = defs
    .append('linearGradient')
    .attr('id', 'greenGradient')
    .attr('x1', '0%')
    .attr('y1', '0%')
    .attr('x2', '0%')
    .attr('y2', '100%');
  greenGradient.append('stop').attr('offset', '0%').attr('stop-color', '#90FBCA').attr('stop-opacity', 1);
  greenGradient.append('stop').attr('offset', '100%').attr('stop-color', '#263235').attr('stop-opacity', 1);

  // 蓝色渐变
  const blueGradient = defs.append('linearGradient').attr('id', 'blueGradient').attr('x1', '0%').attr('y1', '0%').attr('x2', '0%').attr('y2', '100%');
  blueGradient.append('stop').attr('offset', '0%').attr('stop-color', '#263235').attr('stop-opacity', 1);
  blueGradient.append('stop').attr('offset', '100%').attr('stop-color', '#4DBDFE').attr('stop-opacity', 1);

  // 蓝绿渐变
  const greenAndBlue = defs.append('linearGradient').attr('id', 'greenAndBlue').attr('x1', '0%').attr('y1', '0%').attr('x2', '100%').attr('y2', '0%');
  greenAndBlue.append('stop').attr('offset', '0%').attr('stop-color', '#90FBCA').attr('stop-opacity', 1);
  greenAndBlue.append('stop').attr('offset', '100%').attr('stop-color', '#4DBFFF').attr('stop-opacity', 1);

  // 黄色渐变
  const yellowGradient = defs
    .append('linearGradient')
    .attr('id', 'yellowGradient')
    .attr('x1', '0%')
    .attr('y1', '0%')
    .attr('x2', '0%')
    .attr('y2', '100%');
  yellowGradient.append('stop').attr('offset', '0%').attr('stop-color', '#ffd86b').attr('stop-opacity', 1);
  yellowGradient.append('stop').attr('offset', '100%').attr('stop-color', '#1f242b').attr('stop-opacity', 1);

  // 第二种蓝色渐变（用于AlgorithmTripleComparisonBar）
  const blueGradient2 = defs
    .append('linearGradient')
    .attr('id', 'blueGradient2')
    .attr('x1', '0%')
    .attr('y1', '0%')
    .attr('x2', '0%')
    .attr('y2', '100%');
  blueGradient2.append('stop').attr('offset', '0%').attr('stop-color', '#4E7AB4').attr('stop-opacity', 1);
  blueGradient2.append('stop').attr('offset', '100%').attr('stop-color', '#1F242B').attr('stop-opacity', 1);

  // 紫色渐变（用于AlgorithmTripleComparisonBar）
  const purpleGradient = defs
    .append('linearGradient')
    .attr('id', 'purpleGradient')
    .attr('x1', '0%')
    .attr('y1', '0%')
    .attr('x2', '0%')
    .attr('y2', '100%');
  purpleGradient.append('stop').attr('offset', '0%').attr('stop-color', '#9490FB').attr('stop-opacity', 1);
  purpleGradient.append('stop').attr('offset', '100%').attr('stop-color', '#1F242B').attr('stop-opacity', 1);

  return {
    greenGradient,
    blueGradient,
    greenAndBlue,
    yellowGradient,
    blueGradient2,
    purpleGradient,
  };
};

/**
 * 默认参考线配置
 */
export const DEFAULT_REFERENCE_LINES = [
  { value: 0, color: '#4dbfff', dashArray: '', width: 2, x1: 34 }, // x坐标轴蓝色实线
  { value: 0, color: '#90fbc9', dashArray: '', width: 1, x1: 34 }, // x坐标轴绿色实线
  { value: -1, color: '#90fbc966', dashArray: '', width: 1, offset: -1 }, // 柱子顶部绿色实线，默认从0开始
  { value: 1, color: '#4dbfff66', dashArray: '', width: 1, offset: 1 }, // 柱子顶部蓝色实线，默认从0开始
  { value: -1, color: '#90fbc9', dashArray: '', width: 1, x1: -5, x2: 22 }, // 柱子顶部y轴到绿色实线的线
  { value: 1, color: '#4dbfff', dashArray: '', width: 1, x1: -5, x2: 34 }, // 柱子顶部y轴到蓝色实线的线
  { value: 0, color: '#3d4b5c', dashArray: '', width: 1, x1: -134, x2: -32 }, // 图表左侧多选框的分界线
  { value: -0.85, color: '#90fbc9', dashArray: '5,5', width: 1, x1: 34 }, // 高阈值线
  { value: -0.75, color: '#69b293', dashArray: '5,5', width: 1, x1: 34 }, // 中阈值线
  { value: -0.65, color: '#ff7f6b', dashArray: '5,5', width: 1, x1: 34 }, // 低阈值线
];

/**
 * 过滤参考线（根据最大分数）
 * @param {Array} referenceLines - 参考线配置
 * @param {number} maxScore - 最大分数
 * @returns {Array} 过滤后的参考线
 */
export const filterReferenceLines = (referenceLines, maxScore) => {
  return referenceLines.filter((line) => {
    if (maxScore < 0.65) {
      // 如果 maxScore 低于 0.65，移除高阈值线和中阈值线
      return line.value !== -0.85 && line.value !== -0.75;
    } else if (maxScore < 0.75) {
      // 如果 maxScore 低于 0.75，移除高阈值线
      return line.value !== -0.85;
    } else {
      // 其他情况不处理，保留所有线
      return true;
    }
  });
};

/**
 * 处理数据，添加分组信息
 * @param {Array} data - 原始数据
 * @param {number} rectsPerGroup - 每组矩形数
 * @returns {Array} 处理后的数据
 */
export const processChartData = (data, rectsPerGroup) => {
  return data.map((d, i) => ({
    ...d,
    xLabel: `TOP${d.scoreRank}`,
    inGroupIndex: i % rectsPerGroup,
    groupIndex: Math.floor(i / rectsPerGroup),
  }));
};

/**
 * 计算X坐标位置
 * @param {Object} d - 数据项
 * @param {number} startX - 起始X坐标
 * @param {number} rectWidth - 矩形宽度
 * @param {number} rectsPerGroup - 每组矩形数
 * @param {number} interval - 间隔
 * @returns {number} X坐标
 */
export const calculateXPosition = (d, startX, rectWidth, rectsPerGroup, interval) => {
  return startX + d.groupIndex * (rectWidth * rectsPerGroup + interval) + d.inGroupIndex * rectWidth;
};

/**
 * 计算组间标准差（用于AlgorithmTripleComparisonBar）
 * @param {Array} data - 数据数组
 * @returns {Array} 标准差数组
 */
export const calculateGroupStdDev = (data) => {
  const groups = {};
  // 按 portraitId 分组
  data.forEach((item) => {
    if (!groups[item.portrait.portraitId]) {
      groups[item.portrait.portraitId] = [];
    }
    groups[item.portrait.portraitId].push(item);
  });

  const stdDevs = [];
  // 计算每组标准差
  Object.values(groups).forEach((group) => {
    const scores = group.map((item) => item.score);
    const mean = scores.reduce((sum, val) => sum + val, 0) / scores.length;
    const variance = scores.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / scores.length;
    stdDevs.push(Math.sqrt(variance));
  });

  return stdDevs;
};

/**
 * 获取算法填充颜色（用于AlgorithmTripleComparisonBar）
 * @param {Object} d - 数据项
 * @returns {string} 颜色URL
 */
export const getAlgorithmFillColor = (d) => {
  if (d.algorithmCode === 'ci') {
    return 'url(#blueGradient2)';
  } else if (d.algorithmCode === 'an') {
    return 'url(#purpleGradient)';
  }
  return 'url(#greenGradient)';
};

/**
 * 默认边距配置
 */
export const DEFAULT_MARGIN = { top: 30, right: 200, bottom: 20, left: 160 };

/**
 * 通用柱状图绘制工具函数
 */

/**
 * 创建基础矩形元素
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {d3.Selection} 矩形选择器
 */
export const createBaseRect = (container, config) => {
  const { className = '', x = 0, y = 0, width = 0, height = 0, fill = 'none', dataId = null, ...attrs } = config;

  const rect = container
    .append('rect')
    .attr('class', className)
    .attr('x', x)
    .attr('y', y)
    .attr('width', width)
    .attr('height', height)
    .attr('fill', fill);

  if (dataId) {
    rect.attr('data-id', dataId);
  }

  // 应用其他属性
  Object.entries(attrs).forEach(([key, value]) => {
    rect.attr(key, value);
  });

  return rect;
};

/**
 * 创建基础文本元素
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {d3.Selection} 文本选择器
 */
export const createBaseText = (container, config) => {
  const { className = '', x = 0, y = 0, text = '', fontSize = '12px', fill = '#000', textAnchor = 'middle', dataId = null, ...attrs } = config;

  const textElement = container
    .append('text')
    .attr('class', className)
    .attr('x', x)
    .attr('y', y)
    .attr('fill', fill)
    .text(text)
    .style('font-size', fontSize)
    .style('text-anchor', textAnchor);

  if (dataId) {
    textElement.attr('data-id', dataId);
  }

  // 应用其他属性
  Object.entries(attrs).forEach(([key, value]) => {
    textElement.attr(key, value);
  });

  return textElement;
};

/**
 * 创建基础图像元素
 * @param {d3.Selection} container - 容器选择器
 * @param {Object} config - 配置对象
 * @returns {d3.Selection} 图像选择器
 */
export const createBaseImage = (container, config) => {
  const { className = '', x = 0, y = 0, width = 16, height = 16, href = '', dataId = null, ...attrs } = config;

  const image = container
    .append('image')
    .attr('class', className)
    .attr('x', x)
    .attr('y', y)
    .attr('width', width)
    .attr('height', height)
    .attr('xlink:href', href);

  if (dataId) {
    image.attr('data-id', dataId);
  }

  // 应用其他属性
  Object.entries(attrs).forEach(([key, value]) => {
    image.attr(key, value);
  });

  return image;
};

/**
 * 创建柱状图的标准配色方案
 */
export const BAR_CHART_COLORS = {
  BACKGROUND: '#37404D',
  TEXT_PRIMARY: '#000',
  TEXT_SECONDARY: '#727982',
  WHITE: '#ffffff',
  TRANSPARENT: 'transparent',
  GRADIENTS: {
    GREEN: 'url(#greenGradient)',
    BLUE: 'url(#blueGradient)',
    GREEN_AND_BLUE: 'url(#greenAndBlue)',
    YELLOW: 'url(#yellowGradient)',
    BLUE2: 'url(#blueGradient2)',
    PURPLE: 'url(#purpleGradient)',
  },
};

/**
 * 创建标准的柱状图配置
 * @param {Object} overrides - 覆盖配置
 * @returns {Object} 柱状图配置
 */
export const createBarConfig = (overrides = {}) => {
  const defaultConfig = {
    rectWidth: CHART_CONSTANTS.RECT_WIDTH,
    fontSize: '12px',
    iconSize: 16,
    colors: BAR_CHART_COLORS,
    ...overrides,
  };

  return defaultConfig;
};

/**
 * 图表常量
 */
export const CHART_CONSTANTS = {
  RECT_WIDTH: 32,
  START_X: 34,
  VARIANCE_HEIGHT: 50, // 用于AlgorithmTripleComparisonBar
  RECTS_PER_GROUP_SINGLE: 5, // CompareResultsBarGraph
  RECTS_PER_GROUP_TRIPLE: 3, // AlgorithmTripleComparisonBar
};
