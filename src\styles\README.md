# common.scss 公共样式说明（全量）

## 文件简介

`common.scss` 是本项目的全局通用样式文件，包含大量高频使用的 CSS 工具类和基础样式，旨在提升开发效率、统一团队风格、减少重复代码。

---

## 样式类别与用法说明

### 1. 滚动条美化
- 统一美化 Chrome/Edge/Firefox 滚动条样式。
- **用法**：全局生效，无需手动添加类名。

---

### 2. 清除浮动
- `.clearfix`：用于清除浮动影响。
- **用法**：
  ```html
  <div class="clearfix">...</div>
  ```

---

### 3. 文本溢出省略
- `.text-ellipsis`：单行文本溢出省略号
- `.text-ellipsis-2`：多行（2行）文本溢出省略
- `.text-ellipsis-3`：多行（3行）文本溢出省略
- **用法**：
  ```html
  <div class="text-ellipsis">单行省略</div>
  <div class="text-ellipsis-2">多行省略</div>
  ```

---

### 4. 禁止选中
- `.no-select`：禁止用户选中内容
- **用法**：
  ```html
  <div class="no-select">不可选中内容</div>
  ```

---

### 5. Flex 布局工具类
- `.flex`：display:flex
- `.flex-center`：水平垂直居中
- `.flex-between`：两端对齐
- `.flex-column`：纵向排列
- **用法**：
  ```html
  <div class="flex flex-center">...</div>
  <div class="flex-between">...</div>
  <div class="flex-column">...</div>
  ```

---

### 6. 通用按钮样式
- `.btn-primary`：主色渐变按钮
- `.btn-danger`：红色危险按钮
- `.btn-link`：无底色链接按钮
- **用法**：
  ```html
  <button class="btn-primary">主按钮</button>
  <button class="btn-danger">危险按钮</button>
  <button class="btn-link">链接按钮</button>
  ```

---

### 7. 卡片阴影/圆角
- `.card-shadow`：卡片阴影+圆角
- `.rounded` `.rounded-sm` `.rounded-md` `.rounded-lg` `.rounded-pill`：不同圆角
- **用法**：
  ```html
  <div class="card-shadow rounded">卡片内容</div>
  <div class="rounded-lg">大圆角</div>
  ```

---

### 8. 分割线
- `.divider`：通用分割线
- **用法**：
  ```html
  <div class="divider"></div>
  ```

---

### 9. 通用阴影
- `.shadow` `.shadow-sm` `.shadow-md` `.shadow-lg`：不同强度阴影
- **用法**：
  ```html
  <div class="shadow-md">有阴影的内容</div>
  ```

---

### 10. 通用背景色
- `.bg-dark`：深色背景
- `.bg-light`：浅色背景
- `.bg-success` `.bg-warning` `.bg-danger` `.bg-info`：状态背景色
- **用法**：
  ```html
  <div class="bg-dark">深色背景</div>
  <div class="bg-success">成功背景</div>
  ```

---

### 11. 响应式隐藏/显示
- `.hidden-xs` `.hidden-sm` `.hidden-md` `.hidden-lg`：在不同屏幕宽度下隐藏
- `.visible-xs` `.visible-sm` `.visible-md` `.visible-lg`：在不同屏幕宽度下显示
- **用法**：
  ```html
  <div class="hidden-xs">大屏可见</div>
  <div class="visible-xs">小屏可见</div>
  ```

---

### 12. 动画/过渡
- `.fade-in`：淡入动画
- `.scale-in`：缩放弹入动画
- `.transition` `.transition-bg` `.transition-color`：通用过渡
- **用法**：
  ```html
  <div class="fade-in">淡入</div>
  <div class="scale-in">缩放弹入</div>
  <div class="transition">有过渡</div>
  ```

---

### 13. 间距工具类
- `.mt-8` `.mb-8` `.ml-8` `.mr-8`：margin/padding 8px
- `.pt-8` `.pb-8` `.pl-8` `.pr-8`：padding 8px
- **用法**：
  ```html
  <div class="mt-8 mb-8">上下间距</div>
  ```

---

### 14. 字体/加粗/状态色
- `.font-12` `.font-14` `.font-16`：字体大小
- `.font-bold` `.font-light`：加粗/细字体
- `.text-success` `.text-warning` `.text-danger` `.text-info`：状态色
- **用法**：
  ```html
  <span class="font-bold text-danger">警告</span>
  ```

---

### 15. 头像/图片/遮罩/Loading
- `.avatar` `.avatar-sm` `.avatar-lg`：圆形头像
- `.img-fluid`：自适应图片
- `.overlay`：全屏遮罩层
- `.loading-spin`：通用loading旋转
- **用法**：
  ```html
  <img class="avatar avatar-lg" src="..." />
  <div class="overlay"></div>
  <div class="loading-spin"></div>
  <img class="img-fluid" src="..." />
  ```

---

### 16. 表单项间距
- `.form-item`：表单项下间距
- **用法**：
  ```html
  <div class="form-item">表单项</div>
  ```

---

### 17. 边框工具类
- `.border` `.border-top` `.border-bottom` `.border-left` `.border-right` `.border-none`：通用边框
- **用法**：
  ```html
  <div class="border-bottom">底部分割线</div>
  ```

---

### 18. 定位工具类
- `.relative` `.absolute` `.fixed` `.sticky`：常用定位
- `.top-0` `.left-0` `.right-0` `.bottom-0`：边界定位
- `.center-absolute`：绝对居中
- **用法**：
  ```html
  <div class="absolute top-0 right-0">右上角</div>
  <div class="center-absolute">居中</div>
  ```

---

### 19. 透明度
- `.opacity-0` `.opacity-50` `.opacity-80` `.opacity-100`：不同透明度
- **用法**：
  ```html
  <div class="opacity-50">半透明</div>
  ```

---

### 20. 宽高/最小宽高
- `.w-100` `.h-100`：宽高100%
- `.min-w-0` `.min-h-0`：最小宽高0
- **用法**：
  ```html
  <div class="w-100 h-100">全宽高</div>
  ```

---

### 21. z-index
- `.z-1` `.z-10` `.z-100` `.z-1000`：常用层级
- **用法**：
  ```html
  <div class="z-1000">顶层内容</div>
  ```

---

### 22. 列表无样式
- `.list-unstyled`：去除ul/ol默认样式
- **用法**：
  ```html
  <ul class="list-unstyled">...</ul>
  ```

---

### 23. 渐变遮罩
- `.mask-gradient-bottom`：底部渐变遮罩
- **用法**：
  ```html
  <div class="mask-gradient-bottom">...</div>
  ```

---

### 24. 鼠标样式
- `.cursor-pointer` `.cursor-default` `.cursor-move` `.cursor-not-allowed`
- **用法**：
  ```html
  <span class="cursor-pointer">可点击</span>
  ```

---

## 如何在项目中引入和使用

1. 在 `main.js` 或入口文件全局引入：
   ```js
   import '@/styles/common.scss';
   ```
2. 直接在模板中为元素添加对应 class 即可复用。
3. 可与 Ant Design Vue、Element Plus 等UI库样式无缝配合。

---

## 适用场景和团队协作建议

- 适用于页面布局、表单、弹窗、卡片、列表、按钮等高频场景
- 推荐团队成员优先使用这些工具类，保持代码风格统一
- 可在设计规范/组件库文档中引用本文件内容

---

## 扩展建议

- 可根据实际需求新增/调整工具类，如 `.mt-16`、`.font-18`、`.shadow-xl` 等
- 建议团队成员统一维护，避免重复命名和样式冲突
- 如需支持更多响应式断点、主题色、动画等，可在本文件基础上扩展

---