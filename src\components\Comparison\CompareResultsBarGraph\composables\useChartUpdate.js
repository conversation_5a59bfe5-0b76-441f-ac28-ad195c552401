/**
 * 图表更新逻辑 composable
 * 处理CompareResultsBarGraph的图表更新和事件处理
 */
import * as d3 from 'd3';
import { calculateInterval, processChartData, CHART_CONSTANTS } from '@/utils/chartUtils';

/**
 * 图表更新逻辑 composable
 * @param {Object} baseChart - 基础图表引用
 * @param {Object} drawingFunctions - 绘制函数集合
 * @param {Object} interactionFunctions - 交互函数集合
 * @returns {Object} 图表更新相关的方法和状态
 */
export function useChartUpdate(baseChart, drawingFunctions, interactionFunctions) {
  // 内部状态变量
  let processedData;
  let interval;

  /**
   * 处理图表挂载完成事件
   * @param {Array} data - 图表数据
   */
  const handleChartMounted = (data) => {
    if (data && data.length > 0) {
      updateChart(data, true);
    }
  };

  /**
   * 处理数据变化事件
   * @param {Array} newData - 新数据
   */
  const handleDataChanged = (newData) => {
    if (!newData || !baseChart.value) return;

    const { currentData } = baseChart.value.barChart;

    // 保持选中状态
    if (currentData.value.size > 0) {
      maintainSelection(currentData);
    }

    // 检查是否是添加柱子的情况
    const addBars = newData.length > (processedData?.length || 0);
    updateChart(newData, false, addBars);
  };

  /**
   * 处理图表重新初始化事件
   * @param {Array} newData - 新数据
   */
  const handleChartInitialized = (newData) => {
    if (newData && newData.length > 0) {
      updateChart(newData, true);
    }
  };

  /**
   * 维持选中状态
   * @param {Object} currentData - 当前选中数据
   */
  const maintainSelection = (currentData) => {
    const selectedItem = currentData.value.values().next().value;
    if (selectedItem) {
      const selector = `[data-id="${selectedItem.algorithmCode}-${selectedItem.portrait.portraitId}"]`;
      const clickedBar = d3.selectAll(selector);
      baseChart.value.barChart.updateElements(clickedBar, selectedItem);
    }
  };

  /**
   * 更新图表函数（CompareResultsBarGraph特有逻辑）
   * @param {Array} newData - 新数据
   * @param {boolean} isInit - 是否为初始化
   * @param {boolean} addBars - 是否为添加柱子
   */
  const updateChart = (newData, isInit = false, addBars = false) => {
    try {
      if (!baseChart.value) return;

      const { g2, g4, altitudeHeight } = baseChart.value.barChart.getSVGElements();
      const { state, currentData } = baseChart.value.barChart;

      // 清除之前的内容
      clearPreviousContent(g2, g4, currentData);

      // 获取配置常量
      const { rectWidth, rectsPerGroup, startX } = getChartConstants();

      // 处理数据
      processedData = processChartData(newData, rectsPerGroup);
      const { totalRects, groups } = calculateChartDimensions(newData, rectsPerGroup);

      // 计算间隔
      if (isInit || addBars) {
        interval = calculateInterval(state.chartWidth - startX, rectWidth, totalRects, groups, rectsPerGroup);
      }

      // 创建X坐标计算函数
      const xCalculator = createXCalculator(startX, rectWidth);

      // 获取图例复选框状态
      const { legendCheckbox } = baseChart.value;

      // 绘制所有柱状图元素
      renderAllBars(processedData, {
        xCalculator,
        rectWidth,
        altitudeHeight,
        g2,
        g4,
        legendCheckbox,
        currentData,
      });
    } catch (error) {
      console.error('更新图表错误', error);
    }
  };

  /**
   * 清除之前的内容
   * @param {d3.Selection} g2 - SVG组选择器
   * @param {d3.Selection} g4 - SVG组选择器
   * @param {Object} currentData - 当前选中数据
   */
  const clearPreviousContent = (g2, g4, currentData) => {
    g2.selectAll('*').remove();
    g4.selectAll('.operation-area').remove();
    currentData.value.clear();
  };

  /**
   * 获取图表常量
   * @returns {Object} 图表常量
   */
  const getChartConstants = () => ({
    rectWidth: CHART_CONSTANTS.RECT_WIDTH,
    rectsPerGroup: CHART_CONSTANTS.RECTS_PER_GROUP_SINGLE,
    startX: CHART_CONSTANTS.START_X,
  });

  /**
   * 计算图表尺寸
   * @param {Array} data - 数据
   * @param {number} rectsPerGroup - 每组矩形数
   * @returns {Object} 图表尺寸信息
   */
  const calculateChartDimensions = (data, rectsPerGroup) => ({
    totalRects: data.length,
    groups: Math.ceil(data.length / rectsPerGroup),
  });

  /**
   * 创建X坐标计算函数（CompareResultsBarGraph特有：均匀分布）
   * @param {number} startX - 起始X坐标
   * @param {number} rectWidth - 矩形宽度
   * @returns {Function} X坐标计算函数
   */
  const createXCalculator = (startX, rectWidth) => {
    return (_, i) => startX + i * (rectWidth + interval);
  };

  /**
   * 渲染所有柱状图元素
   * @param {Array} data - 处理后的数据
   * @param {Object} renderParams - 渲染参数
   */
  const renderAllBars = (data, renderParams) => {
    const { xCalculator, rectWidth, altitudeHeight, g2, g4, legendCheckbox, currentData } = renderParams;

    data.forEach((d, i) => {
      // 绘制主要柱状图
      drawingFunctions.drawMainBars(d, i, xCalculator, rectWidth, altitudeHeight, g2, legendCheckbox);

      // 绘制文本匹配柱状图
      drawingFunctions.drawTextMatchingBars(d, i, xCalculator, rectWidth, altitudeHeight, g2, legendCheckbox);

      // 绘制操作区域
      interactionFunctions.drawOperationArea(d, i, xCalculator, rectWidth, g4, currentData, legendCheckbox, altitudeHeight);

      // 绘制完全匹配图标
      drawingFunctions.drawPerfectMatchIcon(d, i, xCalculator, altitudeHeight, g2);

      // 绘制顶部标签
      drawingFunctions.drawTopLabel(d, i, xCalculator, g2, altitudeHeight, rectWidth);
    });
  };

  /**
   * 获取处理后的数据
   * @returns {Array} 处理后的数据
   */
  const getProcessedData = () => processedData;

  /**
   * 获取当前间隔
   * @returns {number} 当前间隔
   */
  const getCurrentInterval = () => interval;

  return {
    handleChartMounted,
    handleDataChanged,
    handleChartInitialized,
    updateChart,
    getProcessedData,
    getCurrentInterval,
  };
}
