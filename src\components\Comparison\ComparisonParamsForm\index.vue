<!--
 * @Author:yuz<PERSON><PERSON>e
 * @Date: 2025-03-25 15:16:51
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-29 17:11:32
 * @FilePath: \platform-face-web\src\components\Comparison\ComparisonParamsForm\index.vue
 * @Description: 用于编辑比对时用到的参数，支持通过配置显示不同的字段和顺序
 * 
-->
<template>
  <div class="comparison-params-container">
    <div class="header">
      <SvgIcon :icon-class="headIcon" size="21px" />
      <span>{{ headTitle }}</span>
    </div>
    <Divider />
    <div class="comparison-params-form">
      <a-form ref="formRef" :model="formData" :colon="false">
        <!-- 动态渲染字段 -->
        <template v-for="(field, index) in computedFieldConfig" :key="field.type">
          <div class="form-row">
            <div class="trim">
              <div class="dots-container"><span class="dots"></span></div>
              <span class="dashes" v-if="index < computedFieldConfig.length - 1"></span>
            </div>
            <div class="form-item" :class="`${layout}-item`">
              <div class="form-label" v-if="field?.showLabel !== false">
                <span v-if="isShowIndex">{{ index + 1 }}</span>
                <span>{{ field.label }}</span>
              </div>
              <div class="form-wrapper">
                <!-- 人像组字段 -->
                <template v-if="field.type === 'portraitGroup'">
                  <PortraitGroupField v-model="formData.portraitLibraryId"
                    :portrait-group-options="portraitGroupOptions" :get-popup-container="getPopupContainer"
                    @update:model-value="handleFieldChange" :button-bg="field?.buttonBg" :hover-bg="field?.hoverBg"
                    :font-size="field?.fontSize" />
                </template>

                <!-- 算法字段 -->
                <template v-else-if="field.type === 'algorithm'">
                  <AlgorithmField v-model="formData.algorithmCodes" :algorithm-options="algorithmOptions"
                    :disabled="requirePortraitFirst && !formData.portraitLibraryId"
                    :get-popup-container="getPopupContainer" @change="handleFieldChange" />
                </template>

                <!-- 返回结果数字段 -->
                <template v-else-if="field.type === 'numberOfReturns'">
                  <SliderField v-model="formData.numberOfReturns" :max="50" :min="0" :step="1"
                    :disabled="requirePortraitFirst && !formData.portraitLibraryId" @change="handleFieldChange"
                    :marks="field?.marks" />
                </template>

                <!-- 比对阈值字段 -->
                <template v-else-if="field.type === 'matchingThreshold'">
                  <SliderField v-model="formData.matchingThreshold" :max="1" :min="0.1" :step="0.1"
                    :disabled="requirePortraitFirst && !formData.portraitLibraryId" @change="handleFieldChange" />
                </template>
                <!-- 在这里添加具名插槽 -->
                <slot name="item-actions" :field="field" :index="index"></slot>
              </div>
            </div>
          </div>
        </template>
      </a-form>
    </div>
  </div>
  <div v-if="!hiddenSubmitButton" class="form-footer">
    <a-form-item>
      <CustomButtonWithTooltip width="220px" height="34px" @click="handleSubmit">
        保存编辑
      </CustomButtonWithTooltip>
    </a-form-item>
  </div>
</template>

<script setup>
import Divider from '@/components/Common/Divider/index.vue';
import PortraitGroupField from './components/PortraitGroupField.vue';
import AlgorithmField from './components/AlgorithmField.vue';
import SliderField from './components/SliderField.vue';
import { useFormData } from './composables/useFormData.js';
import { useFieldConfig } from './composables/useFieldConfig.js';
import { ref } from 'vue';

const props = defineProps({
  headTitle: {
    type: String,
    default: '比对配置'
  },
  headIcon: {
    type: String,
    default: 'comparison-comparison-strategy-high'
  },
  defaultParams: { // 默认数据
    type: Object,
    default: () => ({
      portraitLibraryIds: [],
      algorithmCodes: ['ci_an'],
      numberOfReturns: 50,
      matchingThreshold: 0.7,
      strategyId: ''
    })
  },
  comparisonType: { // 模式
    type: String,
    default: 'image-to-library',
    validator: (value) => ['image-to-library', 'image-to-image'].includes(value)
  },
  algorithmOptions: {
    type: Array,
    default: () => [],
  },
  portraitGroupOptions: {
    type: Array,
    default: () => [],
  },
  layout: { // 布局
    type: String,
    default: 'vertical'
  },
  hiddenSubmitButton: {
    type: Boolean,
    default: false,
  },
  // 优先选择人像组
  requirePortraitFirst: {
    type: Boolean,
    default: false
  },
  // 字段配置
  fieldConfig: {
    type: Array,
    required: false
  },
  // 是否显示序号
  isShowIndex: {
    type: Boolean,
    default: true
  }
});

// 表单引用
const formRef = ref(null);

const emits = defineEmits(['submit-success', 'comparison-change']);

// 使用 composables
const {
  formData,
  handleFieldChange,
  handleSubmit,
  getFormData
} = useFormData(props, emits);

const {
  computedFieldConfig,
  getPopupContainer
} = useFieldConfig(props);

// 暴露给父组件的方法和数据

defineExpose({
  getFormData,
  formData
});
</script>

<style scoped lang="scss">
@use './styles/form-styles.scss';
</style>
