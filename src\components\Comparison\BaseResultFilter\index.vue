<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 15:03:43
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-23 16:55:04
 * @FilePath: \platform-face-web\src\components\Comparison\BaseResultFilter\index.vue
 * @Description: 通用的结果过滤器基础组件-通过配置化的方式支持不同类型的结果过滤器
-->
<template>
  <div class="matching-filter-container">
    <!-- 左侧标题区域 -->
    <template v-if="config.leftSection">
      <SvgIcon v-if="config.leftSection.icon" :icon-class="config.leftSection.icon"></SvgIcon>
      <span v-if="config.leftSection.title" class="font-size-h4">{{ config.leftSection.title }}</span>
      <span v-if="config.leftSection.text">{{ config.leftSection.text }}</span>
    </template>

    <!-- 梯形区域 -->
    <TrapezoidSection :config="config" :form-data="formData" :portrait-group-options="portraitGroupOptions"
      @update:portraitGroupId="handlePortraitGroupChange" />

    <!-- 主要内容区域 -->
    <div class="inner-container">
      <!-- 统计信息和自定义按钮区域 -->
      <FilterSections :config="config" :stats-data="statsData" />

      <!-- 算法选择区域 -->
      <div v-if="config.showAlgorithmSelect" class="filter-item">
        <span class="label">{{ config.algorithmLabel || '比对算法' }}</span>
        <div class="algorithm-wrapper">
          <AlgorithmField v-model="formData.algorithmCodes" :algorithm-options="computedAlgorithmOptions"
            :get-popup-container="getPopupContainer" @change="handleAlgorithmChange" />
        </div>
      </div>

      <!-- 表单控件区域 -->
      <FormControls :config="config" :form-data="formData" @update:numberOfResults="handleResultsChange"
        @update:comparisonThreshold="handleThresholdChange" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
// 导入子组件
import TrapezoidSection from './components/TrapezoidSection.vue';
import FilterSections from './components/FilterSections.vue';
import FormControls from './components/FormControls.vue';
import AlgorithmField from '../ComparisonParamsForm/components/AlgorithmField.vue';
// 导入 composables
import { useFilterForm } from './composables/useFilterForm.js';
import { useAlgorithmOptions } from './composables/useAlgorithmOptions.js';
import { useStatsData } from './composables/useStatsData.js';

const props = defineProps({
  config: {
    type: Object,
    required: true,
    default: () => ({})
  },
  defaultParams: {
    type: Object,
    default: () => ({
      portraitGroupId: '',
      algorithmCodes: ['ci_an'],
      numberOfResults: 50,
      comparisonThreshold: 0.7,
    })
  },
  portraitGroupOptions: {
    type: Array,
    default: () => [],
  },
  // 统计数据，用于显示统计信息
  statsData: {
    type: Object,
    default: () => ({})
  }
});

const emits = defineEmits(['submit-success']);

// 使用 composables
const {
  formData,
  isFormChanged,
  handleAlgorithmChange,
  getFormData
} = useFilterForm(props, emits);

const { computedAlgorithmOptions } = useAlgorithmOptions(props, formData);
const { getPopupContainer } = useStatsData(props);

// 算法选择器引用
const algorithmFieldRef = ref(null);

// 处理人像组变化
const handlePortraitGroupChange = (value) => {
  formData.value.portraitGroupId = value;
};

// 处理返回结果数变化
const handleResultsChange = (value) => {
  formData.value.numberOfResults = value;
};

// 处理阈值变化
const handleThresholdChange = (value) => {
  formData.value.comparisonThreshold = value;
};

// 聚焦算法选择器
const focusAlgorithmSelect = () => {
  // 这里可以添加聚焦逻辑，如果 AlgorithmField 组件暴露了相关方法
  console.log('Focus algorithm select');
};

defineExpose({
  getFormData,
  formData
});
</script>

<style lang="scss" scoped>
@use './styles/filter-styles.scss';

.matching-filter-container {
  align-items: center;
  display: flex;
  gap: 16px;
  height: 45px;
  justify-content: flex-end;
  color: #b0bbcc;
  padding: 0 0 0 16px;
}

.inner-container {
  align-items: center;
  display: flex;
  gap: 8px;
  height: 45px;
  justify-content: flex-end;
  color: #b0bbcc;
  background-color: #24394e;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -80px;
    top: 0;
    height: 100%;
    width: 80px;
    background-color: inherit;
  }
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 20px;

  .label {
    // 标签样式
  }
}

.algorithm-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 108px;

  .filter-icon {
    margin-left: 8px;
  }
}
</style>
