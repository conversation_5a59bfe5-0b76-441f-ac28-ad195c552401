# GalleryFilterDropdown 通用筛选组件

一个功能强大、高度可配置的下拉筛选组件，支持多级分组、多选/单选、自定义数据源等功能。

## 特性

- ✅ 支持 v-model 双向绑定
- ✅ 多级分组和嵌套选项
- ✅ 多选/单选模式
- ✅ 自定义数据源
- ✅ 可配置的排序功能
- ✅ 灵活的触发方式（hover/click）
- ✅ 可配置的位置（left/right）
- ✅ 完整的事件系统
- ✅ TypeScript 友好
- ✅ 向后兼容

## 基础用法

```vue
<template>
  <GalleryFilterDropdown v-model="selectedValues" :options="filterOptions" @change="handleChange" />
</template>

<script setup>
import { ref } from 'vue';
import GalleryFilterDropdown from '@/components/Business/GalleryFilterDropdown';

const selectedValues = ref([]);
const filterOptions = ref([
  {
    name: '分类',
    items: [
      {
        label: '电子产品',
        value: 'electronics',
        children: [
          { label: '手机', value: 'phone' },
          { label: '电脑', value: 'computer' },
        ],
      },
    ],
  },
]);

const handleChange = (values, items) => {
  console.log('选中的值:', values);
  console.log('选中的完整项目:', items);
};
</script>
```

## Props

| 参数            | 类型      | 默认值                                         | 说明                           |
| --------------- | --------- | ---------------------------------------------- | ------------------------------ |
| `options`       | `Array`   | `[]`                                           | 筛选选项数据                   |
| `modelValue`    | `Array`   | `[]`                                           | v-model 绑定的选中值           |
| `showSort`      | `Boolean` | `true`                                         | 是否显示排序                   |
| `sortOptions`   | `Array`   | `[{label: '按最近添加排序', value: 'recent'}]` | 排序选项                       |
| `showSelectAll` | `Boolean` | `true`                                         | 是否显示"显示全部"             |
| `placement`     | `String`  | `'right'`                                      | 下拉框位置 ('left' \| 'right') |
| `trigger`       | `String`  | `'hover'`                                      | 触发方式 ('hover' \| 'click')  |
| `closeOnSelect` | `Boolean` | `false`                                        | 选择后是否关闭                 |
| `disabled`      | `Boolean` | `false`                                        | 是否禁用                       |
| `iconHeight`    | `String`  | `'27px'`                                       | 图标高度                       |
| `iconSize`      | `String`  | `'14px'`                                       | 图标大小                       |

## Events

| 事件名              | 参数                                  | 说明             |
| ------------------- | ------------------------------------- | ---------------- |
| `update:modelValue` | `(values: Array)`                     | v-model 更新事件 |
| `change`            | `(values: Array, items: Array)`       | 选中值变化事件   |
| `sort-change`       | `(sortBy: String, sortOrder: String)` | 排序变化事件     |
| `open`              | `()`                                  | 下拉框打开事件   |
| `close`             | `()`                                  | 下拉框关闭事件   |

## 数据结构

### options 数据结构

```typescript
interface FilterOption {
  name?: string; // 分组名称
  items: FilterItem[]; // 分组中的选项
}

interface FilterItem {
  label: string; // 显示文本
  value: string; // 选项值
  number?: number; // 数量显示（可选，会在选项右侧显示数字）
  iconName?: string; // 图标名称（可选）
  children?: FilterItem[]; // 子选项（可选，支持多级嵌套）
}
```

### 示例数据

```javascript
const options = [
  {
    name: '商品分类',
    items: [
      {
        label: '电子产品',
        value: 'electronics',
        number: 156, // 可选：显示数量
        children: [
          { label: '手机', value: 'phone', number: 45 },
          { label: '电脑', value: 'computer', number: 32 },
          {
            label: '配件',
            value: 'accessories',
            iconName: 'accessories-icon', // 可选：显示图标
            children: [
              { label: '耳机', value: 'headphone' },
              { label: '充电器', value: 'charger' },
            ],
          },
        ],
      },
      {
        label: '服装',
        value: 'clothing',
        number: 89,
      },
    ],
  },
  {
    name: '状态',
    items: [
      { label: '在售', value: 'on-sale', number: 145 },
      { label: '缺货', value: 'out-of-stock', number: 23 },
    ],
  },
];
```

### 更多示例数据

组件提供了多种场景的示例数据，可以从 `defaultData.js` 文件中导入：

```javascript
import {
  galleryFilterOptions, // 图册筛选
  productFilterOptions, // 商品筛选
  userPermissionOptions, // 用户权限筛选
  regionOptions, // 地区选择
} from '@/components/Business/GalleryFilterDropdown/defaultData.js';
```

## 暴露的方法

通过 ref 可以访问以下方法：

```javascript
const filterRef = ref();

// 获取选中的完整项目信息
const selectedItems = filterRef.value.selectedItems;

// 根据值获取项目信息
const item = filterRef.value.getItemByValue('some-value');

// 选中全部
filterRef.value.selectAll();

// 清空选择
filterRef.value.clearAll();

// 打开/关闭下拉框
filterRef.value.open();
filterRef.value.close();
```

## 高级用法

### 点击触发 + 左对齐

```vue
<GalleryFilterDropdown v-model="selectedValues" :options="options" trigger="click" placement="left" />
```

### 隐藏排序和全选

```vue
<GalleryFilterDropdown v-model="selectedValues" :options="options" :show-sort="false" :show-select-all="false" />
```

## 注意事项

1. `modelValue` 应该是一个数组
2. 每个选项的 `value` 必须是唯一的
3. 嵌套选项的选中状态会自动同步到父级
4. 组件内部会建立值到项目的映射关系，确保高效的查找性能
