<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-17 17:06:42
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 16:17:12
 * @FilePath: \platform-face-web\src\components\Common\FaceCanvas\components\FaceBox.vue
 * @Description:人脸框组件
-->
<template>
  <div :class="[
    'face-box',
    {
      selected: isSelected,
      auto: !face?.manualSelection && !isNew,
      manual: face?.manualSelection,
      drawing: isDrawing && isNew
    }
  ]" :style="boxStyle" @click="handleClick">
    <!-- 选中状态的对勾图标 -->
    <img v-if="shouldShowCheckIcon" src="@/assets/svg/common/selected.svg" :style="checkIconStyle"
      class="face-check-icon" />

    <!-- 人脸框序号 -->
    <span v-if="shouldShowIndex" class="face-index" :style="indexStyle">
      {{ index }}
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue';

/**
 * 人脸框组件属性
 */
const props = defineProps({
  /** 人脸框数据 */
  face: {
    type: Object,
    required: true
  },
  /** 是否被选中 */
  isSelected: {
    type: Boolean,
    default: false
  },
  /** 缩放比例 */
  scale: {
    type: Number,
    default: 1
  },
  /** 偏移量 */
  offset: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  /** 是否正在绘制 */
  isDrawing: {
    type: Boolean,
    default: false
  },
  /** 是否显示对勾图标 */
  showCheckIcon: {
    type: Boolean,
    default: true
  },
  /** 是否显示序号 */
  showIndex: {
    type: Boolean,
    default: true
  },
  /** 序号 */
  index: {
    type: Number,
    default: 1
  },
  /** 是否为新绘制的框 */
  isNew: {
    type: Boolean,
    default: false
  },
  /** 自定义样式配置 */
  customStyle: {
    type: Object,
    default: () => null
  }
});

/**
 * 组件事件
 */
const emit = defineEmits(['click']);

// 样式常量
const FACE_BOX_COLORS = {
  autoDefault: '#9EA6B2',      // 自动检测未选中
  autoSelected: '#00E875',     // 自动检测选中
  manual: '#4DBFFF',           // 手动绘制
  drawing: '#4DBFFF'           // 绘制中
};

const FACE_BOX_STYLES = {
  borderWidth: '1px',
  borderStyle: 'solid',
  borderDashed: 'dashed',
  borderTransparent: 'transparent',
  cornerSize: '15px',
  cornerThickness: '2px'
};

const ICON_SIZE = 20;
const INDEX_OFFSET = -8;

/**
 * 是否显示选中图标
 */
const shouldShowCheckIcon = computed(() => {
  if (props.customStyle && props.customStyle.showSelection === false) {
    return false;
  }
  return props.isSelected && props.showCheckIcon;
});

/**
 * 是否显示序号
 */
const shouldShowIndex = computed(() => {
  if (props.customStyle && props.customStyle.showIndex === false) {
    return false;
  }
  return props.showIndex;
});

/**
 * 生成角落渐变背景
 */
const getCornerGradientBackground = (borderColor, cornerThickness) => {
  return `
    linear-gradient(to right, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 0 0,
    linear-gradient(to right, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 0 100%,
    linear-gradient(to left, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 100% 0,
    linear-gradient(to left, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 100% 100%,
    linear-gradient(to bottom, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 0 0,
    linear-gradient(to bottom, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 100% 0,
    linear-gradient(to top, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 0 100%,
    linear-gradient(to top, ${borderColor} ${cornerThickness}, transparent ${cornerThickness}) 100% 100%
  `;
};

/**
 * 人脸框样式计算
 */
const boxStyle = computed(() => {
  if (!props.face) return {};

  // 正在绘制的新框
  if (props.isNew) {
    return {
      left: props.face.x + 'px',
      top: props.face.y + 'px',
      width: props.face.width + 'px',
      height: props.face.height + 'px',
      pointerEvents: 'none',
    };
  }

  const baseStyle = {
    left: props.face.x * props.scale + props.offset.x + 'px',
    top: props.face.y * props.scale + props.offset.y + 'px',
    width: props.face.width * props.scale + 'px',
    height: props.face.height * props.scale + 'px',
  };

  // 如果有自定义样式，使用自定义样式但保持四角框样式
  if (props.customStyle) {
    const customBorderColor = props.customStyle.borderColor || '#00E875';
    const cornerThickness = FACE_BOX_STYLES.cornerThickness;
    const cornerSize = FACE_BOX_STYLES.cornerSize;

    return {
      ...baseStyle,
      border: `${FACE_BOX_STYLES.borderWidth} ${FACE_BOX_STYLES.borderStyle} ${FACE_BOX_STYLES.borderTransparent}`,
      background: getCornerGradientBackground(customBorderColor, cornerThickness),
      backgroundRepeat: 'no-repeat',
      backgroundSize: `${cornerSize} ${cornerSize}`,
      pointerEvents: props.isDrawing ? 'none' : 'auto',
    };
  }

  // 手动绘制的人脸框
  if (props.face?.manualSelection) {
    return {
      ...baseStyle,
      border: `${FACE_BOX_STYLES.borderWidth} ${FACE_BOX_STYLES.borderDashed} ${FACE_BOX_COLORS.manual}`,
      background: 'transparent',
      pointerEvents: props.isDrawing ? 'none' : 'auto',
    };
  }

  // 自动检测的人脸框 - 使用四角框样式
  const borderColor = props.isSelected ? FACE_BOX_COLORS.autoSelected : FACE_BOX_COLORS.autoDefault;
  const cornerThickness = FACE_BOX_STYLES.cornerThickness;
  const cornerSize = FACE_BOX_STYLES.cornerSize;

  return {
    ...baseStyle,
    border: `${FACE_BOX_STYLES.borderWidth} ${FACE_BOX_STYLES.borderStyle} ${FACE_BOX_STYLES.borderTransparent}`,
    background: getCornerGradientBackground(borderColor, cornerThickness),
    backgroundRepeat: 'no-repeat',
    backgroundSize: `${cornerSize} ${cornerSize}`,
    pointerEvents: props.isDrawing ? 'none' : 'auto',
  };
});

/**
 * 对勾图标样式
 */
const checkIconStyle = computed(() => ({
  width: ICON_SIZE + 'px',
  height: ICON_SIZE + 'px',
  bottom: `${-(ICON_SIZE + 4)}px`
}));

/**
 * 序号样式
 */
const indexStyle = computed(() => ({
  right: INDEX_OFFSET + 'px',
  bottom: `${-ICON_SIZE}px`
}));

/**
 * 处理点击事件
 */
const handleClick = () => {
  if (props.isDrawing || props.isNew) return;
  // 只有当face有key时才发射点击事件
  if (props.face.key) {
    emit('click', props.face.key);
  }
};
</script>

<style lang="scss" scoped>
.face-box {
  position: absolute;
  box-sizing: border-box;
  border-radius: 0px;
  cursor: pointer;

  // 正在绘制中的样式
  &.drawing {
    pointer-events: none;
  }
}

.face-check-icon {
  position: absolute;
  right: 0;
}

.face-index {
  position: absolute;
  font-size: 10px;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  padding: 2px 4px;
  border-radius: 8px;
}
</style>
