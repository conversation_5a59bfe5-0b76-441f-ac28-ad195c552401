<!--
 * @Author:  gzr <EMAIL>
 * @Date: 2025-07-16 16:58:41
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-29 16:45:44
 * @FilePath: \platform-face-web\src\components\Common\ImagePreviewer\index.vue
 * @Description: 图片预览组件 - 支持多张图片切换查看
-->
<template>
  <!-- 多张图片切换查看组件 -->
  <a-modal :open="visible" title="" width="50%" :footer="null" :mask-closable="maskClosable"
    @cancel="$emit('update:visible', false)">
    <div class="preview-container">
      <!-- 图片加载状态 -->
      <div v-if="imageLoading" class="loading-container">
        <a-spin size="large" />
        <p class="loading-text">图片加载中...</p>
      </div>

      <!-- 图片加载错误状态 -->
      <div v-else-if="imageError" class="error-container">
        <div class="error-content">
          <ExclamationCircleOutlined class="error-icon" />
          <p class="error-text">图片加载失败</p>
          <a-button type="primary" @click="retryLoadImage">重试</a-button>
        </div>
      </div>

      <!-- 图片显示 -->
      <div v-else class="image-container">
        <img :src="images[currentIndex]" class="preview-image" @load="handleImageLoad" @error="handleImageError" />
      </div>

      <!-- 导航按钮 -->
      <div v-if="showNavigation && images.length > 0" class="navigation-btns">
        <a-button @click="handlePrev" :disabled="imageLoading">
          <LeftOutlined />
        </a-button>
        <span class="index-display">{{ currentIndex + 1 }}/{{ images.length }}</span>
        <a-button @click="handleNext" :disabled="imageLoading">
          <RightOutlined />
        </a-button>
      </div>

      <!-- 缩略图切换 -->
      <div v-if="showThumbnails && images.length > 1" class="thumbnails-container" ref="thumbnailsContainerRef">
        <div v-for="(image, index) in images" :key="index" class="thumbnail-item"
          :class="{ active: index === currentIndex }" @click="handleThumbnailClick(index)">
          <img :src="image" :alt="`缩略图 ${index + 1}`" class="thumbnail-image" />
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import {
  LeftOutlined,
  RightOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 是否显示模态框 */
  visible: {
    type: Boolean,
    default: false
  },
  /** 图片列表 */
  images: {
    type: Array,
    default: () => [],
    validator: (value) => Array.isArray(value) && value.every(item => typeof item === 'string')
  },
  /** 初始显示的图片索引 */
  initialIndex: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0
  },
  /** 是否显示导航按钮 */
  showNavigation: {
    type: Boolean,
    default: true
  },
  /** 是否显示缩略图 */
  showThumbnails: {
    type: Boolean,
    default: true
  },
  /** 是否允许点击遮罩关闭 */
  maskClosable: {
    type: Boolean,
    default: true
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'update:visible',
  'change' // 图片切换时触发
]);

// 响应式数据
const currentIndex = ref(props.initialIndex);
const imageLoading = ref(false); // 初始设置为true，等待图片加载
const imageError = ref(false);

/**
 * 监听visible变化，当模态框打开时重置图片状态
 */
watch(() => props.visible, (newVisible) => {
  newVisible && resetImageState();
});

/**
 * 监听初始索引变化
 */
watch(() => props.initialIndex, (newIndex) => {
  if (newIndex >= 0 && newIndex < props.images.length) {
    currentIndex.value = newIndex;
    resetImageState();
  }
});

/**
 * 监听当前索引变化
 */
watch(currentIndex, (newIndex, oldIndex) => {
  if (newIndex !== oldIndex) {
    // 只有在索引真正变化时才重置状态
    if (oldIndex !== undefined) {
      resetImageState();
    }
    emit('change', newIndex);
  }
});

/**
 * 重置图片状态
 */
const resetImageState = () => {
  // imageLoading.value = true;
  imageError.value = false;
};

/**
 * 图片加载完成处理
 */
const handleImageLoad = () => {
  imageLoading.value = false;
  imageError.value = false;
};

/**
 * 图片加载错误处理
 */
const handleImageError = () => {
  imageLoading.value = false;
  imageError.value = true;
};

/**
 * 重试加载图片
 */
const retryLoadImage = () => {
  resetImageState();
};

/**
 * 上一张图片
 */
const handlePrev = () => {
  if (props.images.length <= 1) return;
  currentIndex.value = (currentIndex.value - 1 + props.images.length) % props.images.length;
};

/**
 * 下一张图片
 */
const handleNext = () => {
  if (props.images.length <= 1) return;
  currentIndex.value = (currentIndex.value + 1) % props.images.length;
};

/**
 * 缩略图点击处理
 */
const handleThumbnailClick = (index) => {
  currentIndex.value = index;
};

const thumbnailsContainerRef = ref(null);

let isDragging = false;
let startX = 0;
let scrollLeft = 0;

function handleMouseDown(e) {
  isDragging = true;
  startX = e.pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  scrollLeft = thumbnailsContainerRef.value.scrollLeft;
  document.body.style.cursor = 'grabbing';
}
function handleMouseMove(e) {
  if (!isDragging) return;
  e.preventDefault();
  const x = e.pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  const walk = x - startX;
  thumbnailsContainerRef.value.scrollLeft = scrollLeft - walk;
}
function handleMouseUp() {
  isDragging = false;
  document.body.style.cursor = '';
}

// 触摸事件
function handleTouchStart(e) {
  isDragging = true;
  startX = e.touches[0].pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  scrollLeft = thumbnailsContainerRef.value.scrollLeft;
}
function handleTouchMove(e) {
  if (!isDragging) return;
  const x = e.touches[0].pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  const walk = x - startX;
  thumbnailsContainerRef.value.scrollLeft = scrollLeft - walk;
}
function handleTouchEnd() {
  isDragging = false;
}

onMounted(() => {
  const el = thumbnailsContainerRef.value;
  if (!el) return;
  el.addEventListener('mousedown', handleMouseDown);
  window.addEventListener('mousemove', handleMouseMove);
  window.addEventListener('mouseup', handleMouseUp);

  el.addEventListener('touchstart', handleTouchStart, { passive: false });
  window.addEventListener('touchmove', handleTouchMove, { passive: false });
  window.addEventListener('touchend', handleTouchEnd);
});
onBeforeUnmount(() => {
  const el = thumbnailsContainerRef.value;
  if (!el) return;
  el.removeEventListener('mousedown', handleMouseDown);
  window.removeEventListener('mousemove', handleMouseMove);
  window.removeEventListener('mouseup', handleMouseUp);

  el.removeEventListener('touchstart', handleTouchStart);
  window.removeEventListener('touchmove', handleTouchMove);
  window.removeEventListener('touchend', handleTouchEnd);
});
</script>


<style lang="scss" scoped>
* {
  -webkit-user-drag: none;
  -moz-user-drag: none;
  -ms-user-drag: none;
  user-drag: none;
  user-select: none;
}

.preview-container {
  position: relative;
  padding: 10px 0 80px 0;
  min-height: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  width: 100%;

  .loading-text {
    margin-top: var(--spacing-medium);
    color: var(--color-text-secondary);
    font-size: var(--font-size-medium);
  }
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  width: 100%;

  .error-content {
    text-align: center;

    .error-icon {
      font-size: 48px;
      color: var(--color-error);
      margin-bottom: var(--spacing-medium);
    }

    .error-text {
      color: var(--color-text-secondary);
      font-size: var(--font-size-medium);
      margin-bottom: var(--spacing-large);
    }
  }
}

.image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 70vh; // 固定高度
  max-height: 600px; // 最大高度限制
  min-height: 400px; // 最小高度
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  display: block;
  margin: 0 auto;
  border-radius: var(--border-radius-small);
  object-fit: contain; // 保持比例，完整显示
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); // 添加阴影效果
}

.navigation-btns {
  position: absolute;
  bottom: 100px; // 调整位置，避免与缩略图重叠
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-medium);
  align-items: center;
  padding: var(--spacing-small) var(--spacing-medium);
  border-radius: var(--border-radius-medium);
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);

  .index-display {
    color: var(--color-text);
    font-size: var(--font-size-medium);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    min-width: 60px;
    text-align: center;
  }
}

.thumbnails-container {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-small);
  padding: var(--spacing-small);
  border-radius: var(--border-radius-medium);
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  z-index: 10;
  max-width: 80%;
  overflow-x: auto;
  overflow-y: hidden;
  cursor: grab;

  .thumbnails-wrapper {
    display: flex;
    gap: var(--spacing-small);
    -webkit-overflow-scrolling: touch;
    padding: 0 var(--spacing-small);
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE and Edge

    &::-webkit-scrollbar {
      display: none; // Chrome, Safari, Opera
    }
  }

  .thumbnail-item {
    flex: 0 0 auto;
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-small);
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      border-color: rgba(255, 255, 255, 0.5);
    }

    &.active {
      border-color: var(--color-primary);
      transform: scale(1.1);
    }

    .thumbnail-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>