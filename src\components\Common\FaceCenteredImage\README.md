# FaceCenteredImage 组件

一个基于人脸坐标智能裁剪和居中显示图片的 Vue 3 组件。该组件能够根据提供的人脸坐标信息，自动计算缩放比例和偏移量，确保人脸区域在容器中居中显示。

## 功能特性

- 🎯 **智能人脸居中**: 根据人脸坐标自动计算最佳显示位置
- 📏 **自适应缩放**: 自动计算缩放比例，确保人脸完整显示
- 🔄 **响应式布局**: 支持容器尺寸变化时自动重新计算
- 🎨 **灵活样式**: 支持自定义容器尺寸和圆角
- ⚡ **性能优化**: 使用 ResizeObserver 监听容器变化

## 安装使用

```vue
<template>
  <FaceCenteredImage
    :src="imageUrl"
    :face-x="100"
    :face-y="50"
    :face-width="200"
    :face-height="250"
    :container-width="300"
    :container-height="300"
    border-radius="50%"
  />
</template>

<script setup>
import FaceCenteredImage from '@/components/Common/FaceCenteredImage/index.vue'

const imageUrl = 'https://example.com/photo.jpg'
</script>
```

## Props 参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `src` | `String` | ✅ | - | 图片 URL 地址 |
| `faceX` | `Number \| String` | ✅ | - | 人脸区域左上角 X 坐标（基于原图尺寸） |
| `faceY` | `Number \| String` | ✅ | - | 人脸区域左上角 Y 坐标（基于原图尺寸） |
| `faceWidth` | `Number \| String` | ✅ | - | 人脸区域宽度（基于原图尺寸） |
| `faceHeight` | `Number \| String` | ✅ | - | 人脸区域高度（基于原图尺寸） |
| `containerWidth` | `Number \| String` | ❌ | - | 容器宽度，支持 px 数值或百分比字符串 |
| `containerHeight` | `Number \| String` | ❌ | - | 容器高度，支持 px 数值或百分比字符串 |
| `borderRadius` | `String` | ❌ | `'0'` | 容器圆角，如 `'8px'` 或 `'50%'`（圆形头像） |

## 使用示例

### 基础用法

```vue
<FaceCenteredImage
  src="/images/portrait.jpg"
  :face-x="120"
  :face-y="80"
  :face-width="180"
  :face-height="220"
  :container-width="200"
  :container-height="200"
/>
```

### 圆形头像

```vue
<FaceCenteredImage
  src="/images/avatar.jpg"
  :face-x="100"
  :face-y="50"
  :face-width="200"
  :face-height="250"
  :container-width="120"
  :container-height="120"
  border-radius="50%"
/>
```

### 响应式容器

```vue
<FaceCenteredImage
  src="/images/photo.jpg"
  :face-x="150"
  :face-y="100"
  :face-width="300"
  :face-height="400"
  container-width="100%"
  container-height="60vh"
/>
```

## 工作原理

1. **坐标计算**: 根据提供的人脸坐标计算人脸中心点
2. **缩放计算**: 计算最佳缩放比例，确保人脸区域能够填充容器
3. **偏移计算**: 计算图片偏移量，使人脸中心与容器中心对齐
4. **响应式更新**: 监听容器尺寸变化，自动重新计算显示参数

## 注意事项

- 人脸坐标参数基于图片的原始尺寸，不是显示尺寸
- 组件会自动处理图片加载状态，确保在图片加载完成后才进行计算
- 容器尺寸支持数值（px）和字符串（%、vh、vw 等）
- 建议为容器设置明确的宽高，避免布局抖动

## 技术实现

- 使用 Vue 3 Composition API
- 利用 ResizeObserver 实现响应式布局
- 通过 CSS Transform 实现高性能的图片定位
- 支持图片预加载和缓存场景

## 浏览器兼容性

- 现代浏览器（Chrome 64+、Firefox 69+、Safari 13.1+）
- 需要 ResizeObserver 支持（可通过 polyfill 兼容旧版本）
