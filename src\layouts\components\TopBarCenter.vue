<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-10 17:35:59
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-28 11:09:37
 * @FilePath: \platform-face-web\src\layouts\components\TopBarCenter.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="center-menu">
    <transition name="menu-fade" mode="out-in">
      <a-menu v-if="menuItems.length > 0" mode="horizontal" :selectedKeys="[activeKey]" :items="menuItems"
        @click="onMenuClick" :style="topBarMenuStyle.value" :key="moduleName" />
    </transition>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, computed, watch, nextTick, h } from 'vue';
import menus from '@/router/menus';
import theme from '@/theme';
import * as Icons from '@ant-design/icons-vue';

const props = defineProps({
  childrenRoutes: {
    type: Array,
    default: () => [],
  },
});

const route = useRoute();
const router = useRouter();

const menuItems = ref([]);
const moduleName = computed(() => {
  return route.path.split('/')[1];
});
// 强制更新菜单项的函数
const updateMenuItems = () => {
  if (!moduleName.value) menuItems.value = [];
  else {
    const menuItem = menus.find(({ key }) => key === `/${moduleName.value}`);
    const list = menuItem?.subMenus || [];
    menuItems.value = list.map((item) => {
      // 判断激活
      const isActive = route.path === item.path;
      // 取 icon 或 activeIcon
      const iconMeta = isActive && item.meta?.activeIcon ? item.meta.activeIcon : item.meta?.icon;

      let iconVNode = null;
      if (typeof iconMeta === 'string' && Icons[iconMeta]) {
        iconVNode = h(Icons[iconMeta], { class: 'topbar-menu-anticon' });
      } else if (typeof iconMeta === 'string') {
        iconVNode = h('img', { src: iconMeta, class: 'topbar-menu-img' });
      } else if (iconMeta) {
        iconVNode = h(iconMeta, { class: 'topbar-menu-anticon' });
      }

      return {
        label: h('div', { style: { display: 'flex', alignItems: 'center', gap: '4px' } }, [iconVNode, h('span', {}, item.meta?.title)]),
        key: item.path,
      };
    });
  }
};

// 监听路由变化，使用 nextTick 确保在 DOM 更新后执行
watch(
  () => route.path,
  () => {
    nextTick(() => {
      updateMenuItems();
    });
  },
  {
    immediate: true,
  },
);

// 监听路由参数变化
watch(
  () => route.params,
  () => {
    nextTick(() => {
      updateMenuItems();
    });
  },
  {
    deep: true,
  },
);
const activeKey = computed(() => {
  // const moduleName = route.path.split('/')[1];
  return route.path;
});

const topBarMenuStyle = computed(() => ({
  background: theme.topBarMenuBg,
  color: theme.topBarMenuColor,
  width: '100%',
  borderBottom: 'none',
}));

const showAnimation = ref(false);

function onMenuClick({ key }) {
  if (key !== route.path) {
    router.push(key);
    // 路由跳转完成后重新启用动画
    nextTick(() => {
      setTimeout(() => {
        showAnimation.value = true;
      }, 100);
    });
  }
}
</script>

<style lang="scss" scoped>
.ant-menu-light {
  background: transparent;
}

.top-bar-center-content {
  font-size: 1.1rem;
  color: #b6cfff;
}

.center-menu {
  width: 100%;
}

.ant-menu-horizontal {
  width: 100%;
  display: flex;
  justify-content: center;
  color: #fff;
}

:deep(.ant-menu-horizontal > .ant-menu-item) {
  transition: color 0.2s, background 0.2s;
  padding: 0 24px;
  height: 48px;
  line-height: 48px;
  color: v-bind('theme.topBarMenuColor') !important;
  background: v-bind('theme.topBarMenuBg') !important;
}

:deep(.ant-menu-horizontal > .ant-menu-item:hover),
:deep(.ant-menu-horizontal > .ant-menu-item-active) {
  color: v-bind('theme.topBarMenuActiveColor') !important;
  background: v-bind('theme.topBarMenuActiveBg') !important;
  border-radius: 6px 6px 0 0;
}

:deep(.ant-menu-horizontal > .ant-menu-item-selected) {
  color: v-bind('theme.topBarMenuSelectedColor') !important;
  background: v-bind('theme.topBarMenuSelectedBg') !important;
  border-radius: 8px 8px 0 0;
  font-weight: bold;
}

// 菜单淡入淡出动画
.menu-fade-enter-active,
.menu-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-fade-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.menu-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.menu-fade-enter-to,
.menu-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

:deep(.topbar-menu-img) {
  width: 16px;
  height: 16px;
  display: block;
  margin: 0 auto 2px auto;
}

:deep(.topbar-menu-anticon) {
  font-size: 16px !important;
  width: 16px;
  height: 16px;
  display: block;
  margin: 0 auto 2px auto;
  line-height: 1;
  vertical-align: middle;
}
</style>