# ManInfoForm 人员信息表单组件

## 📖 组件概述

ManInfoForm 是一个用于编辑人员详细信息的表单组件，支持身份信息、社会行为、其他信息等多个模块的数据录入和管理。

## ✨ 主要功能

- 🆔 **身份信息管理**：姓名、性别、曾用名、年龄、别名、籍贯、证件号、居住地
- 🏛️ **社会行为管理**：关键标签、关联事件、关系人管理
- 📝 **其他信息管理**：肤色、备注等补充信息
- 🔄 **数组字段支持**：支持动态增删的多值字段
- 📱 **响应式设计**：适配不同屏幕尺寸

## 🏗️ 组件架构

```
src/components/Comparison/ManInfoForm/
├── index.vue                    # 主组件
├── components/                  # 子组件目录
│   ├── IdentityFields.vue       # 身份信息字段
│   ├── SocialBehaviorFields.vue # 社会行为字段
│   └── OtherFields.vue          # 其他信息字段
├── composables/                 # 逻辑复用
│   ├── useFormData.js           # 表单数据管理
└── README.md                    # 组件文档
```

## 📋 Props 属性

| 属性名         | 类型   | 默认值 | 说明         |
| -------------- | ------ | ------ | ------------ |
| involvedFaceId | String | ''     | 关联的人脸ID |
| initialData    | Object | -      | 初始表单数据 |

## 🎪 Events 事件

| 事件名         | 参数     | 说明               |
| -------------- | -------- | ------------------ |
| submit-success | formData | 表单提交成功时触发 |

## 🚀 使用示例

### 基础用法

```vue
<template>
  <ManInfoForm :involved-face-id="faceId" :initial-data="formData" @submit-success="handleSubmit" />
</template>

<script setup>
const faceId = ref('face-123');
const formData = ref({
  name: '张三',
  sex: 'male',
  age: 30,
  // ... 其他字段
});

const handleSubmit = (data) => {
  console.log('提交的表单数据:', data);
  // 处理提交逻辑
};
</script>
```

## 🔧 开发指南

### 添加新字段

1. 在对应的字段组件中添加表单项
2. 在 `useFormData.js` 中更新初始数据结构
3. 在提交逻辑中处理新字段的数据转换

### 添加新模块

1. 创建新的字段组件（如 `NewModuleFields.vue`）
2. 在主组件中使用 `FormSection` 包装新模块
3. 更新相关的 composables

## ⚠️ 注意事项

1. **数据格式**：确保传入的 initialData 格式正确
2. **字典数据**：证件类型和地址类型依赖字典接口
3. **图片资源**：关系人头像需要有效的图片ID
4. **表单验证**：目前暂未启用严格的表单验证

## 🎯 重构说明

本组件已从原来的 576 行单文件重构为模块化结构：

- **主组件**：负责整体布局和数据流转
- **子组件**：各自负责特定功能模块
- **Composables**：抽取可复用的逻辑
- **通用组件**：提供基础的UI组件

重构后的优势：

- ✅ 符合项目规范（每个文件 < 400行）
- ✅ 职责清晰，易于维护
- ✅ 组件可复用，提升开发效率
- ✅ 逻辑分离，便于测试
