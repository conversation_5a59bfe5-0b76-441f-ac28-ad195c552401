<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 09:48:10
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 16:41:28
 * @FilePath: \platform-face-web\src\components\Common\AvatarImage\index.vue
 * @Description: 支持基础图片显示、边框装饰、状态徽章、人脸智能居中、箭头指示等功能的图片组件
 * 
 * 
-->
<template>
  <div class="avatar-image-wrapper" :style="wrapperStyle">
    <!-- 主图片容器 -->
    <div class="avatar-image-ring" :style="imageRingStyle">
      <div :style="imageContainerStyle">
        <slot name="image">
          <!-- 如果启用人脸居中且有人脸坐标 -->
          <template v-if="shouldShowFaceCenteredImage">
            <!-- 如果有自定义图片插槽内容，优先使用插槽 -->
            <template v-if="$slots.childImage">
              <slot name="childImage" v-bind="faceProps"></slot>
            </template>
            <!-- 否则使用默认的FaceCenteredImage -->
            <FaceCenteredImage v-else v-bind="faceProps" :key="faceImageKey" />
          </template>
          <!-- 普通图片显示 -->
          <img v-else :src="src" :alt="alt" :style="imageStyle" class="avatar-image" draggable="false" />
        </slot>
      </div>
    </div>

    <!-- 状态徽章 - 左上角 -->
    <div class="status-badges status-top-left" :style="statusTopLeftStyle">
      <div class="status-items-wrapper">
        <slot name="statusTopLeft"></slot>
      </div>
    </div>

    <!-- 状态徽章 - 右上角 -->
    <div class="status-badges status-top-right" :style="statusTopRightStyle">
      <div class="status-items-wrapper">
        <slot name="statusTopRight"></slot>
      </div>
    </div>

    <!-- 状态徽章 - 左下角 -->
    <div class="status-badges status-bottom-left" :style="statusBottomLeftStyle">
      <div class="status-items-wrapper">
        <slot name="statusBottomLeft"></slot>
      </div>
    </div>

    <!-- 状态徽章 - 右下角 -->
    <div class="status-badges status-bottom-right" :style="statusBottomRightStyle">
      <div class="status-items-wrapper">
        <slot name="statusBottomRight"></slot>
      </div>
    </div>

    <!-- 箭头指示器 -->
    <div v-if="showArrow" :class="arrowWrapperClass"></div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue';
import FaceCenteredImage from '@/components/Common/FaceCenteredImage/index.vue';

const props = defineProps({
  // === 基础图片属性 (ImageRing) ===
  src: { type: String, required: true },
  alt: { type: String, default: '' },

  // === 边框样式属性 ===
  ringColor: { type: String, default: '#2B2F38' },
  ringThickness: { type: Number, default: 3 },
  gap: { type: [Number, String], default: 0 },
  width: { type: [Number, String], default: 80 },
  height: { type: [Number, String], default: 80 },
  borderRadius: { type: String, default: '50%' },
  backgroundColor: { type: String, default: '#16171c' },
  overflow: { type: String, default: 'hidden' },

  // === 状态徽章功能 (ImageRingWithStatus) ===
  statusTopLeft: {
    type: Object,
    default: () => ({ top: '-12px', left: '16px' }),
  },
  statusTopRight: {
    type: Object,
    default: () => ({ top: '-12px', right: '16px' }),
  },
  statusBottomLeft: {
    type: Object,
    default: () => ({ bottom: '-11px', left: '16px' }),
  },
  statusBottomRight: {
    type: Object,
    default: () => ({ bottom: '-11px', right: '2px' }),
  },

  // === 人脸居中功能 (FaceCenteredImage) ===
  faceX: [Number, String], // 人脸左上角 X 坐标
  faceY: [Number, String], // 人脸左上角 Y 坐标
  faceWidth: [Number, String], // 人脸宽度
  faceHeight: [Number, String], // 人脸高度
  containerWidth: { type: [Number, String], default: '100%' }, // 容器宽度 (可 px 或 %)
  containerHeight: { type: [Number, String], default: '100%' }, // 容器高度

  // === 箭头指示器功能 (IdentityImage) ===
  showArrow: { type: Boolean, default: false },
  arrowDirection: { type: String, default: 'right' }, // 'right' 或 'bottom'
});

const { showArrow, arrowDirection } = toRefs(props);

// 主容器样式
const wrapperStyle = computed(() => ({
  position: 'relative',
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
}));

// 图片环样式
const imageRingStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  border: `${props.ringThickness}px solid ${props.ringColor}`,
  borderRadius: props.borderRadius,
  overflow: props.overflow,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: `${props.gap}px`,
  boxSizing: 'border-box',
  backgroundColor: props.backgroundColor,
}));

// 图片容器样式
const imageContainerStyle = computed(() => {
  const gap = props.ringThickness * 2 + props.gap * 2;
  const innerWidth = typeof props.width === 'number'
    ? ((props.width - gap) + 'px')
    : `calc(${props.width} - ${gap}px)`;
  const innerHeight = typeof props.height === 'number'
    ? ((props.height - gap) + 'px')
    : `calc(${props.height} - ${gap}px)`;
  return {
    width: innerWidth,
    height: innerHeight,
    borderRadius: props.borderRadius,
    zIndex: 2,
  };
});

// 普通图片样式
const imageStyle = computed(() => ({
  borderRadius: props.borderRadius,
}));

// 人脸居中相关计算
const shouldShowFaceCenteredImage = computed(() => {
  return props.faceX && props.faceY && props.faceHeight && props.faceWidth;
});

const faceProps = computed(() => ({
  src: props.src,
  faceX: props.faceX,
  faceY: props.faceY,
  faceWidth: props.faceWidth,
  faceHeight: props.faceHeight,
  containerWidth: props.containerWidth,
  containerHeight: props.containerHeight,
  borderRadius: props.borderRadius,
}));

const faceImageKey = computed(() =>
  `${props.faceHeight}-${props.faceWidth}-${props.faceX}-${props.faceY}-${props.src}`
);

// 状态徽章样式
const statusTopLeftStyle = computed(() => ({
  position: 'absolute',
  ...props.statusTopLeft,
}));

const statusTopRightStyle = computed(() => ({
  position: 'absolute',
  ...props.statusTopRight,
}));

const statusBottomLeftStyle = computed(() => ({
  position: 'absolute',
  ...props.statusBottomLeft,
}));

const statusBottomRightStyle = computed(() => ({
  position: 'absolute',
  ...props.statusBottomRight,
}));

// 箭头样式
const arrowWrapperClass = computed(() => {
  return arrowDirection.value === 'bottom' ? 'arrow-wrapper-bottom' : 'arrow-wrapper';
});
</script>

<style scoped lang="scss">
.avatar-image-wrapper {
  position: relative;
}

.avatar-image-ring {
  position: relative;
}

.avatar-image {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 状态徽章样式
.status-badges {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  z-index: 4;
}

.status-top-left {
  justify-content: flex-start;
}

.status-top-right {
  justify-content: flex-end;
}

.status-bottom-left {
  justify-content: flex-start;
}

.status-bottom-right {
  justify-content: flex-end;
}

.status-items-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 24px;
}

.status-items-wrapper::after {
  content: '';
  height: 2px;
  background-color: #16171c;
  width: 100%;
  position: absolute;
  top: calc(50% - 1px);
  left: 0;
  z-index: -1;
}

// 右侧箭头样式
.arrow-wrapper {
  position: absolute;
  top: 50%;
  right: -16px;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  overflow: hidden;
  pointer-events: none;
}

.arrow-wrapper::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: calc(50% - 8px);
  left: calc(50% - 9px);
  background: #16171c;
  transform: rotate(45deg);
  border: 1px solid #4DBFFF;
  border-left: none;
  border-bottom: none;
}

.arrow-wrapper::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  background: #16171c;
  top: calc(50% - 5px);
  left: calc(50% - 7px);
  transform: rotate(45deg);
  border: 1px solid #4DBFFF;
  border-left: none;
  border-bottom: none;
}

// 底部箭头样式
.arrow-wrapper-bottom {
  position: absolute;
  left: calc(50% - 16px);
  bottom: -32px;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  overflow: hidden;
  pointer-events: none;
}

.arrow-wrapper-bottom::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: calc(50% - 9px);
  left: calc(50% - 8px);
  background: #16171c;
  transform: rotate(45deg);
  border: 1px solid #4DBFFF;
  border-left: none;
  border-top: none;
}

.arrow-wrapper-bottom::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  background: #16171c;
  top: calc(50% - 8px);
  left: calc(50% - 5px);
  transform: rotate(45deg);
  border: 1px solid #4DBFFF;
  border-left: none;
  border-top: none;
}
</style>
