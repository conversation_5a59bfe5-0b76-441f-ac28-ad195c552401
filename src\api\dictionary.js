import request from '@/utils/service';

const BASE_URL = import.meta.env.VITE_API_BASE_URL;

const CACHE_TTL = 60 * 60 * 1000; // 1 小时，单位：毫秒

/**
 * 查询缓存数据
 * @param {string} cacheKey
 * @returns {any|null} 如果数据存在且未过期，则返回数据；否则返回 null
 */
function getCache(cacheKey) {
  const cacheStr = localStorage.getItem(cacheKey);
  if (!cacheStr) return null;

  try {
    const cacheObj = JSON.parse(cacheStr);
    if (Date.now() - cacheObj.timestamp < cacheObj.ttl) {
      return cacheObj.data;
    } else {
      // 缓存过期，清除数据
      localStorage.removeItem(cacheKey);
      return null;
    }
  } catch (error) {
    // 解析失败则删除缓存
    localStorage.removeItem(cacheKey);
    return null;
  }
}

/**
 * 缓存数据
 * @param {string} cacheKey
 * @param {any} data
 * @param {number} ttl - 有效期，单位：毫秒
 */
function setCache(cacheKey, data, ttl) {
  const cacheObj = {
    timestamp: Date.now(),
    ttl,
    data,
  };
  localStorage.setItem(cacheKey, JSON.stringify(cacheObj));
}

/**
 * 通过业务码查找字典
 * @param {string} dictionaryCode
 * @returns
 */
export async function getDictionary(dictionaryCode) {
  const cacheKey = `dictionary-${dictionaryCode}`;
  const cachedData = getCache(cacheKey);
  if (cachedData) {
    return Promise.resolve(cachedData);
  }

  const result = await request.get(`${BASE_URL}/api/dictionaries/businessCode?prefixes=${dictionaryCode}`);
  const data = result.data;
  setCache(cacheKey, data, CACHE_TTL);
  return data;
}

// 查询符合前缀的所有字典
export async function dictionariesSearch(payload) {
  return request.get(`${BASE_URL}/api/dictionaries/search?prefixes=${payload.prefixes}`);
}

/**
 * 查询委托单位
 */
export async function fetchOrganizations(name = '') {
  const cacheKey = `commissionUnit-${name}`;
  const cachedData = getCache(cacheKey);
  if (cachedData) {
    return Promise.resolve(cachedData);
  }

  const result = await request.request(`${BASE_URL}/api/assigned-organizations?name=${name}`, {}, 'GET');
  setCache(cacheKey, result, CACHE_TTL);
  return result;
}

/**
 * 根据dictionaryId获取对应的单个字典
 */
export async function getDictionaryByDictionaryId(dictionaryId) {
  const result = await request.get(`${BASE_URL}/api/dictionaries/${dictionaryId}`);
  return result?.data?.items;
}
