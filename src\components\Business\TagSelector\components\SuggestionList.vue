<!--
 * @Author: yuzhouisme
 * @Date: 2025-07-24 10:39:04
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 14:40:37
 * @FilePath: \platform-face-web\src\components\Business\TagSelector\components\SuggestionList.vue
 * @Description: TagSelector 建议列表组件
 * 负责显示搜索建议和处理选择操作
 * 
-->
<template>
  <div v-if="showSuggestions && suggestions.length >= 0" class="suggestion-list" :style="suggestionsStyle">
    <div v-for="(item, index) in suggestions" :key="item?.key || item?.id" class="suggestion-list__item"
      :class="{ 'suggestion-list__item--active': index === activeSuggestionIndex }"
      @click="handleSelectSuggestion(item)" @mouseenter="handleMouseEnter(index)">
      <span class="suggestion-list__name">{{ item?.name }}</span>
      <span v-if="item?.description" class="suggestion-list__desc">
        {{ item?.description }}
      </span>
    </div>

    <!-- 无匹配结果 -->
    <div v-if="searchText.trim() && !loading && suggestions.length === 0" class="suggestion-list__no-data">
      <span>无匹配结果，按回车创建新标签</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props 定义
const props = defineProps({
  // 建议列表数据
  suggestions: {
    type: Array,
    default: () => []
  },

  // 是否显示建议列表
  showSuggestions: {
    type: Boolean,
    default: false
  },

  // 当前激活的建议项索引
  activeSuggestionIndex: {
    type: Number,
    default: -1
  },

  // 搜索文本
  searchText: {
    type: String,
    default: ''
  },

  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
});

// 事件定义
const emit = defineEmits([
  'select-suggestion',
  'mouse-enter'
]);

// 计算属性
const suggestionsStyle = computed(() => {
  return {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    zIndex: 1000
  };
});

// 事件处理方法
const handleSelectSuggestion = (item) => {
  emit('select-suggestion', item);
};

const handleMouseEnter = (index) => {
  emit('mouse-enter', index);
};
</script>

<style lang="scss" scoped>
.suggestion-list {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;

  &__item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:hover,
    &--active {
      background-color: #f5f5f5;
    }
  }

  &__name {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
  }

  &__desc {
    font-size: 12px;
    color: #8c8c8c;
    margin-left: 8px;
  }

  &__no-data {
    padding: 12px;
    text-align: center;
    color: #8c8c8c;
    font-size: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .suggestion-list {
    max-height: 150px;
  }
}
</style>
