/**
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 15:40:00
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 15:40:00
 * @FilePath: \platform-face-web\src\components\Business\GroupedImageDisplay\composables\useImageEvents.js
 * @Description: 图片事件处理逻辑 - 统一处理拖拽、点击等事件
 */

import { ref, watch } from 'vue';

/**
 * 图片事件处理 composable
 * @param {Object} props - 组件props
 * @param {Function} emit - 组件emit函数
 * @returns {Object} 事件处理相关的方法和状态
 */
export function useImageEvents(props, emit) {
  // 图片是否可点击的响应式状态
  const imageClickable = ref(props.imageClickable);

  // 监听props变化
  watch(() => props.imageClickable, (newVal) => {
    imageClickable.value = newVal;
  });

  /**
   * 处理图片拖拽开始事件
   * @param {DragEvent} event - 拖拽事件对象
   * @param {Object} item - 被拖拽的图片项
   */
  const onDragStart = (event, item) => {
    const payload = { item, groupId: props.groupId };
    emit('dragstart', event, payload);
  };

  /**
   * 处理拖拽悬停事件
   * @param {DragEvent} event - 拖拽事件对象
   */
  const onDragOver = (event) => {
    event.preventDefault();
    emit('dragover', event);
  };

  /**
   * 处理拖拽进入事件
   * @param {DragEvent} event - 拖拽事件对象
   */
  const onDragEnter = (event) => {
    event.preventDefault();
    emit('dragenter', event);
  };

  /**
   * 处理拖拽离开事件
   * @param {DragEvent} event - 拖拽事件对象
   */
  const onDragLeave = (event) => {
    emit('dragleave', event);
  };

  /**
   * 处理拖拽放置事件
   * @param {DragEvent} event - 拖拽事件对象
   */
  const onDrop = (event) => {
    console.log('onDrop =', event);
    event.preventDefault();
    emit('drop', event, props.groupId);
  };

  /**
   * 处理图片点击事件
   * @param {MouseEvent} event - 点击事件对象
   * @param {Object} face - 被点击的图片对象
   */
  const handleImageClick = (event, face) => {
    if (imageClickable.value) {
      event.stopPropagation(); // 阻止冒泡，仅组件内部处理
      emit('clickImage', face);
    }
    // 不做任何处理，让事件自然冒泡到父组件
  };

  /**
   * 处理"更多"模态框打开事件
   * @param {MouseEvent} event - 点击事件对象
   */
  const openMoreModal = (event) => {
    // 交给父级处理
    event.stopPropagation(); // 阻止冒泡，仅组件内部处理
    emit('openMore', props.groupId);
  };

  return {
    imageClickable,
    onDragStart,
    onDragOver,
    onDragEnter,
    onDragLeave,
    onDrop,
    handleImageClick,
    openMoreModal
  };
}
