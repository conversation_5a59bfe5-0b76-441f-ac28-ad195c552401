<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-29 10:30:00
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-29 16:44:41
 * @FilePath: \platform-face-web\src\components\Common\VideoPlayer\index.vue
 * @Description: 视频播放器组件 - 支持弹窗展示播放视频
-->
<template>
  <!-- 视频播放器弹窗 -->
  <a-modal :open="visible" title="" width="80%" :footer="null" :mask-closable="maskClosable"
    @cancel="$emit('update:visible', false)">
    <div class="video-player-container">
      <!-- 视频信息 -->
      <div v-if="currentVideoInfo" class="video-info">
        <h3 class="video-title">{{ currentVideoInfo.title || '视频播放' }}</h3>
        <!-- <p class="video-duration" v-if="videoDuration">{{ formatDuration(videoDuration) }}</p> -->
      </div>

      <!-- 视频播放器 -->
      <div class="video-container">
        <video ref="videoRef" :src="currentVideoSrc" class="video-player" controls preload="metadata"
          @loadstart="handleVideoLoadStart" @canplay="handleVideoCanPlay" @error="handleVideoError"
          @ended="handleVideoEnded">
          您的浏览器不支持视频播放
        </video>
      </div>

      <!-- 导航按钮 -->
      <div v-if="showNavigation && videos.length > 1" class="navigation-btns">
        <a-button @click="handlePrev" :disabled="videoLoading">
          <LeftOutlined />
        </a-button>
        <span class="index-display">{{ currentIndex + 1 }}/{{ videos.length }}</span>
        <a-button @click="handleNext" :disabled="videoLoading">
          <RightOutlined />
        </a-button>
      </div>

      <!-- 缩略图切换 -->
      <div v-if="showThumbnails && videos.length > 1" class="thumbnails-container" ref="thumbnailsContainerRef">
        <div v-for="(video, index) in videos" :key="index" class="thumbnail-item"
          :class="{ active: index === currentIndex }" @click="onThumbnailClick(index)">
          <video :src="video.url" class="thumbnail-video" muted></video>
          <div class="play-overlay">
            <div class="play-icon">▶</div>
          </div>
        </div>
      </div>

      <!-- 视频加载状态覆盖层 -->
      <div v-if="videoLoading" class="loading-overlay">
        <a-spin size="large" />
        <p class="loading-text">视频加载中...</p>
      </div>

      <!-- 视频加载错误状态覆盖层 -->
      <div v-else-if="videoError" class="error-overlay">
        <div class="error-content">
          <ExclamationCircleOutlined class="error-icon" />
          <p class="error-text">视频加载失败</p>
          <a-button type="primary" @click="retryLoadVideo">重试</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch, onUnmounted, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { ExclamationCircleOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons-vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 是否显示模态框 */
  visible: {
    type: Boolean,
    default: false
  },
  /** 视频源地址 */
  videoSrc: {
    type: String,
    required: true
  },
  /** 视频信息 */
  videoInfo: {
    type: Object,
    default: () => ({})
  },
  /** 视频列表 */
  videos: {
    type: Array,
    default: () => [],
    validator: (value) => Array.isArray(value) && value.every(item => typeof item === 'object' && item.url)
  },
  /** 初始显示的视频索引 */
  initialIndex: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0
  },
  /** 是否显示导航按钮 */
  showNavigation: {
    type: Boolean,
    default: true
  },
  /** 是否显示缩略图 */
  showThumbnails: {
    type: Boolean,
    default: true
  },
  /** 是否允许点击遮罩关闭 */
  maskClosable: {
    type: Boolean,
    default: true
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'update:visible',
  'play',
  'pause',
  'ended',
  'error'
]);

// 响应式数据
const videoRef = ref(null);
const videoLoading = ref(true); // 初始设置为true
const videoError = ref(false);
const videoDuration = ref(0);
const currentIndex = ref(props.initialIndex);

const thumbnailsContainerRef = ref(null);
let isDragging = false;
let startX = 0;
let scrollLeft = 0;

function handleMouseDown(e) {
  console.log('mousedown'); // 调试
  isDragging = true;
  startX = e.pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  scrollLeft = thumbnailsContainerRef.value.scrollLeft;
  document.body.style.cursor = 'grabbing';
}
function handleMouseMove(e) {
  if (!isDragging) return;
  e.preventDefault();
  const x = e.pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  const walk = x - startX;
  thumbnailsContainerRef.value.scrollLeft = scrollLeft - walk;
  console.log('mousemove', { walk, scrollLeft: thumbnailsContainerRef.value.scrollLeft });
}
function handleMouseUp() {
  isDragging = false;
  document.body.style.cursor = '';
}
function handleTouchStart(e) {
  isDragging = true;
  startX = e.touches[0].pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  scrollLeft = thumbnailsContainerRef.value.scrollLeft;
}
function handleTouchMove(e) {
  if (!isDragging) return;
  const x = e.touches[0].pageX - thumbnailsContainerRef.value.getBoundingClientRect().left;
  const walk = x - startX;
  thumbnailsContainerRef.value.scrollLeft = scrollLeft - walk;
}
function handleTouchEnd() {
  isDragging = false;
}

// 缩略图点击事件，只有非拖拽时才切换
function onThumbnailClick(index) {
  handleThumbnailClick(index);
}

// 计算当前视频源和信息
const currentVideoSrc = computed(() => {
  if (props.videos.length > 0) {
    return props.videos[currentIndex.value]?.url || props.videoSrc;
  }
  return props.videoSrc;
});

const currentVideoInfo = computed(() => {
  if (props.videos.length > 0) {
    return props.videos[currentIndex.value] || props.videoInfo;
  }
  return props.videoInfo;
});

/**
 * 组件卸载时清理
 */
onUnmounted(() => {
  if (videoRef.value) {
    videoRef.value.pause();
    videoRef.value.currentTime = 0;
  }
});

onMounted(() => {
  nextTick(() => {
    const el = thumbnailsContainerRef.value;
    if (!el) return;
    el.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    el.addEventListener('touchstart', handleTouchStart, { passive: false });
    window.addEventListener('touchmove', handleTouchMove, { passive: false });
    window.addEventListener('touchend', handleTouchEnd);
  });
});
onBeforeUnmount(() => {
  const el = thumbnailsContainerRef.value;
  if (!el) return;
  el.removeEventListener('mousedown', handleMouseDown);
  window.removeEventListener('mousemove', handleMouseMove);
  window.removeEventListener('mouseup', handleMouseUp);

  el.removeEventListener('touchstart', handleTouchStart);
  window.removeEventListener('touchmove', handleTouchMove);
  window.removeEventListener('touchend', handleTouchEnd);
});

/**
 * 监听visible变化，当模态框打开时重置视频状态
 */
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    resetVideoState();
    // 确保视频重新加载
    if (videoRef.value) {
      videoRef.value.load();
    }
  } else {
    // 模态框关闭时暂停视频并重置状态
    if (videoRef.value) {
      videoRef.value.pause();
      videoRef.value.currentTime = 0;
    }
    resetVideoState();
  }
});

/**
 * 监听视频源变化
 */
watch(() => props.videoSrc, (newSrc) => {
  if (newSrc && props.visible) {
    resetVideoState();
    // 确保视频重新加载
    if (videoRef.value) {
      videoRef.value.load();
    }
  }
});

/**
 * 重置视频状态
 */
const resetVideoState = () => {
  videoLoading.value = true;
  videoError.value = false;
  videoDuration.value = 0;
};

/**
 * 视频开始加载
 */
const handleVideoLoadStart = () => {
  videoLoading.value = true;
  videoError.value = false;
};

/**
 * 视频可以播放
 */
const handleVideoCanPlay = () => {
  videoLoading.value = false;
  videoError.value = false;

  // 获取视频时长
  if (videoRef.value) {
    videoDuration.value = videoRef.value.duration;
  }

  emit('play');
};

/**
 * 视频加载错误
 */
const handleVideoError = (error) => {
  console.log('视频加载错误:', error); // 调试信息
  videoLoading.value = false;
  videoError.value = true;
  emit('error');
};

/**
 * 视频播放结束
 */
const handleVideoEnded = () => {
  emit('ended');
};

/**
 * 重试加载视频
 */
const retryLoadVideo = () => {
  resetVideoState();
  if (videoRef.value) {
    videoRef.value.load();
  }
};

/**
 * 上一视频
 */
const handlePrev = () => {
  if (props.videos.length <= 1) return;
  currentIndex.value = (currentIndex.value - 1 + props.videos.length) % props.videos.length;
};

/**
 * 下一视频
 */
const handleNext = () => {
  if (props.videos.length <= 1) return;
  currentIndex.value = (currentIndex.value + 1) % props.videos.length;
};

/**
 * 缩略图点击处理
 */
const handleThumbnailClick = (index) => {
  currentIndex.value = index;
};
</script>

<style lang="scss" scoped>
* {
  -webkit-user-drag: none;
  -moz-user-drag: none;
  -ms-user-drag: none;
  user-drag: none;
  user-select: none;
}

.video-player-container {
  position: relative;
  width: max-content;
  max-width: 90%;
  margin: 0 auto;
  min-height: 400px;
  border-radius: 12px;
  padding: 5px 20px 150px 20px; // 增加底部内边距，为缩略图留出空间
  background-color: rgba(250, 250, 250, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 10px;
}

.loading-text,
.error-text {
  margin-top: 16px;
  color: #999;
  font-size: 14px;
}

.error-content {
  text-align: center;

  .error-icon {
    font-size: 48px;
    color: #ff4d4f;
    margin-bottom: 16px;
  }

  .error-text {
    margin-bottom: 16px;
  }
}

.video-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  position: relative;
}

.video-player {
  max-width: 100%;
  max-height: 60vh;
  display: block;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-info {
  text-align: center;

  .video-title {
    font-size: 18px;
    font-weight: 600;
    color: #b4b4b4;
    margin-bottom: 8px;
  }

  .video-duration {
    font-size: 14px;
    color: #999;
    margin: 0;
  }
}

.navigation-btns {
  position: absolute;
  bottom: 95px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-medium);
  align-items: center;
  padding: var(--spacing-small) var(--spacing-medium);
  border-radius: var(--border-radius-medium);
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  z-index: 10;

  .index-display {
    color: var(--color-text);
    font-size: var(--font-size-medium);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    min-width: 60px;
    text-align: center;
  }
}

.thumbnails-container {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-small);
  padding: var(--spacing-small);
  border-radius: var(--border-radius-medium);
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  z-index: 10;
  max-width: 80%;
  overflow-x: auto;
  overflow-y: hidden;
  cursor: grab;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;

  /* IE和Edge */
  .thumbnails-container::-webkit-scrollbar {
    display: none;
    /* Chrome/Safari/Webkit */
  }

  .thumbnails-wrapper {
    display: flex;
    gap: var(--spacing-small);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 var(--spacing-small);
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .thumbnail-item {
    flex: 0 0 auto;
    width: 80px;
    height: 60px;
    border-radius: var(--border-radius-small);
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      // transform: scale(1.05);
      border-color: rgba(255, 255, 255, 0.5);
    }

    &.active {
      border-color: var(--color-primary);
      // transform: scale(1.1);
    }

    .thumbnail-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;

      .play-icon {
        font-size: 20px;
        color: white;
        opacity: 0.8;
      }
    }
  }
}
</style>