<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 16:10:00
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 14:41:32
 * @FilePath: \platform-face-web\src\components\Business\GroupedImageDisplay\index.vue
 * @Description: 分组图片展示组件 - 重构后的主入口组件
 -->
<template>
  <div class="grouped-image-container" @dragover.prevent="onDragOver" @dragenter="onDragEnter" @dragleave="onDragLeave"
    @drop.stop="onDrop">
    <!-- 根据 gridType 来动态显示不同的布局 -->
    <template v-if="groupedImages.length > 0">
      <!-- 单张大图展示 -->
      <SingleLayout v-if="gridType === 'single'" :image="groupedImages[0]" :selected-items="selectedItems"
        :ring-color="ringColor" :selected-ring-color="selectedRingColor" :ring-thickness="ringThickness"
        @dragstart="onDragStart" @click="handleImageClick" />

      <!-- 1x2 和 2x2 布局 -->
      <GridLayout v-else-if="gridType === '1x2' || gridType === '2x2'" :images="groupedImages" :grid-type="gridType"
        :selected-items="selectedItems" :ring-color="ringColor" :selected-ring-color="selectedRingColor"
        :ring-thickness="ringThickness" @dragstart="onDragStart" @click="handleImageClick" />

      <!-- 嵌套子网格的布局（5-7 张） -->
      <NestedLayout v-else-if="gridType === 'nested'" :first-three-images="firstThreeImages"
        :nested-images="nestedImages" :selected-items="selectedItems" :ring-color="ringColor"
        :selected-ring-color="selectedRingColor" :ring-thickness="ringThickness" @dragstart="onDragStart"
        @click="handleImageClick" />

      <!-- 嵌套子网格，并在右下单元添加"更多"覆盖层（大于 7 张） -->
      <NestedMoreLayout v-else-if="gridType === 'nested-more'" :first-three-images="firstThreeImages"
        :nested-images="nestedImages" :more-count="moreCount" :selected-items="selectedItems" :ring-color="ringColor"
        :selected-ring-color="selectedRingColor" :ring-thickness="ringThickness" @dragstart="onDragStart"
        @click="handleImageClick" @open-more="openMoreModal" />
    </template>

    <!-- 如果没有分组，则显示 currentImage -->
    <SingleLayout v-else-if="currentImage" :image="currentImage" :selected-items="selectedItems" :ring-color="ringColor"
      :selected-ring-color="selectedRingColor" :ring-thickness="ringThickness" @dragstart="onDragStart"
      @click="handleImageClick" />
  </div>
</template>

<script setup>
// 导入子组件
import SingleLayout from './components/SingleLayout.vue';
import GridLayout from './components/GridLayout.vue';
import NestedLayout from './components/NestedLayout.vue';
import NestedMoreLayout from './components/NestedMoreLayout.vue';

// 导入composables
import { useLayoutLogic } from './composables/useLayoutLogic.js';
import { useImageEvents } from './composables/useImageEvents.js';

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 分组图片数据数组 */
  groupedImages: {
    type: Array,
    required: true,
  },
  /** 标识当前分组的唯一 ID */
  groupId: {
    type: [Number, String],
    required: true,
  },
  /** 当前图片对象（无分组时使用） */
  currentImage: {
    type: Object,
  },
  /** 已选中的图片ID数组 */
  selectedItems: {
    type: Array,
    default: () => [],
  },
  /** 默认边框颜色 */
  ringColor: {
    type: String,
    default: '#4c5f78',
  },
  /** 选中状态边框颜色 */
  selectedRingColor: {
    type: String,
    default: '#4DBFFF',
  },
  /** 边框厚度 */
  ringThickness: {
    type: Number,
    default: 0,
  },
  /** 是否启用图片点击功能 */
  imageClickable: {
    type: Boolean,
    default: false,
  },
});

/**
 * 组件事件定义
 */
const emit = defineEmits([
  'dragstart',
  'dragover',
  'dragenter',
  'dragleave',
  'drop',
  'clickImage',
  'openMore'
]);

/**
 * 使用布局逻辑 composable
 */
const {
  gridType,
  firstThreeImages,
  nestedImages,
  moreCount
} = useLayoutLogic(props);

/**
 * 使用事件处理 composable
 */
const {
  onDragStart,
  onDragOver,
  onDragEnter,
  onDragLeave,
  onDrop,
  handleImageClick,
  openMoreModal
} = useImageEvents(props, emit);
</script>

<style lang="scss" scoped>
.grouped-image-container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
