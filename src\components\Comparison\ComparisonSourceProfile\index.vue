<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-24 17:04:57
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 15:02:52
 * @FilePath: \platform-face-web\src\components\Comparison\ComparisonSourceProfile\index.vue
 * @Description: 详情页、研判页、快速报告页面左侧的比对源，包括关键信息编辑、图片编辑的功能
 * 
 * 
-->
<template>
    <div class="identity-image-container">
        <AvatarImage :src="getImageWebURL(currentFace.multiFaceData.sourceImageId)" width="404px" height="264px"
            :gap="5" :face-x="currentFace.multiFaceData.items[0].coordinate[0]"
            :face-y="currentFace.multiFaceData.items[0].coordinate[1]"
            :face-width="currentFace.multiFaceData.items[0].coordinate[2]"
            :face-height="currentFace.multiFaceData.items[0].coordinate[3]" :show-arrow="showArrow">
            <template #statusTopRight>
                <SvgIcon v-if="currentFace?.keyInvolved && comparisonType === 'image-to-library'" icon-class="keyman"
                    size="30px"></SvgIcon>
                <SvgIcon v-if="currentFace?.targetMan && comparisonType === 'image-to-image'"
                    icon-class="target-man-blue" size="30px"></SvgIcon>
            </template>
            <template #statusTopLeft>
                <div v-if="comparisonType === 'image-to-image'" class="d-flex align-items-center g-1 score-div">
                    <span>{{ currentFace?.recommendedPortrait?.portrait?.xm }}</span>
                    <Divider direction="vertical" color="#40516A" height="10px" />
                    <span>{{ toFixedDecimal(currentFace?.recommendedPortrait?.score, 2) }}</span>
                    <span>{{ algorithmShortNames[currentFace?.recommendedPortrait?.algorithmCode] }}</span>
                </div>
            </template>
        </AvatarImage>
        <div class="w-100 d-flex align-items-center justify-content-between position-relative ">
            <div class="d-flex align-items-center g-2" style="height: 44px;">
                <span>{{ currentFace?.recommendedPortrait?.portrait?.xm }}</span>
                <Divider direction="vertical" color="#40516A" height="10px" />
                <CustomButtonWithTooltip tooltip="关键信息编辑" icon-name="man-info" icon-size="16px" button-bg="#16171C"
                    hover-bg="#2A2D34" @click="handleModalContent('manInfo')">
                </CustomButtonWithTooltip>
                <BadgeWrapper :count="imageDataCount || 0" :offset="{ top: '0px', right: '-4px' }">
                    <CustomButtonWithTooltip tooltip="图像信息编辑" icon-name="image-info" icon-size="18px"
                        button-bg="#16171C" hover-bg="#2A2D34" @click="handleModalContent('imageInfo')">
                    </CustomButtonWithTooltip>
                </BadgeWrapper>
            </div>
            <div v-if="showMoreBtn" class="more-btn d-flex cursor-pointer align-items-center justify-content-center"
                @click="toggleClick">
                <SvgIcon :icon-class="faceVisible ? 'up-arrow-blue' : 'down-arrow-blue'" size="10px">
                </SvgIcon>
            </div>
        </div>
    </div>

    <a-modal v-model:open="modelVisible" width="1240px" height="750px">
        <template #title>
            <div class="modal-header">
                <SvgIcon :icon-class="modalContent?.titleIconClass" size="18px" />
                <span>{{ modalContent?.titleText }}</span>
            </div>
        </template>
        <Suspense>
            <template #default>
                <component :is="modalContent?.component" v-bind="modalContent?.props"
                    v-on="modalContent?.events || {}" />
            </template>
            <template #fallback>
                <div class="w-100 h-100 d-flex align-items-center justify-content-center">
                    <a-spin tip="加载中..." />
                </div>
            </template>
        </Suspense>
    </a-modal>
</template>

<script setup>
import { DEFAULT_CONFIG } from '@/constants';
import { ref, defineAsyncComponent, markRaw, reactive, toRefs, watch } from 'vue';
import { algorithmShortNames } from '@/api/algorithm.js'
import { toFixedDecimal } from '@/utils/tool.js'
import { useToolbar } from '@/composables/useToolbar.js'

import Divider from '@/components/Common/Divider/index.vue';
import AvatarImage from '@/components/Common/AvatarImage/index.vue';
const ManInfoForm = defineAsyncComponent(() => import('@/components/Comparison/ManInfoForm/index.vue'));
const ImageInfoForm = defineAsyncComponent(() => import('@/components/Comparison/ImageInfoForm/index.vue'))

const { getImageWebURL } = DEFAULT_CONFIG || {};

const props = defineProps({
    currentFace: {
        type: Object,
    },
    moduleName: {
        type: String
    },
    faceVisible: {
        type: Boolean
    },
    imageDataCount: {
        type: Number
    },
    showMoreBtn: {
        type: Boolean,
        default: true
    },
    comparisonType: {
        // 模式
        type: String,
        default: 'image-to-library',
        validator: (value) =>
            ['image-to-library', 'image-to-image'].includes(value),
    },
    showArrow: {
        type: Boolean,
        default: true
    }
})

const emits = defineEmits(['update:faceVisible'])

const toolbarMethods = reactive(useToolbar({ moduleName: props.moduleName }));
const {
    currentObjectData,
} = toRefs(toolbarMethods);
// 直接解构方法（保持原始函数调用）
const {
    loadObjectData,
    updateInformation,
    updataImageInfo
} = toolbarMethods;

const modelVisible = ref(false)
const modalContent = ref(null)
const handleModalContent = (type) => {
    let content = {}
    switch (type) {
        case 'manInfo':
            content = {
                titleIconClass: "man-info",
                titleText: '关键信息编辑',
                component: markRaw(ManInfoForm),
                props: {
                    involvedFaceId: props.currentFace.involvedFaceId,
                    initialData: currentObjectData.value.involvedInformation
                },
                events: {
                    'submit-success': updateInformation
                }
            }
            break;
        case "imageInfo":
            content = {
                titleIconClass: "image-info",
                titleText: '图像信息编辑',
                component: markRaw(ImageInfoForm),
                props: {
                    materialId: currentObjectData.value.subjectMaterialId,
                    initialData: currentObjectData.value.materialDetail
                },
                events: {
                    'submit-success': updataImageInfo
                }
            }
            break;
        default:
            break;
    }
    modalContent.value = content;
    modelVisible.value = true;
}

const toggleClick = () => {
    emits('update:faceVisible', !props.faceVisible)
}

watch(() => props.currentFace, (newData, oldData) => {
    if (newData?.involvedFaceId !== oldData?.involvedFaceId && newData) {
        loadObjectData(newData.involvedFaceId)
    }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.identity-image-container {
    height: 330px;
    min-height: 330px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 31px;
    padding-bottom: 8px;
}

.more-btn {
    width: 40px;
    height: 16px;
    background: #121318;
    border: 1px solid;
    border-color: #1e2128;
    border-radius: 8px;
    position: absolute;
    left: 44%;
    top: 6px;
}

.score-div {
    background: var(--color-bg-1);
}

.modal-header {
    margin-left: 12px;
    height: 50px;
    display: flex;
    align-items: center;
}
</style>