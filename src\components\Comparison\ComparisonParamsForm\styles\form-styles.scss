// 比对参数表单共享样式
// 从原 ComparisonParamsForm 组件中提取的样式，确保拆分后样式一致性

.comparison-params-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.header {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 48px;
  color: #FFFFFF;
}

.comparison-params-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  border-radius: 8px;
}

.form-row {
  display: flex;
  gap: 8px;
}

.trim {
  align-items: center;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  gap: 4px;
  width: 16px;
}

.dots-container {
  width: 16px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dots {
  display: block;
  width: 8px;
  height: 8px;
  line-height: 8px;
  background: #4975AC;
  border-radius: 8px;
}

.dashes {
  display: inline-block;
  width: 2px;
  height: calc(100% - 8px);
  background: linear-gradient(to bottom,
      #4975AC 0%,
      #4975AC 2px,
      transparent 2px,
      transparent 8px);
  background-size: 2px 6px;
}

.form-item {
  display: flex;
  flex: 1;

  &.vertical-item {
    flex-direction: column;
  }

  &.horizontal-item {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }
}

.form-label {
  align-items: center;
  height: 32px;
  display: flex;
  gap: 8px;
  white-space: nowrap;
}

.form-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  position: relative;
}

.form-input {
  width: 100%;
}

.form-text {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  width: 40px;
  height: 30px;
  background-color: #4C5F7833;
  color: #9EA6B2;
}

.form-footer {
  align-items: center;
  display: flex;
  justify-content: center;
  margin-top: 24px;
  width: 100%;
}

// 人像组下拉框样式
.select-library-dropdown {
  display: flex;
  gap: 16px;
  background: var(--color-primary-bg-image);
  border-radius: 8px;
  padding: 16px;
}

.group-column,
.library-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.library-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.library-item {
  padding: 8px;
}

.empty-libraries {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #9EA6B2;
}

// 滑块字段样式
.slider-div {
  position: relative;

  span{
    border-radius: 50%;
  }
}

.portrait-text{
  padding: 0 16px;
  cursor: pointer;
}

// 单选框样式
.radio-div {
  :deep(.ant-radio-group) {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  :deep(.ant-radio-wrapper) {
    color: #ffffff;
    margin: 0;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #293040;
    }
  }

  :deep(.ant-radio-checked .ant-radio-inner) {
    border-color: #4dbfff;
    background-color: #4dbfff;
  }

  :deep(.ant-radio-inner) {
    border-color: #4C5F78;
    background-color: transparent;
  }
}
