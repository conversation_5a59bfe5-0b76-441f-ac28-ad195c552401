import { createApp, h } from 'vue';

/**
 * Vue组件Tooltip管理器
 * 支持HTML字符串和Vue组件两种tooltip类型
 */
export class VueTooltipManager {
    constructor() {
        this.tooltipContainer = null;
        this.currentTooltipApp = null;
        this.isVisible = false;
    }

    /**
     * 初始化tooltip容器
     */
    init() {
        // 清理旧的容器
        this.cleanup();
        
        // 创建新的容器
        this.tooltipContainer = document.createElement('div');
        this.tooltipContainer.className = 'vue-relation-tooltip';
        this.tooltipContainer.style.cssText = `
            position: absolute;
            pointer-events: none;
            visibility: hidden;
            z-index: 9999;
            max-width: 300px;
        `;
        document.body.appendChild(this.tooltipContainer);
    }

    /**
     * 显示tooltip
     * @param {Object} options - 配置选项
     * @param {string|Object|Function} options.content - 内容：HTML字符串、Vue组件定义或渲染函数
     * @param {Object} options.data - 传递给Vue组件的数据
     * @param {Object} options.position - 位置 {x, y}
     * @param {Object} options.style - 样式配置
     * @param {Object} options.props - 传递给Vue组件的props
     */
    show(options) {
        const {
            content,
            data = {},
            position = { x: 0, y: 0 },
            style = {},
            props = {}
        } = options;

        if (!this.tooltipContainer) {
            this.init();
        }

        // 清理之前的Vue应用
        if (this.currentTooltipApp) {
            this.currentTooltipApp.unmount();
            this.currentTooltipApp = null;
        }

        // 根据内容类型处理
        if (typeof content === 'string') {
            // HTML字符串
            this.tooltipContainer.innerHTML = content;
        } else if (typeof content === 'object' || typeof content === 'function') {
            // Vue组件
            this.renderVueComponent(content, { ...props, ...data });
        }

        // 应用样式
        this.applyStyle(style);

        // 设置位置
        this.setPosition(position);

        // 显示tooltip
        this.tooltipContainer.style.visibility = 'visible';
        this.isVisible = true;
    }

    /**
     * 渲染Vue组件
     * @param {Object|Function} component - Vue组件定义或渲染函数
     * @param {Object} props - 传递给组件的props
     */
    renderVueComponent(component, props) {
        // 清空容器
        this.tooltipContainer.innerHTML = '';

        // 创建Vue应用
        let vueComponent;
        if (typeof component === 'function') {
            // 如果是渲染函数
            vueComponent = {
                render: () => component(props)
            };
        } else {
            // 如果是组件定义对象
            vueComponent = component;
        }

        this.currentTooltipApp = createApp(vueComponent, props);
        this.currentTooltipApp.mount(this.tooltipContainer);
    }

    /**
     * 应用样式
     * @param {Object} style - 样式对象
     */
    applyStyle(style) {
        const defaultStyle = {
            background: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '6px',
            fontSize: '14px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            border: '1px solid rgba(255,255,255,0.1)'
        };

        const finalStyle = { ...defaultStyle, ...style };

        Object.keys(finalStyle).forEach(key => {
            this.tooltipContainer.style[key] = finalStyle[key];
        });
    }

    /**
     * 设置tooltip位置
     * @param {Object} position - 位置 {x, y}
     */
    setPosition(position) {
        if (!this.tooltipContainer) return;

        const { x, y } = position;
        const offset = 10; // 偏移量，避免遮挡鼠标

        // 获取tooltip尺寸
        const rect = this.tooltipContainer.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 计算最佳位置，避免超出视窗
        let finalX = x + offset;
        let finalY = y - rect.height - offset;

        // 右侧超出时，显示在左侧
        if (finalX + rect.width > viewportWidth) {
            finalX = x - rect.width - offset;
        }

        // 上方超出时，显示在下方
        if (finalY < 0) {
            finalY = y + offset;
        }

        // 确保不超出左边界和上边界
        finalX = Math.max(5, finalX);
        finalY = Math.max(5, finalY);

        this.tooltipContainer.style.left = `${finalX}px`;
        this.tooltipContainer.style.top = `${finalY}px`;
    }

    /**
     * 更新tooltip位置（用于鼠标移动时）
     * @param {Object} position - 新位置 {x, y}
     */
    updatePosition(position) {
        if (this.isVisible) {
            this.setPosition(position);
        }
    }

    /**
     * 隐藏tooltip
     */
    hide() {
        if (this.tooltipContainer) {
            this.tooltipContainer.style.visibility = 'hidden';
        }
        this.isVisible = false;
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.currentTooltipApp) {
            this.currentTooltipApp.unmount();
            this.currentTooltipApp = null;
        }

        if (this.tooltipContainer && this.tooltipContainer.parentNode) {
            this.tooltipContainer.parentNode.removeChild(this.tooltipContainer);
            this.tooltipContainer = null;
        }

        this.isVisible = false;
    }

    /**
     * 检查是否可见
     */
    get visible() {
        return this.isVisible;
    }
}

/**
 * 创建默认的tooltip管理器实例
 */
export const defaultTooltipManager = new VueTooltipManager();
