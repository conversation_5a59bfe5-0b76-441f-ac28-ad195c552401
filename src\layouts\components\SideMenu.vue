<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-09 14:58:02
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-11 16:32:34
 * @FilePath: \vue3-js-template-master\src\layouts\components\SideMenu.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div :class="['side-menu-outer', { 'full-height': !isAutoHeight, 'auto-height': isAutoHeight }]">
    <a-menu class="side-menu-wrap" mode="inline" :selectedKeys="activePath" :items="menuWithIcons" @click="onMenuClick" :style="sideMenuStyle" />
  </div>
</template>

<script setup>
import { h, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import theme from '@/theme';
import * as Icons from '@ant-design/icons-vue';

const props = defineProps({
  menu: Array,
  selectedKeys: Array,
});

const router = useRouter();
const route = useRoute();
const activePath = computed(() => {
  const activeList = [route.path];
  const moduleName = route.path.split('/')[1];
  activeList.push(`/${moduleName}`);
  return activeList;
});

function onMenuClick({ key }) {
  if (key !== route.path) {
    router.push(key);
  }
}

// 判断当前路由是否需要内容自动撑开
const isAutoHeight = computed(() => !!route.meta?.sideMenuAutoHeight);

const sideMenuStyle = computed(() => ({
  width: '100px',
  background: theme.sideMenuBg,
  color: theme.sideMenuColor,
  borderRight: 0,
  minHeight: 'auto',
  margin: '0 auto',
  padding: 20,
  minWidth: 80,
  maxWidth: 80,
  borderRadius: 10,
}));

function mapMenuIcons(menu, activePath) {
  return (menu || []).map((item) => {
    // 判断当前项是否激活
    const isActive = activePath.includes(item.key);
    // 取 icon 或 activeIcon
    const iconMeta = isActive && item?.activeIcon ? item.activeIcon : item?.icon;

    let iconVNode = null;
    if (typeof iconMeta === 'string' && Icons[iconMeta]) {
      // antd icon 字符串
      iconVNode = h(Icons[iconMeta], { class: 'side-menu-anticon' });
    } else if (typeof iconMeta === 'string') {
      // 图片路径
      iconVNode = h('img', { src: iconMeta, class: 'side-menu-img' });
    } else if (iconMeta) {
      // antd icon 组件
      iconVNode = h(iconMeta, { class: 'side-menu-anticon' });
    }

    return {
      ...item,
      icon: '',
      label: h('div', { style: { display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '4px' } }, [
        iconVNode,
        h('div', {}, item.meta?.title || item.title),
      ]),
      children: item.children ? mapMenuIcons(item.children, activePath) : undefined,
      subMenus: item.subMenus ? mapMenuIcons(item.subMenus, activePath) : undefined,
    };
  });
}
const menuWithIcons = computed(() => mapMenuIcons(props.menu, activePath.value));
</script>

<style lang="scss" scoped>
.side-menu-outer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 90px;
  min-height: 0;
}
.side-menu-outer.full-height {
  height: 100%;
  justify-content: flex-start;
  background: v-bind('theme.sideMenuBg') !important;
}
.auto-height {
  .ant-menu {
    background: v-bind('theme.sideMenuBg') !important;
  }
}
.side-menu-wrap {
  background: transparent !important;
  color: #fff !important;
  padding: 20px 0;
  min-width: 80px;
  max-width: 80px;
  border-right: none;
  margin: 0 auto;
  border-radius: 10px;
}

/* 覆盖 antd menu hover 字体色 */
:deep(.ant-menu-item:hover),
:deep(.ant-menu-item-active) {
  color: v-bind('theme.sideMenuActiveColor') !important;
  background: transparent !important;
}
:deep(.ant-menu-item-selected) {
  color: v-bind('theme.sideMenuSelectedColor') !important;
  background: v-bind('theme.sideMenuSelectedBg') !important;
}

:deep(.ant-menu-item) {
  white-space: normal !important; /* 允许换行 */
  word-break: break-all !important; /* 长单词也能断开 */
  height: auto !important; /* 高度自适应内容 */
  line-height: 1.4 !important; /* 行高适中 */
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  /* 可根据实际调整padding */
}
:deep(.ant-menu-title-content) {
  white-space: normal !important;
  word-break: break-all !important;
}
:deep(.ant-menu-item .ant-menu-title-content > div) {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}
:deep(.side-menu-img) {
  width: 18px;
  height: 18px;
  display: block;
  margin: 0 auto;
}
:deep(.side-menu-anticon) {
  font-size: 18px !important;
  width: 18px;
  height: 18px;
  display: block;
  margin: 0 auto;
  line-height: 1;
  vertical-align: middle;
}
</style> 