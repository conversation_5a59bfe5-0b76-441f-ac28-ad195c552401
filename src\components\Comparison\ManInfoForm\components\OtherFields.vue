<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 17:39:49
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 10:55:03
 * @FilePath: \platform-face-web\src\components\Comparison\ManInfoForm\components\OtherFields.vue
 * @Description: 其他信息字段组件
 * 包含肤色、备注等其他补充信息字段
 * 
-->
<template>
  <div class="other-fields">
    <!-- 肤色选择 -->
    <a-form-item label="肤色" name="humanRace">
      <a-radio-group v-model:value="formData.humanRace">
        <a-radio value="yellow">黄皮肤</a-radio>
        <a-radio value="black">黑皮肤</a-radio>
        <a-radio value="white">白皮肤</a-radio>
        <a-radio value="unknown">未知</a-radio>
      </a-radio-group>
    </a-form-item>

    <!-- 备注信息 -->
    <a-form-item label="备注" name="description">
      <a-input v-model:value="formData.description" placeholder="请输入备注" />
    </a-form-item>
  </div>
</template>

<script setup>
defineProps({
  /**
   * 表单数据
   */
  formData: {
    type: Object,
    required: true
  }
});
</script>

<style scoped>
.other-fields {
  /* 继承父组件样式 */
}
</style>
