<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 15:45:00
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 15:45:00
 * @FilePath: \platform-face-web\src\components\Business\GroupedImageDisplay\components\SingleLayout.vue
 * @Description: 单张图片布局组件
 -->
<template>
  <div class="single-layout">
    <ImageItem
      :image="image"
      :selected-items="selectedItems"
      :ring-color="ringColor"
      :selected-ring-color="selectedRingColor"
      :ring-thickness="ringThickness"
      :thumbnail-size="thumbnailSize"
      @dragstart="handleDragStart"
      @click="handleClick"
    />
  </div>
</template>

<script setup>
import ImageItem from './ImageItem.vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 图片数据对象 */
  image: {
    type: Object,
    required: true
  },
  /** 已选中的图片ID数组 */
  selectedItems: {
    type: Array,
    default: () => []
  },
  /** 默认边框颜色 */
  ringColor: {
    type: String,
    default: '#4c5f78'
  },
  /** 选中状态边框颜色 */
  selectedRingColor: {
    type: String,
    default: '#4DBFFF'
  },
  /** 边框厚度 */
  ringThickness: {
    type: Number,
    default: 0
  }
});

/**
 * 组件事件定义
 */
const emit = defineEmits(['dragstart', 'click']);

/**
 * 缩略图尺寸配置
 */
const thumbnailSize = {
  width: '75px',
  height: '48px',
  bottom: '0px',
  right: '2px'
};

/**
 * 事件处理方法
 */
const handleDragStart = (event, image) => {
  emit('dragstart', event, image);
};

const handleClick = (event, image) => {
  emit('click', event, image);
};
</script>

<style lang="scss" scoped>
.single-layout {
  width: 100%;
  height: 100%;
}
</style>
