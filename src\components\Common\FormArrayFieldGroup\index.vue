<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 17:32:57
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 10:10:11
 * @FilePath: \platform-face-web\src\components\Common\FormArrayFieldGroup\index.vue
 * @Description: 数组字段组组件
 * 通用的数组字段组件，支持动态增删，适用于曾用名、别名、证件号、居住地等场景
 * 
-->
<template>
  <a-form-item :label="label">
    <div v-for="(item, index) in modelValue" :key="`${fieldKey}-${index}`" class="flex-row">
      <!-- 字段内容插槽 -->
      <slot :item="item" :index="index" :update-item="updateItem" />

      <!-- 操作按钮 -->
      <div class="button-container">
        <template v-if="modelValue.length > minItems">
          <a class="link" @click="removeItem(index)">删除</a>
        </template>
        <template v-if="index === modelValue.length - 1">
          <a class="link" @click="addItem">新增一条</a>
        </template>
      </div>
    </div>
  </a-form-item>
</template>

<script setup>
const props = defineProps({
  /**
   * 字段标签
   */
  label: {
    type: String,
    required: true
  },

  /**
   * 双向绑定的数组值
   */
  modelValue: {
    type: Array,
    required: true
  },

  /**
   * 字段唯一标识，用于生成key
   */
  fieldKey: {
    type: String,
    required: true
  },

  /**
   * 最小项目数量
   */
  minItems: {
    type: Number,
    default: 1
  },

  /**
   * 新增项的默认值
   */
  defaultValue: {
    type: [String, Object],
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

/**
 * 添加新项
 */
const addItem = () => {
  const newArray = [...props.modelValue, props.defaultValue];
  emit('update:modelValue', newArray);
};

/**
 * 删除指定项
 */
const removeItem = (index) => {
  const newArray = props.modelValue.filter((_, i) => i !== index);
  emit('update:modelValue', newArray);
};

/**
 * 更新指定项的值
 */
const updateItem = (index, value) => {
  const newArray = [...props.modelValue];
  newArray[index] = value;
  emit('update:modelValue', newArray);
};
</script>

<style scoped>
.flex-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.button-container {
  display: flex;
  justify-content: flex-start;
  margin-left: 8px;
  gap: 8px;
  width: 21%;
}

.link {
  color: #00bfff;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}
</style>
