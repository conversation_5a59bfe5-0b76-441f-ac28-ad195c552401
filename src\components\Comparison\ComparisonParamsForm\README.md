# ComparisonParamsForm 比对参数表单组件

## 📖 组件概述

ComparisonParamsForm 是一个高度可配置的比对参数表单组件，支持通过预设配置或自定义字段配置来生成不同类型的比对表单。它内置了多种布局和字段类型，并支持通过插槽进行灵活扩展。

## ✨ 主要功能

- 🎯 **预设配置**：内置“大库检索”和“同人预判”两种常用配置。
- 🔧 **深度自定义**：通过 `fieldConfig` 可完全自定义字段、顺序、标签甚至部分样式。
- 🎨 **布局模式**：支持垂直（`vertical`）和水平（`horizontal`）两种布局。
- 🔌 **插槽扩展**：允许通过具名插槽在指定字段后添加自定义操作（如按钮）。
- 🔄 **状态管理**：内置完整的表单数据管理和验证逻辑。
- 🎪 **事件系统**：提供丰富的事件回调，如 `submit-success` 和 `comparison-change`。

## 🏗️ 组件架构

```
src/components/Comparison/ComparisonParamsForm/
├── index.vue                    # 主组件
├── components/                  # 子组件目录
│   ├── PortraitGroupField.vue   # 人像组字段组件
│   ├── AlgorithmField.vue       # 算法字段组件
│   └── SliderField.vue          # 滑块字段组件
├── composables/                 # 逻辑复用
│   ├── useFormData.js           # 表单数据管理
│   └── useFieldConfig.js        # 字段配置管理
├── styles/
│   └── form-styles.scss         # 共享样式文件
└── README.md                    # 组件文档
```

## 📋 Props 属性

| 属性名                 | 类型    | 默认值                                  | 说明                                                      |
| ---------------------- | ------- | --------------------------------------- | --------------------------------------------------------- |
| `headTitle`            | String  | `'比对配置'`                            | 组件头部的标题。                                          |
| `headIcon`             | String  | `'comparison-comparison-strategy-high'` | 组件头部的图标。                                          |
| `defaultParams`        | Object  | `{...}`                                 | 表单的默认参数。                                          |
| `comparisonType`       | String  | `'image-to-library'`                    | 比对模式，可选 `['image-to-library', 'image-to-image']`。 |
| `algorithmOptions`     | Array   | `[]`                                    | 可选算法列表。                                            |
| `portraitGroupOptions` | Array   | `[]`                                    | 可选人像组/库列表。                                       |
| `layout`               | String  | `'vertical'`                            | 布局，可选 `['vertical', 'horizontal']`。                 |
| `hiddenSubmitButton`   | Boolean | `false`                                 | 是否隐藏提交按钮。                                        |
| `requirePortraitFirst` | Boolean | `false`                                 | 是否要求必须先选择人像组。                                |
| `fieldConfig`          | Array   | `null`                                  | 自定义字段配置，优先级高于预设。                          |
| `isShowIndex`          | Boolean | `true`                                  | 是否显示字段前的序号。                                    |
| `preset`               | String  | `'auto'`                                | 预设配置，可选 `['auto', 'library', 'image', 'custom']`。 |

## 🎨 字段配置 (fieldConfig)

通过 `fieldConfig` 数组，你可以精确控制每个表单项的行为和外观。

### 通用配置

| 属性        | 类型    | 说明                                       |
| ----------- | ------- | ------------------------------------------ |
| `type`      | String  | **必需**。字段类型，如 `'portraitGroup'`。 |
| `label`     | String  | 字段标签文本。                             |
| `showLabel` | Boolean | 是否显示标签，默认为 `true`。              |

### 特定字段配置

- **`portraitGroup` (人像组)**

  - `buttonBg`: String - 自定义按钮背景色。
  - `fontSize`: String - 自定义按钮字体大小。

- **`numberOfReturns` (返回结果数)**
  - `marks`: Object - 自定义滑块的刻度标记，例如 `{ 0: '0', 50: '50' }`。

### 示例

```javascript
const customConfig = [
  // 不显示标签，并自定义按钮样式
  {
    type: 'portraitGroup',
    label: '选择人像组/库',
    showLabel: false,
    buttonBg: 'transparent',
    fontSize: '14px',
  },
  // 自定义滑块刻度
  {
    type: 'numberOfReturns',
    label: '返回结果数量',
    marks: { 0: '0', 10: '10', 20: '20', 30: '30', 40: '40', 50: '50' },
  },
];
```

## 🔌 Slots 插槽

### `item-actions`

允许你在每个表单项的操作区后添加自定义内容。

| 插槽 Prop | 类型   | 说明                 |
| --------- | ------ | -------------------- |
| `field`   | Object | 当前字段的配置对象。 |
| `index`   | Number | 当前字段的索引。     |

## 🚀 使用方式

### 1. 基础使用 (自动预设)

```vue
<template>
  <ComparisonParamsForm
    :comparison-type="'image-to-library'"
    :algorithm-options="algorithms"
    :portrait-group-options="groups"
    @submit-success="handleSubmit" />
</template>
```

### 2. 高级自定义与插槽扩展

此示例展示了如何使用 `fieldConfig` 进行深度定制，并利用 `item-actions` 插槽添加一个管理按钮。

```vue
<template>
  <ComparisonParamsForm
    :field-config="customFieldConfig"
    layout="horizontal"
    :hidden-submit-button="true"
    :is-show-index="false"
    head-title="参数设置"
    head-icon="comparison-comparison-strategy-gray">
    <template #item-actions="{ field }">
      <div v-if="field.type === 'portraitGroup'" class="ml-1">
        <CustomButtonWithTooltip width="120px" height="32px" font-size="12px"> 人像组/库管理 </CustomButtonWithTooltip>
      </div>
    </template>
  </ComparisonParamsForm>
</template>

<script setup>
import { ref } from 'vue';

const customFieldConfig = ref([
  {
    type: 'portraitGroup',
    label: '选择人像组/库',
    showLabel: false,
    buttonBg: 'transparent',
    fontSize: '14px',
  },
  {
    type: 'numberOfReturns',
    label: '返回结果数量',
    marks: { 0: '0', 10: '10', 20: '20', 30: '30', 40: '40', 50: '50' },
  },
]);
</script>
```

## 📤 Events 事件

| 事件名              | 参数                                   | 说明                                 |
| ------------------- | -------------------------------------- | ------------------------------------ |
| `submit-success`    | `{ params: Object, changed: Boolean }` | 提交表单数据事件。                   |
| `comparison-change` | `formData: Object`                     | 表单数据变更事件（仅大库检索模式）。 |

## 🔧 Methods 方法

| 方法名          | 返回值 | 说明               |
| --------------- | ------ | ------------------ |
| `getFormData()` | Object | 获取当前表单数据。 |
