<!--
/**
 * 滑块字段组件
 * 
 * @description 通用滑块组件，用于返回结果数和比对阈值字段
 * <AUTHOR>
 * @version 1.0.0
 */
-->
<template>
  <div class="w-100 d-flex flex-1 g-1 slider-div">
    <a-slider :value="modelValue" class="form-input" :max="max" :min="min" :step="step" :disabled="disabled"
      @change="handleChange" :marks="marks" />
    <span class="form-text">{{ modelValue }}</span>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: [Number, String],
    required: true
  },
  max: {
    type: Number,
    default: 100
  },
  min: {
    type: Number,
    default: 0
  },
  step: {
    type: Number,
    default: 1
  },
  disabled: {
    type: Boolean,
    default: false
  },
  marks: {
    type: Object,
    default: () => []
  }
});

const emits = defineEmits(['update:modelValue', 'change']);

const handleChange = (value) => {
  emits('update:modelValue', value);
  emits('change', value);
};
</script>

<style scoped lang="scss">
@use '../styles/form-styles.scss';
</style>
