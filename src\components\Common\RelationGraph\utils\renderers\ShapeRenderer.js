/**
 * 形状渲染器 - 负责渲染各种节点形状
 * 支持圆形、圆角矩形等多种形状
 */

/**
 * 圆形渲染器
 */
export class CircleRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染圆形节点
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     * @param {number} radius - 半径
     * @param {string} nodeType - 节点类型 ('center' | 'other')
     */
    render(container, node, radius, nodeType = 'other') {
        const styleConfig = this.configManager.get(`nodes.${nodeType}.style`);

        return container
            .append('circle')
            .attr('r', radius)
            .attr('fill', styleConfig.fill || '#16171c')
            .attr('stroke', styleConfig.stroke || '#4DBFFF')
            .attr('stroke-width', styleConfig.strokeWidth || 1);
    }

    /**
     * 渲染带图片的圆形节点
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     * @param {number} radius - 半径
     * @param {string} nodeType - 节点类型
     */
    renderWithImage(container, node, radius, nodeType = 'other') {
        // 先渲染基础圆形
        this.render(container, node, radius, nodeType);

        // 添加图片
        if (node.imageId || node?.portrait?.standardPortraitImages?.[0]?.imageId) {
            this.addCircleImage(container, node, radius, nodeType);
        }
    }

    /**
     * 添加圆形裁剪的图片
     */
    addCircleImage(container, node, radius, nodeType) {
        const imageConfig = this.configManager.get(`nodes.${nodeType}.image`);
        const imageUrl = node?.portrait?.standardPortraitImages?.[0]?.imageId || node?.imageId;

        // 创建圆形裁剪路径
        const clipId = `circle-clip-${Math.random().toString(36).substr(2, 9)}`;

        container
            .append('defs')
            .append('clipPath')
            .attr('id', clipId)
            .append('circle')
            .attr('r', radius - 2); // 稍微小一点，避免边框被裁剪

        // 添加图片
        container
            .append('foreignObject')
            .attr('x', -radius)
            .attr('y', -radius)
            .attr('width', radius * 2)
            .attr('height', radius * 2)
            .attr('clip-path', `url(#${clipId})`)
            .append('xhtml:div')
            .attr(
                'style',
                `
                width: 100%;
                height: 100%;
                border-radius: 50%;
                background-image: url(${imageUrl});
                background-size: ${imageConfig.backgroundSize || 'cover'};
                background-position: ${imageConfig.backgroundPosition || 'center'};
            `,
            );
    }
}

/**
 * 圆角矩形渲染器
 */
export class RoundedRectRenderer {
    constructor(configManager) {
        this.configManager = configManager;
    }

    /**
     * 渲染圆角矩形节点（保持圆形边框，内部图片为圆角矩形）
     * @param {d3.Selection} container - D3容器选择器
     * @param {Object} node - 节点数据
     * @param {number} radius - 半径
     * @param {string} nodeType - 节点类型
     */
    render(container, node, radius, nodeType = 'other') {
        const styleConfig = this.configManager.get(`nodes.${nodeType}.style`);

        // 保持圆形边框
        return container
            .append('circle')
            .attr('r', radius)
            .attr('fill', styleConfig.fill || '#16171c')
            .attr('stroke', styleConfig.stroke || '#4DBFFF')
            .attr('stroke-width', styleConfig.strokeWidth || 1);
    }

    /**
     * 渲染带图片的圆角矩形节点
     */
    renderWithImage(container, node, radius, nodeType = 'other') {
        // 先渲染基础矩形
        this.render(container, node, radius, nodeType);

        // 添加图片
        if (node.imageId || node?.portrait?.standardPortraitImages?.[0]?.imageId) {
            this.addRoundedRectImage(container, node, radius, nodeType);
        }
    }

    /**
     * 添加圆角矩形裁剪的图片
     */
    addRoundedRectImage(container, node, radius, nodeType) {
        const imageConfig = this.configManager.get(`nodes.${nodeType}.image`);
        const imageUrl = node?.portrait?.standardPortraitImages?.[0]?.imageId || node?.imageId;

        // 图片尺寸稍小于圆形半径，留出边框空间
        const imageSize = (radius - 2) * 1.4; // 圆角矩形图片大小
        const borderRadius = imageConfig.borderRadius || 12;

        container
            .append('foreignObject')
            .attr('x', -imageSize / 2)
            .attr('y', -imageSize / 2)
            .attr('width', imageSize)
            .attr('height', imageSize)
            .append('xhtml:div')
            .attr(
                'style',
                `
                width: 100%;
                height: 100%;
                border-radius: ${borderRadius}px;
                background-image: url(${imageUrl});
                background-size: ${imageConfig.backgroundSize || 'cover'};
                background-position: ${imageConfig.backgroundPosition || 'center'};
            `,
            );
    }
}

/**
 * 形状渲染器工厂
 */
export class ShapeRendererFactory {
    constructor(configManager) {
        this.configManager = configManager;
        this.renderers = {
            circle: new CircleRenderer(configManager),
            'rounded-rect': new RoundedRectRenderer(configManager),
        };
    }

    /**
     * 获取指定形状的渲染器
     * @param {string} shape - 形状类型
     * @returns {CircleRenderer|RoundedRectRenderer}
     */
    getRenderer(shape) {
        return this.renderers[shape] || this.renderers.circle;
    }

    /**
     * 渲染节点形状
     * @param {d3.Selection} container - D3容器
     * @param {Object} node - 节点数据
     * @param {number} radius - 半径
     * @param {string} nodeType - 节点类型
     * @param {boolean} withImage - 是否包含图片
     */
    renderShape(container, node, radius, nodeType = 'other', withImage = true) {
        const shapeType = this.configManager.get(`nodes.${nodeType}.image.clipPath`) || 'circle';
        const renderer = this.getRenderer(shapeType);

        if (withImage) {
            return renderer.renderWithImage(container, node, radius, nodeType);
        } else {
            return renderer.render(container, node, radius, nodeType);
        }
    }
}
