<!--
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-24 15:26:06
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-24 16:19:55
 * @FilePath: \platform-face-web\src\components\Comparison\MatchingResultHeader\index.vue
 * @Description: 用于显示当前比对结果的头部信息
-->
<template>
  <div class="matching-result-header">
    <div class="result-image">
      <a-dropdown :trigger="['click']" placement="bottom" :get-popup-container="getPopupContainer">
        <AvatarImage :src="getImageWebURL(currentFace.multiFaceData.sourceImageId)" :gap="3" :ring-thickness="1"
          ring-color="#B0BBCC" width="112px" height="112px" :status-bottom-right="{ bottom: '7px', right: '3px' }"
          :face-x="currentFace.multiFaceData.items[0].coordinate[0]"
          :face-y="currentFace.multiFaceData.items[0].coordinate[1]"
          :face-width="currentFace.multiFaceData.items[0].coordinate[2]"
          :face-height="currentFace.multiFaceData.items[0].coordinate[3]" container-width="100%" container-height="100%"
          class="cursor-pointer">
          <template #statusBottomRight>
            <div style="background: #16171c;border-radius: 50%;">
              <SvgIcon icon-class="ellipsis" size="16px"></SvgIcon>
            </div>
          </template>
        </AvatarImage>
        <template #overlay>
          <div class="dropdown-container">
            <LoadMoreList :data="list" layout-type="vertical" gap-size="24px" :has-more="false" :loading="false"
              @update:selected-id="handleGridSelect">
              <template #default="{ item }">
                <AvatarImage :src="getImageWebURL(item[0].imageId)" width="132px" height="132px"
                  style="padding-left: 6px;"
                  :ring-color="item?.faces?.[0].involvedFaceId === currentFace.involvedFaceId ? '#90fbc9' : '#4c5f78'"
                  :face-x="item[0].multiFaceData.items[0].coordinate[0]"
                  :face-y="item[0].multiFaceData.items[0].coordinate[1]"
                  :face-width="item[0].multiFaceData.items[0].coordinate[2]"
                  :face-height="item[0].multiFaceData.items[0].coordinate[3]" container-width="100%"
                  container-height="100%">
                  <template #statusBottomRight>
                    <div class="d-flex align-items-center g-2"
                      style="width: 80px; height: 24px; position: absolute; bottom: 12px; right: -50px;">
                      <SvgIcon icon-class="doubt-gray" size="20px"></SvgIcon>
                      <span class="ml-1">{{
                        item[0]?.assumedPortrait?.name
                      }}</span>
                    </div>
                  </template>
                </AvatarImage>
              </template>
            </LoadMoreList>
          </div>
        </template>
      </a-dropdown>
    </div>
    <div class="result-summary">
      <p>可能是</p>
      <div class="d-flex align-items-center g-2">
        <span style="color: #FFF; font-size: 16px; font-weight: 700;">{{
          currentFace?.recommendedPortrait?.portrait?.xm
        }}</span>
        <Divider direction="vertical" color="#40516A" height="10px" />
        <CustomButtonWithTooltip tooltip="比对报告" icon-name="person-archive-high" icon-size="18px" button-bg="#202531"
          hover-bg="#3A4A5F"></CustomButtonWithTooltip>
        <Divider direction="vertical" color="#40516A" height="10px" />
        <CustomButtonWithTooltip tooltip="导出" icon-name="export-xlsx-blue" icon-size="18px" button-bg="#202531"
          hover-bg="#3A4A5F"></CustomButtonWithTooltip>
        <Divider v-if="isShowThirdButton" direction="vertical" color="#40516A" height="10px" />
        <CustomButtonWithTooltip v-if="isShowThirdButton" :tooltip="thirdButtonTooltipTitle" :icon-name="thirdIconName"
          icon-size="18px" button-bg="#202531" hover-bg="#3A4A5F" @click="handleClick('analysisJudgment')">
        </CustomButtonWithTooltip>
        <Divider v-if="isShowFourthButton" direction="vertical" color="#40516A" height="10px" />
        <CustomButtonWithTooltip v-if="isShowFourthButton" tooltip="历史记录" icon-name="history-high" icon-size="18px"
          button-bg="#202531" hover-bg="#3A4A5F" @click="handleClick('historicalRecords')">
        </CustomButtonWithTooltip>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, ref } from 'vue';
import { DEFAULT_CONFIG } from '@/constants';

import Divider from '@/components/Common/Divider/index.vue';
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';
import AvatarImage from '@/components/Common/AvatarImage/index.vue';
import LoadMoreList from '@/components/Common/LoadMoreList/index.vue';

const props = defineProps({
  // 当前展示的人像数据
  current: {
    type: Object
  },
  // 人像列表数据
  list: {
    type: Array
  },
  // 第三个按钮的图标
  thirdIconName: {
    type: String,
    default: 'bar-chart'
  },
  thirdButtonTooltipTitle: {
    type: String,
    default: '快速研判'
  },
  // 是否显示第三个按钮
  isShowThirdButton: {
    type: Boolean,
    default: true
  },
  // 是否显示第四个按钮
  isShowFourthButton: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['handleGridSelect', 'handleBtnClick'])

const currentFace = computed(() => {
  return props.current
})

const getPopupContainer = (triggerNode) => {
  // 尝试返回 Popover 的容器，如果没找到则回退到 body
  return document.querySelector('.popover') || document.body;
};

const { getImageWebURL } = DEFAULT_CONFIG || {};

const handleGridSelect = (item) => {
  emits('handleGridSelect', item)
}

const handleClick = (type) => {
  emits('handleBtnClick', type)
}

</script>

<style lang="scss" scoped>
.matching-result-header {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  width: 100%;
  height: 110px;
  position: relative;
}

.result-image {
  position: absolute;
  top: 20px;
  left: 40px;
}

.result-summary {
  position: absolute;
  top: calc(50% - 22px);
  left: 180px;
  height: 45px;
  color: #B0BBCC;
}

.dropdown-container {
  width: 300px;
  height: 400px;
  background-color: #242A36 !important;
  background: #242A36 !important;
  border-radius: 8px !important;
  scrollbar-gutter: stable;
}
</style>