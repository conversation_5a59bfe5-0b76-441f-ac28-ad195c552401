# Common 公共组件自动全局注册说明

## 功能简介

本目录下所有组件会被自动全局注册，无需手动 import，可在任意页面/组件中直接使用。

---

## 注册规则

- 采用插件自动注册，已在 `src/main.js` 中通过 `app.use()` 全局引入。
- 组件名规则：
  - 如果组件为 `某目录/Index.vue`，注册名为该目录名（如 `CommonUpload/Index.vue` 注册为 `<CommonUpload />`）。
  - 如果组件为 `某目录/Other.vue`，注册名为“目录名+文件名”（如 `CommonUpload/Other.vue` 注册为 `<CommonUploadOther />`）。
  - 如果是 `Common` 目录下的一级 `.vue` 文件，如 `Test.vue`，注册名为 `<Test />`。

---

## 如何添加新公共组件

1. 在 `src/components/Common/` 下新建文件夹（如 `MyWidget`）。
2. 在该文件夹下新建 `Index.vue`，即 `MyWidget/Index.vue`。
3. 组件会自动注册为 `<MyWidget />`，可直接在任何页面/组件中使用。
4. 如需同目录下有多个组件，可新建其他 `.vue` 文件（如 `MyWidget/Panel.vue`），注册名为 `<MyWidgetPanel />`。

---

## 使用示例

```vue
<template>
  <CommonUpload />
  <DynamicNumber :start="0" :end="1000" />
  <MyWidget />
  <MyWidgetPanel />
</template>
```

---

## 注意事项

- 组件名区分大小写，建议文件夹和文件名采用大驼峰（如 `CommonUpload`、`DynamicNumber`）。
- 不建议多个组件都叫 `Index.vue` 且放在同一目录下，否则会冲突。
- 只会注册 `Common` 目录下的所有子目录及一级 `.vue` 文件。
- 组件名如有特殊需求可调整注册逻辑。

---

## 典型场景

- 上传、动态数字、通用按钮、弹窗等平台级复用组件。

如有更多需求请补充或联系维护者。 