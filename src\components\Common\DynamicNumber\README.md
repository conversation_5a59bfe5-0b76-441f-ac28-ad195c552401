# 动态数字滚动组件（DynamicNumber）

## 组件简介

用于数字动态增长/滚动展示效果，常用于仪表盘、统计卡、数据看板等场景。支持自定义起止值、动画时长、小数位、格式化等。

---

## 基本用法

```vue
<DynamicNumber :start="0" :end="12345" :duration="2000" :decimals="2" />
```

---

## Props 参数

| 参数       | 说明                         | 类型     | 默认值   |
| ---------- | ---------------------------- | -------- | -------- |
| start      | 起始值                       | Number   | 0        |
| end        | 目标值（必填）               | Number   | -        |
| duration   | 动画时长（毫秒）             | Number   | 1500     |
| decimals   | 小数位数                     | Number   | 0        |
| formatter  | 格式化函数（如千分位等）     | Function | -        |

---

## 格式化用法

- 支持自定义格式化函数，如千分位、前缀、后缀等：

```vue
<DynamicNumber :start="0" :end="10000" :formatter="val => Number(val).toLocaleString()" />
```

---

## 进阶用法

- 目标值（end）变化时，数字会自动重新滚动。
- 支持小数位数控制：

```vue
<DynamicNumber :start="0" :end="3.14159" :decimals="3" />
```

- 可结合插槽或外部样式自定义数字外观。

---

## 示例

### 1. 基础数字滚动
```vue
<DynamicNumber :start="0" :end="8888" />
```

### 2. 千分位格式化
```vue
<DynamicNumber :start="0" :end="1234567" :formatter="val => Number(val).toLocaleString()" />
```

### 3. 动画时长与小数
```vue
<DynamicNumber :start="1.5" :end="9.99" :duration="3000" :decimals="2" />
```

---

## 注意事项

- 组件内部使用 requestAnimationFrame 实现平滑动画，性能友好。
- formatter 优先级高于 decimals。
- 目标值变化时会自动重新滚动。
- 如需更复杂的格式化（如带单位、前缀、后缀），建议用 formatter 实现。

---

## 典型场景

- 数据统计卡、仪表盘、首页大屏、增长趋势等动态数字展示。

如有更多需求请补充 props 或联系维护者。 