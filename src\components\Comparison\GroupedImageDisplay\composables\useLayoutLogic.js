/**
 * @Author: CaiXiaomin
 * @Date: 2025-07-22 15:35:00
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-22 15:35:00
 * @FilePath: \platform-face-web\src\components\Business\GroupedImageDisplay\composables\useLayoutLogic.js
 * @Description: 布局逻辑计算 - 根据图片数量确定布局类型和图片分组
 */

import { computed } from 'vue';

/**
 * 布局逻辑处理 composable
 * @param {Object} props - 组件props
 * @returns {Object} 布局相关的计算属性和方法
 */
export function useLayoutLogic(props) {
  /**
   * 根据图片数量确定布局类型
   */
  const gridType = computed(() => {
    const len = props.groupedImages.length;
    if (len === 1) return 'single';
    if (len === 2) return '1x2';
    if (len > 2 && len <= 4) return '2x2';
    if (len > 4 && len <= 7) return 'nested';
    if (len > 7) return 'nested-more';
    return 'default';
  });

  /**
   * 主网格显示的前三张图片（用于嵌套布局）
   */
  const firstThreeImages = computed(() => {
    return props.groupedImages.slice(0, 3);
  });

  /**
   * 嵌套网格中的图片
   * 处理嵌套布局下：从第四张开始的图片，最多显示 4 个单元（2x2 网格）
   * 当图片数量超过 7 时，显示前三个加上第四个覆盖更多图标
   */
  const nestedImages = computed(() => {
    if (gridType.value === 'nested-more') {
      // 仅展示 4 个嵌套网格单元
      return props.groupedImages.slice(3, 7);
    }
    return props.groupedImages.slice(3);
  });

  /**
   * 获取不同布局下的缩略图尺寸配置
   * @param {string} layoutType - 布局类型
   * @param {boolean} isNested - 是否为嵌套网格中的图片
   * @returns {Object} 缩略图尺寸配置
   */
  const getThumbnailSize = (layoutType, isNested = false) => {
    if (isNested) {
      return {
        width: '36px',
        height: '24px',
        bottom: '2px',
        right: '2px'
      };
    }

    switch (layoutType) {
      case 'single':
      case '1x2':
        return {
          width: '75px',
          height: '48px',
          bottom: '2px',
          right: '2px'
        };
      case '2x2':
      case 'nested':
      case 'nested-more':
        return {
          width: '54px',
          height: '36px',
          bottom: '2px',
          right: '2px'
        };
      default:
        return {
          width: '75px',
          height: '48px',
          bottom: '2px',
          right: '2px'
        };
    }
  };

  /**
   * 计算"更多"覆盖层显示的数量
   */
  const moreCount = computed(() => {
    return Math.max(0, props.groupedImages.length - 7);
  });

  return {
    gridType,
    firstThreeImages,
    nestedImages,
    getThumbnailSize,
    moreCount
  };
}
