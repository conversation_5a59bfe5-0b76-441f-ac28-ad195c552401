<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 10:52:07
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-23 11:02:23
 * @FilePath: \platform-face-web\src\components\Business\GalleryFilterDropdown\components\FilterItem.vue
 * @Description: 筛选功能的item子组件
 * 
-->

<template>
    <div class="filter-parent-item" :class="`level-${level}`">
        <!-- 当前选项 -->
        <div class="filter-item" :class="{ selected: isSelected || allChildrenSelected }" @click="handleItemClick">
            <div class="d-flex align-items-center g-2">
                <SvgIcon v-if="item.iconName" :icon-class="item.iconName" size="14px" />
                <span>{{ item.label }}</span>
                <span v-if="item.number" class="number">{{ item.number }}</span>
            </div>
            <SvgIcon v-if="isSelected || allChildrenSelected" icon-class="checked" width="8px" height="10px" />
        </div>

        <!-- 子选项 -->
        <div v-if="hasChildren" class="filter-children">
            <FilterItem v-for="(child, childIdx) in item.children" :key="`child-${childIdx}`" :item="child"
                :selected-values="selectedValues" :level="level + 1" @toggle="handleChildToggle" />
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    item: {
        type: Object,
        required: true
    },
    selectedValues: {
        type: Array,
        default: () => []
    },
    level: {
        type: Number,
        default: 0
    }
})

const emits = defineEmits(['toggle'])

const hasChildren = computed(() => {
    return props.item.children && props.item.children.length
})

const isSelected = computed(() => {
    return props.selectedValues.includes(props.item.value)
})

const allChildrenSelected = computed(() => {
    if (!hasChildren.value) return false
    return shouldSelectParent(props.item, props.selectedValues)
})

const getAllChildValues = (item) => {
    let values = [];
    if (item.children && item.children.length) {
        item.children.forEach(child => {
            values.push(child.value);
            values = values.concat(getAllChildValues(child));
        });
    }
    return values;
};

const shouldSelectParent = (item, selectedValues) => {
    if (!item.children || !item.children.length) return false;

    // 获取所有子项的值（包括嵌套子项）
    const allChildValues = getAllChildValues(item);

    // 检查是否所有子项都被选中
    return allChildValues.length > 0 &&
        allChildValues.every(value => selectedValues.includes(value));
};

const handleItemClick = () => {
    const newValues = [...props.selectedValues];
    const shouldSelect = !(isSelected.value || allChildrenSelected.value);

    // 处理当前项
    const currentIndex = newValues.indexOf(props.item.value);
    if (shouldSelect) {
        if (currentIndex === -1) {
            newValues.push(props.item.value);
        }
    } else {
        if (currentIndex > -1) {
            newValues.splice(currentIndex, 1);
        }
    }

    // 递归处理所有子项（包括嵌套子项）
    const toggleChildren = (item, select) => {
        if (select) {
            if (!newValues.includes(item.value)) {
                newValues.push(item.value);
            }
        } else {
            const index = newValues.indexOf(item.value);
            if (index > -1) {
                newValues.splice(index, 1);
            }
        }

        if (item.children && item.children.length) {
            item.children.forEach(child => toggleChildren(child, select));
        }
    };

    // 处理所有子项
    if (hasChildren.value) {
        props.item.children.forEach(child => toggleChildren(child, shouldSelect));
    }

    emits('toggle', newValues);
};

const handleChildToggle = (valueOrValues) => {
    if (Array.isArray(valueOrValues)) {
        emits('toggle', valueOrValues);
        return;
    }

    // 先处理子项本身的切换
    const newValues = [...props.selectedValues];
    const index = newValues.indexOf(valueOrValues);
    const isNowSelected = index === -1;

    if (isNowSelected) {
        newValues.push(valueOrValues);
    } else {
        newValues.splice(index, 1);
    }

    // 递归检查是否需要更新父项状态
    const checkParentSelection = (item, values) => {
        if (!item.children || !item.children.length) return;

        const allChildValues = getAllChildValues(item);
        const allSelected = allChildValues.every(value => values.includes(value));
        const parentIndex = values.indexOf(item.value);

        if (allSelected) {
            if (parentIndex === -1) {
                values.push(item.value);
            }
        } else {
            if (parentIndex > -1) {
                values.splice(parentIndex, 1);
            }
        }
    };

    // 检查当前项的所有父级
    checkParentSelection(props.item, newValues);

    emits('toggle', newValues);
};

</script>

<style scoped>
.filter-parent-item {
    display: flex;
    flex-direction: column;
}

.filter-parent-item.level-0 {
    margin-left: 0;
}

.filter-parent-item.level-1 {
    margin-left: 24px;
}

.filter-parent-item.level-2 {
    margin-left: 48px;
}

.filter-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    gap: 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    margin-bottom: 8px;
    color: #B0BBCC;

    .number {
        color: #58616D;
    }
}

.filter-item:hover {
    background-color: #2b2b2b;
    color: #FFFFFF;

    .number {
        color: #90FBC9;
    }
}

.filter-item.selected {
    background-color: #393939;
    color: #FFFFFF;

    .number {
        color: #90FBC9;
    }
}

.filter-children {
    display: flex;
    flex-direction: column;
}
</style>