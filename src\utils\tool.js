
/**
 * 简单的随机唯一id
 * @returns string
 */
export function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * 深拷贝对象/数组
 * @param {any} obj
 * @returns {any}
 */
export function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 防抖函数
 * @param {Function} fn
 * @param {number} delay
 * @returns {Function}
 */
export function debounce(fn, delay = 300) {
  let timer = null;
  return function (...args) {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

/**
 * 节流函数
 * @param {Function} fn
 * @param {number} delay
 * @returns {Function}
 */
export function throttle(fn, delay = 300) {
  let last = 0;
  return function (...args) {
    const now = Date.now();
    if (now - last > delay) {
      last = now;
      fn.apply(this, args);
    }
  };
}

/**
 * 判断是否为空对象
 * @param {Object} obj
 * @returns {boolean}
 */
export function isEmptyObject(obj) {
  return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
}

/**
 * 生成随机字符串
 * @param {number} length
 * @returns {string}
 */
export function randomString(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 获取url参数
 * @param {string} name
 * @param {string} [url=window.location.href]
 * @returns {string|null}
 */
export function getQueryParam(name, url = window.location.href) {
  name = name.replace(/[\[\]]/g, '\\$&');
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
  const results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, ' '));
}

/**
 * 生成范围随机整数 [min, max]
 * @param {number} min
 * @param {number} max
 * @returns {number}
 */
export function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 格式化时间
 * @param {Date|string|number} date
 * @param {string} fmt
 * @returns {string}
 * @example formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss')
 */
export function formatDate(date, fmt = 'yyyy-MM-dd HH:mm:ss') {
  date = date ? new Date(date) : new Date();
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  };
  let res = fmt.replace(/(y+)/, (_, $1) => (date.getFullYear() + '').slice(4 - $1.length));
  for (const k in o) {
    res = res.replace(new RegExp('(' + k + ')'), (_, $1) =>
      $1.length === 1 ? o[k] : ('00' + o[k]).slice(('' + o[k]).length)
    );
  }
  return res;
}

/**
 * 数组去重
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArray(arr) {
  return Array.from(new Set(arr));
}

/**
 * 按指定大小分组数组
 * @param {Array} arr
 * @param {number} size
 * @returns {Array<Array>}
 */
export function chunkArray(arr, size) {
  const result = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
}

/**
 * 首字母大写
 * @param {string} str
 * @returns {string}
 */
export function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 复制文本到剪贴板
 * @param {string} text
 * @returns {Promise<void>}
 */
export function copyToClipboard(text) {
  return navigator.clipboard
    ? navigator.clipboard.writeText(text)
    : Promise.reject('Clipboard API not supported');
}

/**
 * 判断是否为中国大陆手机号
 * @param {string} phone
 * @returns {boolean}
 */
export function isPhoneNumber(phone) {
  return /^1[3-9]\\d{9}$/.test(phone);
}

/**
 * 下载文件
 * @param {string} url
 * @param {string} [filename]
 */
export function downloadFile(url, filename) {
  const a = document.createElement('a');
  a.href = url;
  a.download = filename || '';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

/**
 * 获取浏览器类型
 * @returns {string}
 */
export function getBrowser() {
  const ua = navigator.userAgent;
  if (/chrome/i.test(ua)) return 'Chrome';
  if (/firefox/i.test(ua)) return 'Firefox';
  if (/safari/i.test(ua)) return 'Safari';
  if (/msie|trident/i.test(ua)) return 'IE';
  if (/edg/i.test(ua)) return 'Edge';
  return 'Unknown';
}

/**
 * 等待指定毫秒
 * @param {number} ms
 * @returns {Promise<void>}
 */
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 保留小数点后指定位数
 * @param {number} value
 * @param {number} decimal
 * @returns {string}
 */
export const toFixedDecimal = (value, decimal) => {
  const parsedValue = parseFloat(value);

  if (isNaN(parsedValue)) {
    return `0.${'0'.repeat(decimal)}`;
  }

  return parsedValue.toFixed(decimal);
};

