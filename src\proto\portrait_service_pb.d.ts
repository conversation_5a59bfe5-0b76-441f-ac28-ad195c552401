import * as jspb from 'google-protobuf'

import * as google_protobuf_timestamp_pb from 'google-protobuf/google/protobuf/timestamp_pb'; // proto import: "google/protobuf/timestamp.proto"


export class Library extends jspb.Message {
  getId(): string;
  setId(value: string): Library;

  getName(): string;
  setName(value: string): Library;

  getDescription(): string;
  setDescription(value: string): Library;

  getTagsList(): Array<string>;
  setTagsList(value: Array<string>): Library;
  clearTagsList(): Library;
  addTags(value: string, index?: number): Library;

  getCreatedby(): string;
  setCreatedby(value: string): Library;

  getCreatedat(): string;
  setCreatedat(value: string): Library;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Library.AsObject;
  static toObject(includeInstance: boolean, msg: Library): Library.AsObject;
  static serializeBinaryToWriter(message: Library, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Library;
  static deserializeBinaryFromReader(message: Library, reader: jspb.BinaryReader): Library;
}

export namespace Library {
  export type AsObject = {
    id: string,
    name: string,
    description: string,
    tagsList: Array<string>,
    createdby: string,
    createdat: string,
  }
}

export class Group extends jspb.Message {
  getId(): string;
  setId(value: string): Group;

  getName(): string;
  setName(value: string): Group;

  getDescription(): string;
  setDescription(value: string): Group;

  getLibraryidsList(): Array<string>;
  setLibraryidsList(value: Array<string>): Group;
  clearLibraryidsList(): Group;
  addLibraryids(value: string, index?: number): Group;

  getCreatedby(): string;
  setCreatedby(value: string): Group;

  getCreatedat(): string;
  setCreatedat(value: string): Group;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Group.AsObject;
  static toObject(includeInstance: boolean, msg: Group): Group.AsObject;
  static serializeBinaryToWriter(message: Group, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Group;
  static deserializeBinaryFromReader(message: Group, reader: jspb.BinaryReader): Group;
}

export namespace Group {
  export type AsObject = {
    id: string,
    name: string,
    description: string,
    libraryidsList: Array<string>,
    createdby: string,
    createdat: string,
  }
}

export class Portrait extends jspb.Message {
  getPortraitid(): string;
  setPortraitid(value: string): Portrait;

  getLibraryid(): string;
  setLibraryid(value: string): Portrait;

  getName(): string;
  setName(value: string): Portrait;

  getGender(): string;
  setGender(value: string): Portrait;

  getBirthdate(): google_protobuf_timestamp_pb.Timestamp | undefined;
  setBirthdate(value?: google_protobuf_timestamp_pb.Timestamp): Portrait;
  hasBirthdate(): boolean;
  clearBirthdate(): Portrait;

  getIdentitycardnumber(): string;
  setIdentitycardnumber(value: string): Portrait;

  getAttributesMap(): jspb.Map<string, string>;
  clearAttributesMap(): Portrait;

  getPortraitimagesList(): Array<PortraitImage>;
  setPortraitimagesList(value: Array<PortraitImage>): Portrait;
  clearPortraitimagesList(): Portrait;
  addPortraitimages(value?: PortraitImage, index?: number): PortraitImage;

  getOtherimagesList(): Array<PortraitImage>;
  setOtherimagesList(value: Array<PortraitImage>): Portrait;
  clearOtherimagesList(): Portrait;
  addOtherimages(value?: PortraitImage, index?: number): PortraitImage;

  getDeleted(): boolean;
  setDeleted(value: boolean): Portrait;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Portrait.AsObject;
  static toObject(includeInstance: boolean, msg: Portrait): Portrait.AsObject;
  static serializeBinaryToWriter(message: Portrait, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Portrait;
  static deserializeBinaryFromReader(message: Portrait, reader: jspb.BinaryReader): Portrait;
}

export namespace Portrait {
  export type AsObject = {
    portraitid: string,
    libraryid: string,
    name: string,
    gender: string,
    birthdate?: google_protobuf_timestamp_pb.Timestamp.AsObject,
    identitycardnumber: string,
    attributesMap: Array<[string, string]>,
    portraitimagesList: Array<PortraitImage.AsObject>,
    otherimagesList: Array<PortraitImage.AsObject>,
    deleted: boolean,
  }
}

export class PortraitImage extends jspb.Message {
  getImageid(): string;
  setImageid(value: string): PortraitImage;

  getImagepath(): string;
  setImagepath(value: string): PortraitImage;

  getObject(): string;
  setObject(value: string): PortraitImage;

  getMetaMap(): jspb.Map<string, string>;
  clearMetaMap(): PortraitImage;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PortraitImage.AsObject;
  static toObject(includeInstance: boolean, msg: PortraitImage): PortraitImage.AsObject;
  static serializeBinaryToWriter(message: PortraitImage, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PortraitImage;
  static deserializeBinaryFromReader(message: PortraitImage, reader: jspb.BinaryReader): PortraitImage;
}

export namespace PortraitImage {
  export type AsObject = {
    imageid: string,
    imagepath: string,
    object: string,
    metaMap: Array<[string, string]>,
  }
}

export class LibraryList extends jspb.Message {
  getItemsList(): Array<Library>;
  setItemsList(value: Array<Library>): LibraryList;
  clearItemsList(): LibraryList;
  addItems(value?: Library, index?: number): Library;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): LibraryList.AsObject;
  static toObject(includeInstance: boolean, msg: LibraryList): LibraryList.AsObject;
  static serializeBinaryToWriter(message: LibraryList, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): LibraryList;
  static deserializeBinaryFromReader(message: LibraryList, reader: jspb.BinaryReader): LibraryList;
}

export namespace LibraryList {
  export type AsObject = {
    itemsList: Array<Library.AsObject>,
  }
}

export class GroupList extends jspb.Message {
  getItemsList(): Array<Group>;
  setItemsList(value: Array<Group>): GroupList;
  clearItemsList(): GroupList;
  addItems(value?: Group, index?: number): Group;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GroupList.AsObject;
  static toObject(includeInstance: boolean, msg: GroupList): GroupList.AsObject;
  static serializeBinaryToWriter(message: GroupList, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GroupList;
  static deserializeBinaryFromReader(message: GroupList, reader: jspb.BinaryReader): GroupList;
}

export namespace GroupList {
  export type AsObject = {
    itemsList: Array<Group.AsObject>,
  }
}

export class PortraitList extends jspb.Message {
  getItemsList(): Array<Portrait>;
  setItemsList(value: Array<Portrait>): PortraitList;
  clearItemsList(): PortraitList;
  addItems(value?: Portrait, index?: number): Portrait;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PortraitList.AsObject;
  static toObject(includeInstance: boolean, msg: PortraitList): PortraitList.AsObject;
  static serializeBinaryToWriter(message: PortraitList, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PortraitList;
  static deserializeBinaryFromReader(message: PortraitList, reader: jspb.BinaryReader): PortraitList;
}

export namespace PortraitList {
  export type AsObject = {
    itemsList: Array<Portrait.AsObject>,
  }
}

export class DeleteResponse extends jspb.Message {
  getSuccess(): boolean;
  setSuccess(value: boolean): DeleteResponse;

  getMessage(): string;
  setMessage(value: string): DeleteResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DeleteResponse.AsObject;
  static toObject(includeInstance: boolean, msg: DeleteResponse): DeleteResponse.AsObject;
  static serializeBinaryToWriter(message: DeleteResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DeleteResponse;
  static deserializeBinaryFromReader(message: DeleteResponse, reader: jspb.BinaryReader): DeleteResponse;
}

export namespace DeleteResponse {
  export type AsObject = {
    success: boolean,
    message: string,
  }
}

export class GetLibraryRequest extends jspb.Message {
  getId(): string;
  setId(value: string): GetLibraryRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetLibraryRequest.AsObject;
  static toObject(includeInstance: boolean, msg: GetLibraryRequest): GetLibraryRequest.AsObject;
  static serializeBinaryToWriter(message: GetLibraryRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetLibraryRequest;
  static deserializeBinaryFromReader(message: GetLibraryRequest, reader: jspb.BinaryReader): GetLibraryRequest;
}

export namespace GetLibraryRequest {
  export type AsObject = {
    id: string,
  }
}

export class ListLibrariesRequest extends jspb.Message {
  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ListLibrariesRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ListLibrariesRequest): ListLibrariesRequest.AsObject;
  static serializeBinaryToWriter(message: ListLibrariesRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ListLibrariesRequest;
  static deserializeBinaryFromReader(message: ListLibrariesRequest, reader: jspb.BinaryReader): ListLibrariesRequest;
}

export namespace ListLibrariesRequest {
  export type AsObject = {
  }
}

export class CreateLibraryRequest extends jspb.Message {
  getName(): string;
  setName(value: string): CreateLibraryRequest;

  getDescription(): string;
  setDescription(value: string): CreateLibraryRequest;

  getTagsList(): Array<string>;
  setTagsList(value: Array<string>): CreateLibraryRequest;
  clearTagsList(): CreateLibraryRequest;
  addTags(value: string, index?: number): CreateLibraryRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CreateLibraryRequest.AsObject;
  static toObject(includeInstance: boolean, msg: CreateLibraryRequest): CreateLibraryRequest.AsObject;
  static serializeBinaryToWriter(message: CreateLibraryRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CreateLibraryRequest;
  static deserializeBinaryFromReader(message: CreateLibraryRequest, reader: jspb.BinaryReader): CreateLibraryRequest;
}

export namespace CreateLibraryRequest {
  export type AsObject = {
    name: string,
    description: string,
    tagsList: Array<string>,
  }
}

export class UpdateLibraryRequest extends jspb.Message {
  getId(): string;
  setId(value: string): UpdateLibraryRequest;

  getName(): string;
  setName(value: string): UpdateLibraryRequest;

  getDescription(): string;
  setDescription(value: string): UpdateLibraryRequest;

  getTagsList(): Array<string>;
  setTagsList(value: Array<string>): UpdateLibraryRequest;
  clearTagsList(): UpdateLibraryRequest;
  addTags(value: string, index?: number): UpdateLibraryRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): UpdateLibraryRequest.AsObject;
  static toObject(includeInstance: boolean, msg: UpdateLibraryRequest): UpdateLibraryRequest.AsObject;
  static serializeBinaryToWriter(message: UpdateLibraryRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): UpdateLibraryRequest;
  static deserializeBinaryFromReader(message: UpdateLibraryRequest, reader: jspb.BinaryReader): UpdateLibraryRequest;
}

export namespace UpdateLibraryRequest {
  export type AsObject = {
    id: string,
    name: string,
    description: string,
    tagsList: Array<string>,
  }
}

export class DeleteLibraryRequest extends jspb.Message {
  getId(): string;
  setId(value: string): DeleteLibraryRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DeleteLibraryRequest.AsObject;
  static toObject(includeInstance: boolean, msg: DeleteLibraryRequest): DeleteLibraryRequest.AsObject;
  static serializeBinaryToWriter(message: DeleteLibraryRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DeleteLibraryRequest;
  static deserializeBinaryFromReader(message: DeleteLibraryRequest, reader: jspb.BinaryReader): DeleteLibraryRequest;
}

export namespace DeleteLibraryRequest {
  export type AsObject = {
    id: string,
  }
}

export class GetGroupRequest extends jspb.Message {
  getId(): string;
  setId(value: string): GetGroupRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetGroupRequest.AsObject;
  static toObject(includeInstance: boolean, msg: GetGroupRequest): GetGroupRequest.AsObject;
  static serializeBinaryToWriter(message: GetGroupRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetGroupRequest;
  static deserializeBinaryFromReader(message: GetGroupRequest, reader: jspb.BinaryReader): GetGroupRequest;
}

export namespace GetGroupRequest {
  export type AsObject = {
    id: string,
  }
}

export class ListGroupsRequest extends jspb.Message {
  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ListGroupsRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ListGroupsRequest): ListGroupsRequest.AsObject;
  static serializeBinaryToWriter(message: ListGroupsRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ListGroupsRequest;
  static deserializeBinaryFromReader(message: ListGroupsRequest, reader: jspb.BinaryReader): ListGroupsRequest;
}

export namespace ListGroupsRequest {
  export type AsObject = {
  }
}

export class CreateGroupRequest extends jspb.Message {
  getName(): string;
  setName(value: string): CreateGroupRequest;

  getDescription(): string;
  setDescription(value: string): CreateGroupRequest;

  getLibraryidsList(): Array<string>;
  setLibraryidsList(value: Array<string>): CreateGroupRequest;
  clearLibraryidsList(): CreateGroupRequest;
  addLibraryids(value: string, index?: number): CreateGroupRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CreateGroupRequest.AsObject;
  static toObject(includeInstance: boolean, msg: CreateGroupRequest): CreateGroupRequest.AsObject;
  static serializeBinaryToWriter(message: CreateGroupRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CreateGroupRequest;
  static deserializeBinaryFromReader(message: CreateGroupRequest, reader: jspb.BinaryReader): CreateGroupRequest;
}

export namespace CreateGroupRequest {
  export type AsObject = {
    name: string,
    description: string,
    libraryidsList: Array<string>,
  }
}

export class UpdateGroupRequest extends jspb.Message {
  getId(): string;
  setId(value: string): UpdateGroupRequest;

  getName(): string;
  setName(value: string): UpdateGroupRequest;

  getDescription(): string;
  setDescription(value: string): UpdateGroupRequest;

  getLibraryidsList(): Array<string>;
  setLibraryidsList(value: Array<string>): UpdateGroupRequest;
  clearLibraryidsList(): UpdateGroupRequest;
  addLibraryids(value: string, index?: number): UpdateGroupRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): UpdateGroupRequest.AsObject;
  static toObject(includeInstance: boolean, msg: UpdateGroupRequest): UpdateGroupRequest.AsObject;
  static serializeBinaryToWriter(message: UpdateGroupRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): UpdateGroupRequest;
  static deserializeBinaryFromReader(message: UpdateGroupRequest, reader: jspb.BinaryReader): UpdateGroupRequest;
}

export namespace UpdateGroupRequest {
  export type AsObject = {
    id: string,
    name: string,
    description: string,
    libraryidsList: Array<string>,
  }
}

export class DeleteGroupRequest extends jspb.Message {
  getId(): string;
  setId(value: string): DeleteGroupRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): DeleteGroupRequest.AsObject;
  static toObject(includeInstance: boolean, msg: DeleteGroupRequest): DeleteGroupRequest.AsObject;
  static serializeBinaryToWriter(message: DeleteGroupRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): DeleteGroupRequest;
  static deserializeBinaryFromReader(message: DeleteGroupRequest, reader: jspb.BinaryReader): DeleteGroupRequest;
}

export namespace DeleteGroupRequest {
  export type AsObject = {
    id: string,
  }
}

export class PagedQueryPortraitsRequest extends jspb.Message {
  getLibraryid(): string;
  setLibraryid(value: string): PagedQueryPortraitsRequest;

  getAfterid(): string;
  setAfterid(value: string): PagedQueryPortraitsRequest;

  getLimit(): number;
  setLimit(value: number): PagedQueryPortraitsRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PagedQueryPortraitsRequest.AsObject;
  static toObject(includeInstance: boolean, msg: PagedQueryPortraitsRequest): PagedQueryPortraitsRequest.AsObject;
  static serializeBinaryToWriter(message: PagedQueryPortraitsRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PagedQueryPortraitsRequest;
  static deserializeBinaryFromReader(message: PagedQueryPortraitsRequest, reader: jspb.BinaryReader): PagedQueryPortraitsRequest;
}

export namespace PagedQueryPortraitsRequest {
  export type AsObject = {
    libraryid: string,
    afterid: string,
    limit: number,
  }
}

export class SearchPortraitsRequest extends jspb.Message {
  getLibraryidsList(): Array<string>;
  setLibraryidsList(value: Array<string>): SearchPortraitsRequest;
  clearLibraryidsList(): SearchPortraitsRequest;
  addLibraryids(value: string, index?: number): SearchPortraitsRequest;

  getCriteria(): PortraitSearchCriteria | undefined;
  setCriteria(value?: PortraitSearchCriteria): SearchPortraitsRequest;
  hasCriteria(): boolean;
  clearCriteria(): SearchPortraitsRequest;

  getLimit(): number;
  setLimit(value: number): SearchPortraitsRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SearchPortraitsRequest.AsObject;
  static toObject(includeInstance: boolean, msg: SearchPortraitsRequest): SearchPortraitsRequest.AsObject;
  static serializeBinaryToWriter(message: SearchPortraitsRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SearchPortraitsRequest;
  static deserializeBinaryFromReader(message: SearchPortraitsRequest, reader: jspb.BinaryReader): SearchPortraitsRequest;
}

export namespace SearchPortraitsRequest {
  export type AsObject = {
    libraryidsList: Array<string>,
    criteria?: PortraitSearchCriteria.AsObject,
    limit: number,
  }
}

export class FindPortraitByIdRequest extends jspb.Message {
  getLibraryid(): string;
  setLibraryid(value: string): FindPortraitByIdRequest;

  getPortraitid(): string;
  setPortraitid(value: string): FindPortraitByIdRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): FindPortraitByIdRequest.AsObject;
  static toObject(includeInstance: boolean, msg: FindPortraitByIdRequest): FindPortraitByIdRequest.AsObject;
  static serializeBinaryToWriter(message: FindPortraitByIdRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): FindPortraitByIdRequest;
  static deserializeBinaryFromReader(message: FindPortraitByIdRequest, reader: jspb.BinaryReader): FindPortraitByIdRequest;
}

export namespace FindPortraitByIdRequest {
  export type AsObject = {
    libraryid: string,
    portraitid: string,
  }
}

export class FindPortraitsByIdsRequest extends jspb.Message {
  getLibraryid(): string;
  setLibraryid(value: string): FindPortraitsByIdsRequest;

  getPortraitidsList(): Array<string>;
  setPortraitidsList(value: Array<string>): FindPortraitsByIdsRequest;
  clearPortraitidsList(): FindPortraitsByIdsRequest;
  addPortraitids(value: string, index?: number): FindPortraitsByIdsRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): FindPortraitsByIdsRequest.AsObject;
  static toObject(includeInstance: boolean, msg: FindPortraitsByIdsRequest): FindPortraitsByIdsRequest.AsObject;
  static serializeBinaryToWriter(message: FindPortraitsByIdsRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): FindPortraitsByIdsRequest;
  static deserializeBinaryFromReader(message: FindPortraitsByIdsRequest, reader: jspb.BinaryReader): FindPortraitsByIdsRequest;
}

export namespace FindPortraitsByIdsRequest {
  export type AsObject = {
    libraryid: string,
    portraitidsList: Array<string>,
  }
}

export class FindPortraitsByIdsLibrariesRequest extends jspb.Message {
  getLibraryidsList(): Array<string>;
  setLibraryidsList(value: Array<string>): FindPortraitsByIdsLibrariesRequest;
  clearLibraryidsList(): FindPortraitsByIdsLibrariesRequest;
  addLibraryids(value: string, index?: number): FindPortraitsByIdsLibrariesRequest;

  getPortraitidsList(): Array<string>;
  setPortraitidsList(value: Array<string>): FindPortraitsByIdsLibrariesRequest;
  clearPortraitidsList(): FindPortraitsByIdsLibrariesRequest;
  addPortraitids(value: string, index?: number): FindPortraitsByIdsLibrariesRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): FindPortraitsByIdsLibrariesRequest.AsObject;
  static toObject(includeInstance: boolean, msg: FindPortraitsByIdsLibrariesRequest): FindPortraitsByIdsLibrariesRequest.AsObject;
  static serializeBinaryToWriter(message: FindPortraitsByIdsLibrariesRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): FindPortraitsByIdsLibrariesRequest;
  static deserializeBinaryFromReader(message: FindPortraitsByIdsLibrariesRequest, reader: jspb.BinaryReader): FindPortraitsByIdsLibrariesRequest;
}

export namespace FindPortraitsByIdsLibrariesRequest {
  export type AsObject = {
    libraryidsList: Array<string>,
    portraitidsList: Array<string>,
  }
}

export class PortraitSearchCriteria extends jspb.Message {
  getName(): string;
  setName(value: string): PortraitSearchCriteria;

  getIscontaining(): boolean;
  setIscontaining(value: boolean): PortraitSearchCriteria;

  getGender(): string;
  setGender(value: string): PortraitSearchCriteria;

  getIdentitycardnumber(): string;
  setIdentitycardnumber(value: string): PortraitSearchCriteria;

  getStatus(): string;
  setStatus(value: string): PortraitSearchCriteria;

  getMinage(): number;
  setMinage(value: number): PortraitSearchCriteria;

  getMaxage(): number;
  setMaxage(value: number): PortraitSearchCriteria;

  getAfterid(): string;
  setAfterid(value: string): PortraitSearchCriteria;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): PortraitSearchCriteria.AsObject;
  static toObject(includeInstance: boolean, msg: PortraitSearchCriteria): PortraitSearchCriteria.AsObject;
  static serializeBinaryToWriter(message: PortraitSearchCriteria, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): PortraitSearchCriteria;
  static deserializeBinaryFromReader(message: PortraitSearchCriteria, reader: jspb.BinaryReader): PortraitSearchCriteria;
}

export namespace PortraitSearchCriteria {
  export type AsObject = {
    name: string,
    iscontaining: boolean,
    gender: string,
    identitycardnumber: string,
    status: string,
    minage: number,
    maxage: number,
    afterid: string,
  }
}

