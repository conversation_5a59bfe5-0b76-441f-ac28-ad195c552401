/*
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-11 10:25:56
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-11 16:36:09
 * @FilePath: \frontend-template\src\router\modules\manage.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import lineIcon from '@/assets/svg/menus/line.svg'
import lineActiveIcon from '@/assets/svg/menus/line-active.svg'

export default [
  {
    path: '/manage',
    name: 'Manage',
    meta: { title: '平台管理', icon: lineIcon, activeIcon: lineActiveIcon, menu: true, keepAlive: false, layout: 'main' },
    redirect: '/manage/data-manage',
    children: [
      {
        path: 'data-manage',
        name: 'ManageData',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '数据库管理', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'face-manage',
        name: 'ManageFace',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '人像库管理', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'plat-manage',
        name: 'ManagePlat',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '平台参数', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'compare-manage',
        name: 'ManageCompare',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '比对通道管理', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: '/middleground-manage',
        name: 'ManageMiddleground',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '数据中台|技术中台', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
      {
        path: 'message-manage',
        name: 'ManageMessage',
        component: () => import('@/components/Error/404.vue'),
        meta: { title: '消息中心', menu: true, icon: lineIcon, activeIcon: lineActiveIcon, }
      },
    ]
  },
];