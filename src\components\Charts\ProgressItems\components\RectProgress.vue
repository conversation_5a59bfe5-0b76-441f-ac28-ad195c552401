<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-17 10:10:45
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-17 10:29:41
 * @FilePath: \platform-face-web\src\components\Charts\ProgressItems\components\TriangleProgress.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="rect-progress-item">
    <div class="progress-text">
      <div class="text-value">{{ (value * 100).toFixed(0) }}%</div>
      <div class="text-label" :class="{ 'text-label-active': value > 0 }">{{ label }}</div>
    </div>
    <svg :width="size" :height="size">
      <rect
        :fill="bgColor"
        :x="strokeWidth/2"
        :y="strokeWidth/2"
        :rx="radius"
        :width="rectWidth"
        :height="rectWidth"
        :stroke="trackColor"
        :stroke-width="strokeWidth"
      />
      <rect
        v-if="value > 0"
        fill="none"
        :x="strokeWidth/2"
        :y="strokeWidth/2"
        :rx="radius"
        :width="rectWidth"
        :height="rectWidth"
        :stroke="color"
        :stroke-width="strokeWidth"
        :stroke-dasharray="dasharray"
      />
    </svg>
  </div>
</template>

<script setup>
import { computed } from 'vue';
/**
 * 矩形进度条组件
 * @property {number} value - 进度值，0~1
 * @property {string} label - 进度标签
 * @property {string} color - 进度条颜色
 * @property {string} trackColor - 轨道颜色
 * @property {string} bgColor - 底色
 * @property {number} size - 组件尺寸
 * @property {number} strokeWidth - 进度条宽度
 * @property {number} radius - 圆角半径
 */
const props = defineProps({
  value: { type: Number, default: 0 }, // 0~1
  label: { type: String, default: '' },
  color: { type: String, default: '#4079AA' },
  trackColor: { type: String, default: '#2F3C50' },
  bgColor: { type: String, default: 'none' },
  size: { type: Number, default: 80 },
  strokeWidth: { type: Number, default: 8 },
  radius: { type: Number, default: 10 },
});
// 矩形边长
const rectWidth = computed(() => props.size - props.strokeWidth);
// 圆角部分总周长
const circleGirth = computed(() => 2 * Math.PI * props.radius); // 四个1/4圆
// 直线部分总长度
const straightLength = computed(() => (rectWidth.value - 2 * props.radius) * 4);
// 总周长
const girth = computed(() => circleGirth.value + straightLength.value);
// 进度条虚线数组
const dasharray = computed(() => `${props.value * girth.value} ${girth.value}`);
</script>

<style scoped>
.rect-progress-item {
  width: 80px;
  height: 80px;
  position: relative;
}
.progress-text {
  width: max-content;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 11px;
  text-align: center;
  color: #b0bbcc;
}
.text-label-active {
  color: #3792ce;
}
</style> 