/**
 * 复选框相关的 composable
 * 处理图例复选框和阈值复选框的渲染和交互
 */
import * as d3 from 'd3';
import overallComparisonResults from '@/assets/svg/common/top-overview.svg';

/**
 * 复选框管理 composable
 * @param {Function} emit - 事件发射器
 * @returns {Object} 复选框相关的方法
 */
export function useCheckbox(emit) {
  /**
   * 添加复选框
   * @param {Array} checkboxValue - 复选框配置数组
   * @param {string} className - CSS类名
   * @param {d3.Selection} g4 - 操作组选择器
   */
  const addCheckbox = (checkboxValue, className, g4) => {
    const checkboxGroup = g4
      .selectAll(`g.${className}`)
      .data(checkboxValue.value)
      .enter()
      .append('g')
      .attr('class', className)
      .attr('data-id', (d) => d.id)
      .attr('transform', (d) => `translate(${d.x}, ${d.y})`)
      .style('cursor', 'pointer')
      .on('click', function (event, d) {
        // 图例复选框点击处理
        const index = checkboxValue.value.findIndex((item) => item.id === d.id);
        checkboxValue.value[index].isChecked = !checkboxValue.value[index].isChecked;
        // 更新复选框显示状态
        updateCheckboxState(d, checkboxValue);
      });

    // 绘制复选框背景
    checkboxGroup
      .append('rect')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', 16)
      .attr('height', 16)
      .attr('fill', 'transparent')
      .attr('stroke', (d) => `${d.color}`)
      .attr('stroke-width', 1)
      .attr('rx', 2)
      .attr('ry', 2);

    // 绘制复选框勾选标记
    checkboxGroup
      .append('path')
      .attr('class', 'checkmark')
      .attr('d', 'M4,8 L7,11 L12,4')
      .attr('stroke', (d) => `${d.color}`)
      .attr('stroke-width', 2)
      .attr('fill', 'none');

    // 绘制复选框文本
    checkboxGroup
      .append('text')
      .attr('x', 24)
      .attr('y', 12)
      .text((d) => d.label)
      .style('font-size', '12px')
      .attr('fill', (d) => `${d.color}`)
      .style('text-anchor', 'start');

    // 为"显示全部"添加概览图标
    checkboxGroup.each(function (d) {
      if (d.id === 'all') {
        d3.select(this)
          .append('image')
          .attr('class', 'overviewIcon')
          .attr('x', 80)
          .attr('y', -6)
          .attr('xlink:href', overallComparisonResults)
          .attr('width', 24)
          .attr('height', 24)
          .style('cursor', 'pointer')
          .on('click', function (event) {
            event.stopPropagation();
            emit('overviewClick');
          });
      }
    });
  };

  /**
   * 更新复选框状态
   * @param {Array} checkboxValue - 复选框配置数组
   */
  const updateCheckboxState = (d, checkboxValue) => {
    if (d.id === 'all') {
      // 如果点击的是“显示全部”，那么其他的勾选框应该和这个的值是一样的
      const newValue = checkboxValue.value.map((item) => ({
        ...item,
        isChecked: checkboxValue.value[0].isChecked,
      }));
      checkboxValue.value = newValue;
      emit('checkboxChange', checkboxValue.value[0].isChecked);
    } else if (d.id !== 'all' && checkboxValue.value[0].id === 'all') {
      // 此时是点击的右侧筛选框
      checkboxValue.value[0].isChecked = false;
      const valueArr = checkboxValue.value.filter((item) => item.isChecked).map((item) => item.id);
      emit('checkboxChange', valueArr);
    } else if (checkboxValue.value[0].id !== 'all') {
      // 这个是左侧的筛选框，筛选的内容不需要发射事件，直接更新图表即可
    }
    // 改变选项框的样式
    checkboxValue.value.forEach((item) => {
      d3.select(`[data-id="${item.id}"]`)
        .select('.checkmark')
        .attr('visibility', item.isChecked ? 'visible' : 'hidden');
    });
  };

  return {
    addCheckbox,
    updateCheckboxState,
  };
}
