<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-14 09:21:13
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-14 15:02:24
 * @FilePath: \platform-face-web\src\views\Login\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="login-container">
    <div class="svg-icon">
      <SvgIcon icon-class="VERSION" width="90px" height="20px"></SvgIcon>
    </div>
    <div class="logo-area">
      <LogoTitle :logoStyle="{ width: '600px' }" />
    </div>
    <div class="login-form">
      <LogoForm />
    </div>
  </div>
</template>
<script setup>
import LogoTitle from './components/LogoTitle.vue';
import LogoForm from './components/LoginForm.vue'
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  width: 100%;
  height: 100vh;
  background: #000;
  position: relative;

  .svg-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 22;
  }

  .logo-area {
    width: calc(100% - 700px);
    flex: 1;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      height: 60%;
      border-right: 1px solid #505863;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }
  }

  .login-form {
    width: 700px;
    height: 100%;
    color: var(--color-text);
    background: transparent;
    overflow: visible;
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
