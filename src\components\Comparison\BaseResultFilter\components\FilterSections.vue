<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 16:16:31
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-23 16:51:50
 * @FilePath: \platform-face-web\src\components\Comparison\BaseResultFilter\components\FilterSections.vue
 * @Description: 过滤器区域组件
 * 包含统计信息区域和自定义按钮区域的展示组件
 * 
-->
<template>
  <div class="filter-sections">
    <!-- 统计信息区域 -->
    <template v-if="config.statsSection">
      <div v-for="(stat, index) in config.statsSection.items" :key="index" class="filter-item">
        <BadgeWrapper :count="getStatValue(stat)" :show-zero="true">
          {{ stat.label }}
        </BadgeWrapper>
        <Divider v-if="index < config.statsSection.items.length - 1" direction="vertical" height="18px" />
      </div>
    </template>

    <!-- 自定义按钮区域 -->
    <template v-if="config.customButtons">
      <div v-for="(button, index) in config.customButtons" :key="index" class="filter-item">
        <CustomButtonWithTooltip :override-style="true" :button-bg="button.buttonBg" :hover-bg="button.hoverBg"
          :font-color="button.fontColor" :hover-font-color="button.hoverFontColor" :icon-name="button.iconName">
          <span class="label">{{ button.text }}</span>
        </CustomButtonWithTooltip>
        <Divider v-if="index < config.customButtons.length - 1" direction="vertical" height="18px" />
      </div>
    </template>
  </div>
</template>

<script setup>
import Divider from '@/components/Common/Divider/index.vue';
import BadgeWrapper from '@/components/Common/BadgeWrapper/index.vue';
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  statsData: {
    type: Object,
    default: () => ({})
  }
});

// 获取统计数据值
const getStatValue = (stat) => {
  if (stat.key && props.statsData[stat.key] !== undefined) {
    return props.statsData[stat.key];
  }
  return stat.defaultValue || 0;
};
</script>

<style lang="scss" scoped>
.filter-sections {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 20px;

  .label {
    // 标签样式
  }
}
</style>
