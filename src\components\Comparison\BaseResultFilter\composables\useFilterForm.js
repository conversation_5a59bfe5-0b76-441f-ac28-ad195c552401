import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

/**
 * 过滤器表单数据管理 composable
 * 管理表单数据的响应式状态和验证逻辑
 */
export function useFilterForm(props, emits) {
  // 表单数据
  const formData = ref({
    portraitGroupId: '',
    algorithmCodes: ['ci_an'],
    numberOfResults: 50,
    comparisonThreshold: 0.7,
  });

  // 用于记录上一次有效的算法选择
  const previousAlgorithmCodes = ref([...formData.value.algorithmCodes]);

  // 更新表单数据
  const setFormData = (data) => {
    formData.value = {
      portraitGroupId: data.portraitGroupId || '',
      algorithmCodes: data.algorithmCodes || data.algorithms || ['ci_an'],
      numberOfResults: data.numberOfResults || 50,
      comparisonThreshold: data.comparisonThreshold || 0.7
    };
  };

  // 监听默认参数变化
  watch(() => props.defaultParams, (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      setFormData(newVal);
    }
  }, { immediate: true });

  // 判断表单是否变更
  const isFormChanged = computed(() => {
    return JSON.stringify(formData.value) !== JSON.stringify(props.defaultParams);
  });

  // 算法选择变化处理
  const handleAlgorithmChange = (newValue) => {
    if (newValue.length === 0) {
      message.error('请至少选择一项');
      formData.value.algorithmCodes = [...previousAlgorithmCodes.value];
    } else {
      previousAlgorithmCodes.value = [...newValue];
    }
  };

  // 当人像组发生变化时
  watch(
    () => formData.value.portraitGroupId,
    (newGroupId) => {
      const selectedGroup = props.portraitGroupOptions.find(
        grp => grp.libraryGroupId === newGroupId
      );
      if (selectedGroup) {
        formData.value.algorithmCodes = selectedGroup.algorithmCodes.includes('ci_an')
          ? ['ci_an']
          : selectedGroup.algorithmCodes.slice(0, 1);
        formData.value.comparisonThreshold = selectedGroup.comparisonThreshold;
        formData.value.numberOfResults = selectedGroup.numberOfResults;
      }
    }
  );

  // 监听表单数据变化
  watch(
    formData,
    (newVal) => {
      emits('submit-success', {
        params: newVal,
        changed: isFormChanged.value
      });
    },
    { deep: true }
  );

  // 获取表单数据
  const getFormData = () => {
    return formData.value;
  };

  return {
    formData,
    isFormChanged,
    setFormData,
    handleAlgorithmChange,
    getFormData
  };
}
