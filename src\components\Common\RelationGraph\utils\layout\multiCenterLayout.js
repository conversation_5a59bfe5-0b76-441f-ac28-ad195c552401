/**
 * 多中心图布局模块
 * 负责多中心图的布局计算，包括中心节点布局和周边节点分组布局
 */

import { layoutNodesStrict } from './singleCenterLayout';

/**
 * 多中心布局计算
 */
export async function layoutMultiCenterGraph({ nodes, centers, container }) {
    const width = container.clientWidth;
    const height = container.clientHeight;

    // 分离中心节点和普通节点
    const centerNodes = nodes.filter((n) => n.isCenter);
    const otherNodes = nodes.filter((n) => !n.isCenter);

    // 1. 布局中心节点
    const centerPositions = layoutCenters(centerNodes, width, height);

    // 2. 为每个中心布局周边节点
    const nodeGroups = groupNodesByCenter(otherNodes, centers);
    const laidOutNodes = [];

    for (const center of centerNodes) {
        const groupNodes = nodeGroups[center.photoId] || [];
        const positions = await layoutNodesAroundCenter(groupNodes, centerPositions[center.photoId], width, height);
        laidOutNodes.push(...positions);
    }

    return {
        centers: centerPositions,
        nodeGroups: nodeGroups,
        nodes: [...centerNodes.map((c) => centerPositions[c.photoId]), ...laidOutNodes],
    };
}

function groupNodesByCenter(nodes, centers) {
    const groups = {};
    centers.forEach((centerId) => {
        groups[centerId] = nodes.filter((n) => n.photoId === centerId);
    });
    return groups;
}

function layoutCenters(centers, width, height) {
    const positions = {};
    const centerCount = centers.length;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.35;

    if (centerCount === 1) {
        positions[centers[0].photoId] = { ...centers[0], x: centerX, y: centerY };
    } else if (centerCount === 2) {
        positions[centers[0].photoId] = { ...centers[0], x: centerX - radius, y: centerY };
        positions[centers[1].photoId] = { ...centers[1], x: centerX + radius, y: centerY };
    } else if (centerCount === 3) {
        // 钝角等腰三角形参数
        const baseLength = radius * 3; // 底边加长
        const heightRatio = 0.6; // 高度降低
        const topOffset = radius * 0.3; // 顶部节点向内偏移

        // 底部节点 [1] (钝角顶点)
        positions[centers[0].photoId] = {
            ...centers[0],
            x: centerX,
            y: centerY + radius * heightRatio,
        };

        // 左上方节点 [2]
        positions[centers[1].photoId] = {
            ...centers[1],
            x: centerX - baseLength / 2 + topOffset,
            y: centerY - radius * heightRatio,
        };

        // 右上方节点 [3]
        positions[centers[2].photoId] = {
            ...centers[2],
            x: centerX + baseLength / 2 - topOffset,
            y: centerY - radius * heightRatio,
        };
    } else {
        // 圆形布局
        const angleStep = (2 * Math.PI) / centerCount;
        centers.forEach((center, index) => {
            const angle = index * angleStep - Math.PI / 2;
            positions[center.photoId] = {
                ...center,
                x: centerX + radius * Math.cos(angle),
                y: centerY + radius * Math.sin(angle),
            };
        });
    }

    return positions;
}

async function layoutNodesAroundCenter(nodes, center, width, height) {
    // 使用单中心布局算法，调整坐标为中心位置
    const layout = await layoutNodesStrict(
        nodes,
        { width: width * 0.6, height: height * 0.6, innerRadius: 120, outerRadius: 230, minDistance: 10 }, // 缩小布局区域
    );

    return layout.map((node) => ({
        ...node,
        x: node.x + center.x - width * 0.3,
        y: node.y + center.y - height * 0.3,
        group: center.id,
    }));
}
