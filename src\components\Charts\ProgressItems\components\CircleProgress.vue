 
<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-17 10:09:59
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-17 10:25:06
 * @FilePath: \platform-face-web\src\components\Charts\ProgressItems\CircleProgress.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="circle-progress-item">
    <div class="progress-text">
      <div class="text-value">{{ (value * 100).toFixed(0) }}%</div>
      <div class="text-label" :class="{ 'text-label-active': value > 0 }">{{ label }}</div>
    </div>
    <svg :width="size" :height="size" :style="{ transform: 'rotate(-90deg)' }">
      <circle
        :fill="bgColor"
        :cx="size/2"
        :cy="size/2"
        :r="radius"
        :stroke="trackColor"
        :stroke-width="strokeWidth"
      />
      <circle
        v-if="value > 0"
        fill="none"
        :cx="size/2"
        :cy="size/2"
        :r="radius"
        :stroke="color"
        :stroke-width="strokeWidth"
        :stroke-dasharray="dasharray"
        stroke-linecap="round"
      />
    </svg>
  </div>
</template>

<script setup>
import { computed } from 'vue';
/**
 * 圆形进度条组件
 * @property {number} value - 进度值，0~1
 * @property {string} label - 进度标签
 * @property {string} color - 进度条颜色
 * @property {string} trackColor - 轨道颜色
 * @property {string} bgColor - 底色
 * @property {number} size - 组件尺寸
 * @property {number} strokeWidth - 进度条宽度
 */
const props = defineProps({
  value: { type: Number, default: 0 }, // 0~1
  label: { type: String, default: '' },
  color: { type: String, default: '#4079AA' },
  trackColor: { type: String, default: '#2F3C50' },
  bgColor: { type: String, default: 'none' },
  size: { type: Number, default: 80 },
  strokeWidth: { type: Number, default: 8 },
});
// 圆环半径
const radius = computed(() => (props.size - props.strokeWidth) / 2);
// 圆环周长
const circumference = computed(() => 2 * Math.PI * radius.value);
// 进度条虚线数组
const dasharray = computed(() => `${props.value * circumference.value} ${circumference.value}`);
</script>

<style scoped>
.circle-progress-item {
  width: 80px;
  height: 80px;
  position: relative;
}
.progress-text {
  width: max-content;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 11px;
  text-align: center;
  color: #b0bbcc;
}
.text-label-active {
  color: #3792ce;
}
</style> 