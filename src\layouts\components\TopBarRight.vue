<!--
 * @Author: gzr <EMAIL>
 * @Date: 2024-04-05 19:44:44
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-21 14:54:37
 * @FilePath: \vue3-js-template-master\src\layouts\components\TopBarRight.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 头部右侧内容 -->
  <div class="header-right">
    <!-- 搜索栏 -->
    <div class="search-input" v-if="!['/search'].includes(route.path)">
      <a-input placeholder="请输入">
        <template #suffix>
          <SvgIcon icon-class="search" size="14px"></SvgIcon>
          <Divider height="8px" color="#40516A" direction="vertical" margin-size="0px" />
          <SvgIcon icon-class="camera" size="14px"></SvgIcon>
        </template>
      </a-input>
      <SvgIcon icon-class="search-underline" class="search-icon" width="86.76px" height="1.16px"></SvgIcon>
    </div>
    <!-- 消息提醒 -->
    <div class="notice-div">
      <SvgIcon icon-class="notice-icon" size="20px"></SvgIcon>
      <SvgIcon icon-class="notice-dot" size="8px" class="dot"></SvgIcon>
    </div>
    <!-- 用户头像 -->
    <a-dropdown overlay-class-name="account-dropdown">
      <a class="account">
        <span class="ant-avatar">
          <SvgIcon icon-class="icon-user" size="20px" />
        </span>
      </a>
      <template #overlay>
        <a-menu @click="clickRightMenuItem">
          <a-menu-item key="editPassword">修改密码</a-menu-item>
          <a-menu-item key="logout">退出</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <Divider height="8px" color="#40516A" direction="vertical" margin-size="0px" />
    <SvgIcon icon-class="VERSION" width="90px" height="20px"></SvgIcon>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
const route = useRoute();
const router = useRouter();
const userInfo = ref({});

onMounted(() => {
  // 获取用户信息
  const storedUserInfo = localStorage.getItem('userInfo');
  if (storedUserInfo) {
    userInfo.value = JSON.parse(storedUserInfo);
  }
});

const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      message.success('已退出登录');
      router.push('/login');
    },
  });
};
function clickRightMenuItem({ key }) {
  if (key === 'logout') {
    Modal.confirm({
    title: '确认退出',
    content: '确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      // 清除本地存储
      // store.dispatch('auth/logout');
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      message.success('已退出登录');
      router.push('/login');
    },
  });
    
  } else if (key === 'editPassword') {
    editPasswordParam.editShow = true;
  } else {
    router.push(key);
  }
}
</script>

<style lang="scss" scoped>
.header-right {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;

  .account {
    cursor: pointer;
    display: inline-block;
    color: var(--sg-header-right-content-color);

    &:hover {
      color: var(--sg-global-menu-item-selected-bg);
    }

    .ant-avatar {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin-right: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: linear-gradient(90deg, #2d3442 0%, #1a1d25 100%);
      border: 1px solid;
      border-color: #3e4d61;
    }

    .name {
      font-size: var(--font-size-large);
    }
  }

  .search-input {
    position: relative;
    margin-right: 4px;
    margin-top: -5px;

    .search-icon {
      position: absolute;
      bottom: 25%;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
    }

    :deep(.ant-input-affix-wrapper) {
      background-color: #000 !important;
      width: 132px;
      height: 28px;
      border-radius: 15px;
      min-height: 28px;
      border-color: #00a3ff !important;
      font-size: 12px;
      padding: 2px 10px;
      box-shadow: 0px 3px 10px rgba(0, 196, 255, 0.46);
    }

    :deep(.ant-input-affix-wrapper:hover) {
      border-color: #00a3ff !important;
      outline: none !important;
    }

    :deep(.ant-input) {
      height: 24px !important;
      min-height: 24px !important;
      background-color: transparent !important;
    }

    :deep(.ant-input-affix-wrapper .ant-input-suffix > *:not(:last-child)) {
      margin-inline-end: 0px;
    }

    :deep(.ant-input-affix-wrapper .ant-input-suffix) {
      gap: 8px;
    }
  }

  .notice-div {
    width: 20px;
    height: 20px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .dot {
      position: absolute;
      top: 1px;
      right: 1px;
      
    }
  }
}
</style>