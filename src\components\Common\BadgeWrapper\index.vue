<!--
 * @Author: yuzhouisme
 * @Date: 2025-07-16 14:19:45
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-16 16:41:13
 * @FilePath: \platform-face-web\src\components\Common\BadgeWrapper\index.vue
 * @Description: BadgeWrapper - 角标包装器组件
 * 功能描述：为任意内容添加角标标记，支持动态内容变化时的自适应布局
-->
<template>
  <div class="badge-wrapper" ref="wrapperRef" :style="wrapperStyle">
    <slot />

    <span v-if="showZero || count" ref="badgeRef" class="badge" :style="badgeStyle" @click="handleBadgeClick">
      {{ count }}
    </span>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { debounce } from '@/utils/tool'

// Props定义 - 通用角标包装器配置
const props = defineProps({
  // 角标显示内容 - 支持数字和字符串
  count: {
    type: [String, Number],
    default: undefined,
    validator: (value) => value === undefined || value === null || value >= 0
  },
  // 是否在数值为0时显示角标
  showZero: {
    type: Boolean,
    default: false
  },
  // 角标位置偏移配置
  offset: {
    type: Object,
    default: () => ({
      top: '-1px',
      right: '0px'
    }),
    validator: (value) => {
      return value && typeof value === 'object' &&
        ('top' in value || 'right' in value)
    }
  },
  // 角标背景颜色 - 直接传入颜色值
  color: {
    type: String,
    default: 'transparent'
  },
  // 角标字体大小 - 直接传入大小值
  fontSize: {
    type: String,
    default: '12px'
  },
  // 角标字体颜色 - 直接传入颜色值
  fontColor: {
    type: String,
    default: '#fff'
  }
})

// 事件定义
const emit = defineEmits(['badge-click'])

// 响应式引用
const wrapperRef = ref(null)
const badgeRef = ref(null)
const badgePosition = ref({
  top: props.offset.top,
  right: props.offset.right
})
const wrapperStyle = ref({ paddingRight: '0px' })

// ResizeObserver 实例
let resizeObserver = null

// 计算角标样式 - 直接使用传入的值
const badgeStyle = computed(() => ({
  top: badgePosition.value.top,
  right: badgePosition.value.right,
  backgroundColor: props.color,      // 直接使用传入的颜色
  fontSize: props.fontSize,          // 直接使用传入的字体大小
  color: props.fontColor            // 直接使用传入的字体颜色
}))

// 重新计算包装器样式 - 只需要调整右侧padding为角标预留空间
const recalculateBadgeLayout = () => {
  nextTick(() => {
    const wrapper = wrapperRef.value
    const badge = badgeRef.value
    if (!wrapper || !badge) return

    // 临时重置包装器样式，获取内容的真实宽度
    wrapperStyle.value.paddingRight = '0px'

    // 强制重新渲染以获取准确的尺寸
    nextTick(() => {
      const badgeWidth = badge.offsetWidth
      const badgeRight = parseFloat(props.offset.right) || 0

      // 计算包装器需要的右侧padding
      // 当right为负值时，角标向左偏移，需要的padding更少
      // 当right为正值时，角标向右偏移，需要的padding更多
      const requiredPadding = Math.max(0, badgeWidth - badgeRight)
      wrapperStyle.value.paddingRight = `${requiredPadding}px`
    })
  })
}

// 角标点击事件处理
const handleBadgeClick = () => {
  emit('badge-click', { count: props.count })
}

// 防抖函数 - 避免频繁触发重新计算
const debouncedRecalculate = debounce(recalculateBadgeLayout, 10)

// 初始化ResizeObserver - 监听包装器尺寸变化
const initResizeObserver = () => {
  if (typeof ResizeObserver !== 'undefined' && wrapperRef.value) {
    resizeObserver = new ResizeObserver(() => {
      // 使用防抖函数避免频繁重新计算
      debouncedRecalculate()
    })
    resizeObserver.observe(wrapperRef.value)
  }
}

// 清理ResizeObserver
const cleanupResizeObserver = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
}

// 组件挂载后初始化位置和宽度
onMounted(() => {
  recalculateBadgeLayout()

  nextTick(() => {
    // 初始化ResizeObserver监听包装器尺寸变化
    initResizeObserver()
  })
})

// 组件卸载时清理ResizeObserver
onUnmounted(() => {
  cleanupResizeObserver()
})

// 监听count变化，重新计算位置和宽度
watch(() => props.count, () => {
  recalculateBadgeLayout()
})

</script>

<style scoped>
.badge-wrapper {
  position: relative;
  display: inline-block;
}

.badge {
  position: absolute;
  transform: translate(0%, -50%);
  padding: 0 4px;
  line-height: 1.5;
  border-radius: 10px;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
}

.badge:hover {
  transform: translate(0%, -50%) scale(1.1);
}

.badge:active {
  transform: translate(0%, -50%) scale(0.95);
}
</style>
