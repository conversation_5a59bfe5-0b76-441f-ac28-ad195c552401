<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-23 16:17:18
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-23 16:28:42
 * @FilePath: \platform-face-web\src\components\Comparison\BaseResultFilter\components\FormControls.vue
 * @Description: 表单控件组件-包含返回结果数选择、阈值设置、过滤按钮等表单控件
 * 
 * 
-->
<template>
  <div class="form-controls">
    <!-- 返回结果数量选择 -->
    <template v-if="config.showResultsSelect">
      <Divider direction="vertical" height="18px" />
      <div class="filter-item">
        <span class="label">{{ config.resultsLabel || '返回结果' }}</span>
        <a-select :value="formData.numberOfResults" :options="resultsOptions" placeholder="请选择" style="width: 70px;"
          @change="handleResultsChange">
        </a-select>
      </div>
    </template>

    <!-- 阈值设置 -->
    <template v-if="config.showThresholdSelect">
      <Divider direction="vertical" height="18px" />
      <div class="filter-item">
        <span class="label">{{ config.thresholdLabel || '设定阈值' }}</span>
        <a-select :value="formData.comparisonThreshold" placeholder="请选择" style="width: 70px"
          :options="thresholdOptions" @change="handleThresholdChange">
        </a-select>
      </div>
    </template>

    <!-- 额外的过滤按钮 -->
    <template v-if="config.showFilterButton">
      <Divider direction="vertical" height="18px" />
      <CustomButtonWithTooltip class="mr-3" icon-name="filter-gray" icon-size="21px" button-bg="#24394e"
        hover-bg="#2a4a5f">
      </CustomButtonWithTooltip>
    </template>
  </div>
</template>

<script setup>
import Divider from '@/components/Common/Divider/index.vue';
import CustomButtonWithTooltip from '@/components/Common/CustomButtonWithTooltip/index.vue';

const props = defineProps({
  config: {
    type: Object,
    required: true
  },
  formData: {
    type: Object,
    required: true
  }
});

const emits = defineEmits(['update:numberOfResults', 'update:comparisonThreshold']);

// 返回结果数选项
const resultsOptions = [
  { label: '50', value: 50 },
  { label: '100', value: 100 },
  { label: '150', value: 150 },
];

// 阈值选项
const thresholdOptions = [
  { label: '0.1', value: 0.1 },
  { label: '0.2', value: 0.2 },
  { label: '0.3', value: 0.3 },
  { label: '0.4', value: 0.4 },
  { label: '0.5', value: 0.5 },
  { label: '0.6', value: 0.6 },
  { label: '0.7', value: 0.7 },
  { label: '0.8', value: 0.8 },
  { label: '0.9', value: 0.9 },
];

// 处理返回结果数变化
const handleResultsChange = (value) => {
  emits('update:numberOfResults', value);
};

// 处理阈值变化
const handleThresholdChange = (value) => {
  emits('update:comparisonThreshold', value);
};
</script>

<style lang="scss" scoped>
.form-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 20px;

  .label {
    // 标签样式
  }
}
</style>
