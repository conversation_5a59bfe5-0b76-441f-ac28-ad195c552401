<!--
 * @Author: yuzhouisme
 * @Date: 2025-03-17 
 * @LastEditors: yuzhouisme
 * @LastEditTime: 2025-07-16 16:42:25
 * @FilePath: \platform-face-web\src\components\Common\FaceBoxEditor\index.vue
 * @Description:人脸框编辑器组件
-->
<template>
  <div class="face-box-editor">
    <!-- 人脸框绘制子组件 -->
    <FaceCanvas :image-src="imageSrc" :face-boxes="faceBoxes" :manual-face-boxes="manualFaceBoxes"
      :selected-items="selectedItems" @box-drawn="onBoxDrawn" @box-selected="onBoxSelected" />
    <!-- 验证弹窗子组件 -->
    <div v-if="showConfirmation" class="confirmation-overlay">
      <div class="confirmation-dialog">
        <p>确认添加此人脸框吗？</p>
        <img v-if="pendingFaceImage" :src="pendingFaceImage" alt="new face" class="face-preview" />
        <div class="d-flex align-items-center justify-content-center g-3">
          <CustomButtonWithTooltip width="60px" height="24px" @click="handleCancelFace">取消</CustomButtonWithTooltip>
          <CustomButtonWithTooltip width="60px" height="24px" @click="handleConfirmFace">确认</CustomButtonWithTooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import FaceCanvas from '@/components/Common/FaceCanvas/index.vue';

const props = defineProps({
  imageSrc: {
    type: String,
    required: true,
  },
  faceBoxes: {
    type: Array,
    default: () => [],
  },
  manualFaceBoxes: {
    type: Array,
    default: () => [],
  },
  selectedItems: {
    type: Array,
    default: () => [],
  },
  editable: {
    type: Boolean,
    default: true,
  }
});

const emit = defineEmits(['boxDrawn', 'boxSelected']);

// 验证弹窗相关的状态
const showConfirmation = ref(false)
const pendingFace = ref(null)        // 暂存待确认的新框坐标
const pendingFaceImage = ref(null)   // 暂存待确认的新截脸图像（base64）

const onBoxDrawn = (payload) => {
  if (!payload) return;

  const { face, croppedImage } = payload;
  pendingFace.value = face;
  pendingFaceImage.value = croppedImage;
  showConfirmation.value = true;
};

const onBoxSelected = (key) => {
  console.log('onBoxSelected =', key);
  emit('boxSelected', key);
};

const handleConfirmFace = () => {
  if (pendingFace.value) {
    const newBox = {
      ...pendingFace.value,
      manualSelection: true
    };
    emit('boxDrawn', newBox);
  }

  showConfirmation.value = false
  pendingFace.value = null
  pendingFaceImage.value = null
};

const handleCancelFace = () => {
  showConfirmation.value = false;
  pendingFace.value = null;
  pendingFaceImage.value = null;
};

</script>

<style lang="scss" scoped>
.face-box-editor {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  background-image: url(/src/assets/svg/common/face_bg.svg);
}

.confirmation-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background-color: #202531;
  border: 1px solid #31384A;
  padding: 8px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 20px #15161B;
}

.confirmation-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.confirmation-dialog p {
  margin-bottom: 8px;
}

.face-preview {
  max-width: 200px;
  max-height: 200px;
  margin-bottom: 8px;
  border-radius: 4px;
}
</style>