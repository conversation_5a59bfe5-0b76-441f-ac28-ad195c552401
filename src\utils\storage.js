import { Base64 } from './securityUtils';

const storage = {
    getItem(key, useLocalStorage = false) {
      const storageType = useLocalStorage ? localStorage : sessionStorage;
      let data = storageType.getItem(key);
  
      if (!data) return null;
  
      // 针对特定 key 进行解码
      if (['auth_info', 'user_info'].includes(key)) {
        data = Base64.decode(data);
        return JSON.parse(data);
      }
  
      return data;
    },
  
    setItem(key, data, useLocalStorage = false) {
      const storageType = useLocalStorage ? localStorage : sessionStorage;
  
      // 针对特定 key 进行编码
      if (['auth_info', 'user_info'].includes(key)) {
        let dataStr = JSON.stringify(data);
        dataStr = Base64.encode(dataStr);
        storageType.setItem(key, dataStr);
      } else {
        storageType.setItem(key, data);
      }
    },
  
    removeItem(key, useLocalStorage = false) {
      const storageType = useLocalStorage ? localStorage : sessionStorage;
      storageType.removeItem(key);
    }
  };
  
  export default storage;