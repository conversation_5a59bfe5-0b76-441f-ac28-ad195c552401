import { getWebSocketManager } from '@/websocket';

const state = {
  subscriptions: {} // 保存订阅状态
};

const mutations = {
  ADD_SUBSCRIPTION(state, { name }) {
    state.subscriptions[name] = true;
  },
  REMOVE_SUBSCRIPTION(state, { name }) {
    delete state.subscriptions[name];
  }
};

const actions = {
  // 初始化订阅
  subscribe({ commit }, { subscribeName, callback }) {
    if (!getWebSocketManager()) {
      console.warn('WebSocket is disabled or not initialized');
      return;
    }

    if (!state.subscriptions[subscribeName]) {
      getWebSocketManager().subscribe({
        subscribeName,
        dispatch: (name, data) => {
          console.log(`Data received for ${name}:`, data);
          try {
            if (callback) callback(data);
          } catch (error) {
            console.error(`Handle callback for ${name} error:`, error);
          }
        }
      });
      commit('ADD_SUBSCRIPTION', { name: subscribeName });
    }
  },

  // 取消订阅
  unsubscribe({ commit }, { subscribeName }) {
    if (!getWebSocketManager()) {
      console.warn('WebSocket is disabled or not initialized');
      return;
    }

    if (state.subscriptions[subscribeName]) {
      getWebSocketManager().unsubscribe({ subscribeName });
      commit('REMOVE_SUBSCRIPTION', { name: subscribeName });
    }
  },

  // 全部取消订阅
  unsubscribeAll({ commit }) {
    if (!getWebSocketManager()) {
      console.warn('WebSocket is disabled or not initialized');
      return;
    }

    Object.keys(state.subscriptions).forEach((subscribeName) => {
      getWebSocketManager().unsubscribe({ subscribeName });
      commit('REMOVE_SUBSCRIPTION', { name: subscribeName });
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
