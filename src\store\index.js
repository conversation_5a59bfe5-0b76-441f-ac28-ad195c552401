import Vuex from 'vuex';

import actions from './actions';
import getters from './getters';
import state from './state';
import mutations from './mutations';

import auth from './modules/auth';
import websocket from './modules/websocket';

const debug = process.env.NODE_ENV !== 'production';

export default Vuex.createStore({
  modules: {
    auth,
    websocket,
  },
  actions,
  getters,
  state,
  mutations,
  strict: debug
});
