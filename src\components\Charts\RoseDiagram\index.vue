<template>
  <div class="circle-center-wrap">
    <div class="circle-svg-area">
      <svg :width="size" :height="size" :viewBox="`0 0 ${size} ${size}`">
        <!-- 外环辅助线和刻度 -->
        <g>
          <circle :cx="center" :cy="center" :r="outerRadius" fill="none" stroke="#3c4554" stroke-width="1" />
          <g v-for="i in circleList.length" :key="i">
            <line :x1="center" :y1="center" :x2="getTickPos(i - 1, outerRadius).x"
              :y2="getTickPos(i - 1, outerRadius).y" stroke="#3c4554" stroke-width="1" />
            <text :x="getTickPos(i - 1, (outerRadius + sectorRadius) / 2, true).x" :y="getTickY(i - 1)"
              :transform="getTextTransform(i - 1)" text-anchor="middle" alignment-baseline="middle" class="circle-tick">
              {{ circleList[i - 1].value }}%
            </text>
          </g>
        </g>
        <!-- 五个扇区 -->
        <g v-for="(item, idx) in circleList" :key="item.label">
          <!-- 背景扇区（始终渲染，整环） -->
          <path :d="describeSector(
            center,
            center,
            innerRadius,
            sectorRadius,
            getStartAngle(idx),
            getEndAngle(idx)
          )
            " fill="none" stroke="#3c4554" stroke-width="1" />
          <!-- 高亮扇区（从内到外填充百分比） -->
          <path v-if="item.value > 0" :d="describeSector(
            center,
            center,
            innerRadius,
            innerRadius + (sectorRadius - innerRadius) * (item.value / 100),
            getStartAngle(idx),
            getEndAngle(idx)
          )
            " :fill="`rgba(${item.color})`" stroke="#3c4554" stroke-width="0" />
          <!-- label -->
          <text :x="getLabelPos(idx).x" :y="getLabelPos(idx).y" text-anchor="middle" alignment-baseline="middle"
            class="circle-label">
            {{ item.label }}
          </text>
        </g>
        <!-- 中心空心圆 -->
        <circle :cx="center" :cy="center" :r="innerRadius" :fill="centerColor" stroke="#3c4554" :stroke-width="1" />
      </svg>
    </div>
  </div>
</template>

<script setup>
// 组件属性定义
const props = defineProps({
  // 扇区数据列表，每项包含label、value（百分比）、color（rgb字符串或hex）
  circleList: {
    type: Array,
    default: () => [
      { label: '归档', value: 20, color: '#fff' },
      { label: '上传', value: 100, color: '#3a7adf' },
      { label: '归一', value: 80, color: '#3a7adf' },
      { label: '比对', value: 70, color: '#3a7adf' },
      { label: '关联', value: 50, color: '#3a7adf' },
    ],
  },
  // 图表尺寸
  size: { type: Number, default: 200 },
  // 中心圆颜色
  centerColor: { type: String, default: '#1D2028' },
});

// 图表中心点坐标
const center = computed(() => props.size / 2);
// 外环半径
const outerRadius = computed(() => props.size / 2 - 10);
// 扇区外半径
const sectorRadius = computed(() => props.size / 2 - 30);
// 内环半径
const innerRadius = computed(() => props.size / 2 - props.size * 0.38);

// 获取指定扇区的起始角度
const getStartAngle = (idx) => -90 + idx * (360 / props.circleList.length);
// 获取指定扇区的结束角度
const getEndAngle = (idx) => -90 + (idx + 1) * (360 / props.circleList.length);

// 从上到下填充，value为0~1，返回角度
function getAngleByValue(value) {
  // value: 0~1
  const sectorAngle = 360 / props.circleList.length;
  return sectorAngle * value;
}

// 画扇形路径，r1为内半径，r2为外半径，startAngle/endAngle为起止角度
function describeSector(cx, cy, r1, r2, startAngle, endAngle) {
  const startOuter = polarToCartesian(cx, cy, r2, startAngle);
  const endOuter = polarToCartesian(cx, cy, r2, endAngle);
  const startInner = polarToCartesian(cx, cy, r1, endAngle);
  const endInner = polarToCartesian(cx, cy, r1, startAngle);
  const arcSweep = endAngle - startAngle <= 180 ? '0' : '1';
  return [
    'M',
    startOuter.x,
    startOuter.y,
    'A',
    r2,
    r2,
    0,
    arcSweep,
    1,
    endOuter.x,
    endOuter.y,
    'L',
    startInner.x,
    startInner.y,
    'A',
    r1,
    r1,
    0,
    arcSweep,
    0,
    endInner.x,
    endInner.y,
    'Z',
  ].join(' ');
}

// 极坐标转笛卡尔坐标
function polarToCartesian(cx, cy, r, angle) {
  const rad = (angle * Math.PI) / 180.0;
  return {
    x: cx + r * Math.cos(rad),
    y: cy + r * Math.sin(rad),
  };
}

// 获取label文字位置（内环中间）
function getLabelPos(idx) {
  // 扇区中线角度
  const angle = getStartAngle(idx) + 360 / props.circleList.length / 2;
  return polarToCartesian(
    center.value,
    center.value,
    (sectorRadius.value + innerRadius.value) / 2,
    angle
  );
}
// 获取刻度线或文字的位置
function getTickPos(idx, r, isMiddle = false) {
  // 如果是文字，角度在扇区中线；如果是线，角度在分割线
  const angle = isMiddle
    ? getStartAngle(idx) + 360 / props.circleList.length / 2 // 扇区中线
    : getStartAngle(idx); // 分割线
  return polarToCartesian(center.value, center.value, r, angle);
}

// 获取刻度文字的旋转变换
function getTextTransform(idx) {
  const angle = getStartAngle(idx) + 360 / props.circleList.length / 2;
  let rotate = angle + 90;
  if (rotate > 90 && rotate < 270) {
    rotate += 180;
  }
  const pos = getTickPos(
    idx,
    (outerRadius.value + sectorRadius.value) / 2,
    true
  );
  return `rotate(${rotate} ${pos.x} ${pos.y})`;
}

// 获取刻度文字的y坐标（微调）
function getTickY(idx) {
  const pos = getTickPos(
    idx,
    (outerRadius.value + sectorRadius.value) / 2,
    true
  );
  const angle = getStartAngle(idx) + 360 / props.circleList.length / 2;
  let rotate = angle + 90;
  if (rotate > 90 && rotate < 270) {
    return pos.y;
  } else {
    return pos.y + 2;
  }
}

// 获取高亮扇区的外半径，value为百分比（0~100）
function getHighlightOuterRadius(value) {
  return innerRadius + (sectorRadius - innerRadius) * (value / 100);
}
</script>

<style scoped>
.circle-svg-area {
  width: 260px;
  height: 260px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-label {
  font-size: 12px;
  fill: #b0bbcc;
  pointer-events: none;
}

.circle-tick {
  font-size: 12px;
  fill: #b0bbcc;
  pointer-events: none;
}

.circle-center-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>