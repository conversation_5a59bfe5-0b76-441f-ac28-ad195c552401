<!--
 * @Author: gzr <EMAIL>
 * @Date: 2025-07-17 16:06:36
 * @LastEditors: gzr <EMAIL>
 * @LastEditTime: 2025-07-17 16:11:33
 * @FilePath: \platform-face-web\src\components\Common\DynamicNumber\Index.vue
 * @Description: 动态数字滚动组件，从初始值平滑滚动到目标值
-->
<template>
  <!-- 展示动画数字 -->
  <span>{{ displayValue }}</span>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';

// 组件props定义
const props = defineProps({
  // 起始值，动画从该值开始
  start: { type: Number, default: 0 },
  // 目标值，动画滚动到该值
  end: { type: Number, required: true },
  // 动画时长（毫秒）
  duration: { type: Number, default: 1500 },
  // 小数位数，默认0
  decimals: { type: Number, default: 0 },
  // 格式化函数（可选），如千分位、前缀等
  formatter: { type: Function }
});

// 当前显示的数字
const displayValue = ref(props.start);

// 动画起始时间戳
let startTime = null;
// requestAnimationFrame的id，用于取消动画
let frameId = null;

// 数字格式化函数，优先使用用户自定义formatter，否则按小数位数格式化
function format(val) {
  if (props.formatter) return props.formatter(val);
  return Number(val).toFixed(props.decimals);
}

// 动画主逻辑，每一帧更新数字
function animateNumber(ts) {
  if (!startTime) startTime = ts;
  // 计算动画进度（0~1）
  const progress = Math.min((ts - startTime) / props.duration, 1);
  // 当前值按进度插值
  const value = props.start + (props.end - props.start) * progress;
  displayValue.value = format(value);
  if (progress < 1) {
    // 未结束，继续下一帧
    frameId = requestAnimationFrame(animateNumber);
  } else {
    // 动画结束，确保显示最终值
    displayValue.value = format(props.end);
    startTime = null;
    frameId = null;
  }
}

// 启动动画
function startAnimation() {
  if (frameId) cancelAnimationFrame(frameId);
  startTime = null;
  frameId = requestAnimationFrame(animateNumber);
}

// 监听end变化，自动重新滚动
watch(() => props.end, startAnimation, { immediate: true });
onMounted(startAnimation);
</script>
