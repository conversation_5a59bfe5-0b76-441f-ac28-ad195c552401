<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-25 16:42:10
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-31 15:19:58
 * @FilePath: \platform-face-web\src\components\Comparison\CompareResultsBarGraph\CompareResultsBarGraph.vue
 * @Description: 人像详情的均匀分布的堆叠柱状图，一次展示一个算法
 * 
 * 
-->
<template>
  <div class="compare-results-bar-graph-wrapper">
    <BaseBarChart ref="baseChart" :data="data" :clear-select-data="clearSelectData" :threshold="threshold"
      :options="chartOptions" @checkbox-change="emit('checkboxChange', $event)"
      @selected-data="emit('selectedData', $event)" @overview-click="emit('overviewClick', $event)"
      @chart-mounted="handleChartMounted" @data-changed="handleDataChanged"
      @chart-initialized="handleChartInitialized" />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { CHART_CONSTANTS } from '@/utils/chartUtils';
import BaseBarChart from '@/components/Charts/BaseBarChart/index.vue';
import { useBarDrawing } from './composables/useBarDrawing';
import { useBarInteraction } from './composables/useBarInteraction';
import { useChartUpdate } from './composables/useChartUpdate';

const props = defineProps({
  // 图表的原始数据
  data: {
    type: Array,
    require: true
  },
  // 用来判断是不是要清空数据，只是一个标识，与true或false没有关系
  clearSelectData: {
    type: Boolean,
    default: false
  },
  // 设定阈值
  threshold: {
    type: Number,
    default: 0.7
  }
});

const emit = defineEmits(['checkboxChange', 'selectedData', 'overviewClick']);

const baseChart = ref(null);

// 图表配置选项（专门为CompareResultsBarGraph定制）
const chartOptions = computed(() => ({
  rectsPerGroup: CHART_CONSTANTS.RECTS_PER_GROUP_SINGLE, // 5个一组
  varianceHeight: 0, // 不需要相似度差值区域
}));

// 使用composable函数
const drawingFunctions = useBarDrawing();
const interactionFunctions = useBarInteraction(baseChart);
const chartUpdateFunctions = useChartUpdate(baseChart, drawingFunctions, interactionFunctions);

// 使用composable中的事件处理函数
const handleChartMounted = () => chartUpdateFunctions.handleChartMounted(props.data);
const handleDataChanged = (newData) => chartUpdateFunctions.handleDataChanged(newData);
const handleChartInitialized = (newData) => chartUpdateFunctions.handleChartInitialized(newData);

// 监听清空选择数据的变化
watch(
  () => props.clearSelectData,
  (newData, oldData) => {
    if (newData !== oldData && baseChart.value) {
      const { currentData } = baseChart.value.barChart;
      interactionFunctions.clearAllSelections(currentData);
    }
  }
);
</script>

<style scoped>
.compare-results-bar-graph-wrapper {
  width: 100%;
  height: 100%;
}
</style>
