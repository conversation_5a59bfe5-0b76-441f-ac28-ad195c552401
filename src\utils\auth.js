import storage from './storage';
import { DEFAULT_CONFIG } from '@/constants';
import { Base64, TokenContext } from './securityUtils';
import menus from '@/router/menus';

const auth = {
  /**
   * 获取当前所有菜单数组
   * @return {Array}
   */  
  getMenus() {
    return menus.map(item => {
      return {
        ...item,
        children: item.subMenus || []
      }
    })
  },
  isLogin() {
    const userInfo = auth.getUserInfo();
    const authInfo = auth.getAuthInfo();

    return !!(userInfo || authInfo);
  },
  setJWTInfo(data, useLocalStorage) {
    storage.setItem('jwt_info', data, useLocalStorage);
  },
  getJWTInfo(useLocalStorage) {
    return storage.getItem('jwt_info', useLocalStorage);
  },
  clearJWTInfo() {
    storage.removeItem('jwt_info', !!storage.getItem('jwt_info', true));
  },
  /**
   * 缓存当前用户信息
   * @param {*} data 用户信息
   * @param {*} useLocalStorage true = localStorage false = sessionStorage
   */
  setUserInfo(data, useLocalStorage) {
    storage.setItem('user_info', data, useLocalStorage);
  },
  /**
   * 获取当前登录的用户信息
   * 如果要获取用户实体的信息，如用户 ID 等，请调用 getUserEntity 方法
   * @returns {Object|null} 登录用户相关信息
   */
  getUserInfo() {
    return storage.getItem('user_info', true) || storage.getItem('user_info');
  },
  clearUserInfo() {
    storage.removeItem('user_info', !!storage.getItem('user_info', true));
  },
  /**
   * 获取用户基本的实体信息，如 id
   * @returns {Object|null} 登录用户实体信息
   */
  getUserEntity() {
    const user = auth.getUserInfo(true) || auth.getUserInfo();
    if (user !== null && user.loginUserDTO !== undefined) {
      return user.loginUserDTO;
    }
    return null;
  },
  setAuthInfo(data) {
    storage.setItem('auth_info', data);
  },
  getAuthInfo() {
    return storage.getItem('auth_info');
  },

  setToken(info) {
    sessionStorage.setItem('t', Base64.encode(JSON.stringify(info)))
  },
  getToken() {
    let tokenInfoSession = JSON.parse(Base64.decode(sessionStorage.getItem('t')))
    if (!tokenInfoSession) {
      return null
    } else {
      return tokenInfoSession
    }
  },
  clearTokenInfo() {
    sessionStorage.removeItem('t')
  },
  clearAuthInfo() {
    storage.removeItem('auth_info');
  },
  /**
   * 是否有菜单的访问权限,此函数为私有函数,外部请勿调用
   * @param {String} menuPathName 菜单的访问地址
   * @param {String} search 访问地址参数
   * @returns {Boolean} true:表示有 false:表示无
   */
  hasMenuAccessPermission({ menuPathName, search }) {
    if (!DEFAULT_CONFIG.enableMenuAccessControl) {
      return true;
    }

    const fullPathName = menuPathName + search;

    if (fullPathName === null || fullPathName === '') {
      return false;
    }

    // 获取所有有权限的菜单的 url 列表
    const accessPermissions = auth.getAuthInfo().permissions;

    if (accessPermissions?.length) {
      let num = -1;
      const getNum = (path) => {
        const pathNum = accessPermissions.indexOf(path);
        if (pathNum !== -1) {
          const url = accessPermissions[pathNum];
          if (url === path) {
            num = pathNum;
          }
        }
      };

      getNum(fullPathName);
      // 如果当前 url 不在菜单列表上，则不需要鉴权，应为 404
      if (num === -1) {
        return true;
      }

      // 通过权限内容匹配 pathname
      return accessPermissions.some((url) => {
        if (url !== null && url !== undefined) {
          // url 可以是字符串或者数组字符串
          if (typeof url === 'string') {
            return fullPathName === url;
          } else if (url.some) {
            return url.some((aUrl) => {
              return fullPathName === aUrl;
            });
          }
        }
        return false;
      });
    } else {
      return false;
    }
  },

  checkAuth({ currentLocation, onFailure }) {
    const pathname = currentLocation.path;
    const search = '';
    // pathname === / 是处理项目根目录访问
    if (this.isLogin()) {
      if (pathname !== '/') {
        if (DEFAULT_CONFIG.whitePages && DEFAULT_CONFIG.whitePages.indexOf(pathname) > -1) {
          return;
        }

        if (!this.hasMenuAccessPermission({ menuPathName: pathname, search })) {
          onFailure({ redirectUrl: '/unauthorized' });
        }
      }
    } else {
      let redirectUrl = null;
      if (!(pathname.startsWith('/user/login') || pathname === '/')) {
        redirectUrl = `/user/login?from=${encodeURIComponent(pathname + search)}`;
      } else if (pathname === '/') {
        redirectUrl = '/user/login';
      }

      onFailure({ redirectUrl });
    }
  },
  getHeaders() {
    const userInfo = auth.getUserInfo();
    const authInfo = auth.getToken();
    let contentUsr = '';
    let contentStr = '';

    if (userInfo) {
      const userArr = [];
      userArr.push(userInfo.id);
      userArr.push(userInfo.userName);
      userArr.push(userInfo.loginKey);
      userArr.push(userInfo.key);
      contentUsr = Base64.encode(userArr.join(','));
    }

    if (authInfo) {
      contentStr = TokenContext.createToken(
        authInfo.accessKey,
        authInfo.secretKey.substring(8, 24),
        'addplus+addplus+'
      );
    }

    return {
      'Content-Usr': contentUsr,
      'Content-Str': contentStr
    };
  },
  getAuthorizationHeader() {
    const token = storage.getItem('jwt_info', true);
    if (token) {
      return {
        Authorization: `Bearer ${token}`
      };
    }
  },

};

export default auth;
