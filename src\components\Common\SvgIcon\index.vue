<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-16 15:26:59
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-07-16 17:12:59
 * @FilePath: \platform-face-web\src\components\Common\SvgIcon\index.vue
 * @Description: 展示svg格式的图标组件
 * 
-->
<template>
  <svg :class="['svg-icon', customClass]" :width="width ? width : size" :height="height ? height : size"
    aria-hidden="true">
    <!-- vite-plugin-svg-icons 通常会给每个图标加上统一前缀，比如 icon- -->
    <use :xlink:href="symbolId" />
  </svg>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 对应图标文件名，比如传入 'edit' 则引用 #icon-edit
  iconClass: { type: String, required: true },
  // 可选的大小
  size: { type: String, default: '14px' },
  width: { type: String, default: '' },
  height: { type: String, default: '' },
  // 额外的 class
  customClass: { type: String, default: '' },
});

// 计算 symbol id，前缀视 vite-plugin-svg-icons 的配置而定，常见为 icon-
const symbolId = computed(() => `#icon-${props.iconClass}`);
</script>

<style scoped>
.svg-icon {
  fill: currentColor;
}
</style>
