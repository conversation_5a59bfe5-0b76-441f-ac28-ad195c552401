# ImagePreviewer 图片预览组件

## 组件描述
通用的图片预览组件，支持多张图片切换查看，具备加载状态处理和错误重试功能。

## 功能特性
- 支持多张图片切换查看
- 图片加载状态显示
- 图片加载失败重试
- 可配置导航按钮显示/隐藏
- 可配置遮罩点击关闭
- 响应式设计，适配不同屏幕尺寸
- 使用项目统一的样式变量

## 使用场景
- 图片库预览
- 产品图片展示
- 相册浏览
- 缩略图点击放大查看

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 是否显示模态框 |
| images | Array | [] | 图片URL列表，每个元素为字符串 |
| initialIndex | Number | 0 | 初始显示的图片索引 |
| showNavigation | Boolean | true | 是否显示导航按钮 |
| maskClosable | Boolean | true | 是否允许点击遮罩关闭 |

### Props 验证规则
- `images`: 必须是数组，且每个元素都是字符串类型
- `initialIndex`: 必须是非负数

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | Boolean | 模态框显示状态变化时触发 |
| change | Number | 图片切换时触发，参数为当前图片索引 |

## 使用示例

### 基础用法
```vue
<template>
  <div>
    <a-button @click="showPreview = true">预览图片</a-button>
    
    <ImagePreviewer
      v-model:visible="showPreview"
      :images="imageList"
      :initial-index="0"
      @change="handleImageChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ImagePreviewer from '@/components/Common/ImagePreviewer/index.vue';

const showPreview = ref(false);
const imageList = ref([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
  'https://example.com/image3.jpg'
]);

const handleImageChange = (index) => {
  console.log('当前图片索引:', index);
};
</script>
```

### 单张图片预览
```vue
<template>
  <ImagePreviewer
    v-model:visible="visible"
    :images="[singleImage]"
    :show-navigation="false"
  />
</template>

<script setup>
import ImagePreviewer from '@/components/Common/ImagePreviewer/index.vue';

const visible = ref(false);
const singleImage = ref('https://example.com/single-image.jpg');
</script>
```

### 禁用遮罩关闭
```vue
<template>
  <ImagePreviewer
    v-model:visible="visible"
    :images="images"
    :mask-closable="false"
  />
</template>
```

### 与缩略图结合使用
```vue
<template>
  <div class="thumbnail-gallery">
    <div 
      v-for="(image, index) in images" 
      :key="index"
      class="thumbnail"
      @click="openPreview(index)"
    >
      <img :src="image" alt="缩略图" />
    </div>
    
    <ImagePreviewer
      v-model:visible="previewVisible"
      :images="images"
      :initial-index="currentIndex"
      @change="handleImageChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ImagePreviewer from '@/components/Common/ImagePreviewer/index.vue';

const previewVisible = ref(false);
const currentIndex = ref(0);
const images = ref([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
  'https://example.com/image3.jpg'
]);

const openPreview = (index) => {
  currentIndex.value = index;
  previewVisible.value = true;
};

const handleImageChange = (index) => {
  currentIndex.value = index;
};
</script>

<style scoped>
.thumbnail-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}

.thumbnail {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 150px;
    object-fit: cover;
  }
}
</style>
```

## 状态说明

### 加载状态
- 显示加载动画和"图片加载中..."文字
- 导航按钮在加载期间被禁用

### 错误状态
- 显示错误图标和"图片加载失败"文字
- 提供重试按钮，点击可重新加载图片

### 正常状态
- 显示图片内容
- 导航按钮正常可用（多张图片时）

## 样式定制

组件使用了项目的统一样式变量，支持主题切换：

- `--color-text`: 文字颜色
- `--color-text-secondary`: 次要文字颜色
- `--color-error`: 错误状态颜色
- `--btn-default-bg`: 按钮背景色
- `--spacing-medium`: 中等间距
- `--border-radius-small`: 小圆角

## 注意事项

1. **图片URL有效性**: 确保传入的图片URL有效且可访问
2. **索引范围**: `initialIndex`应在有效范围内（0 到 images.length-1）
3. **性能考虑**: 大量图片时建议实现懒加载或分页
4. **移动端适配**: 组件已适配移动端，但建议在小屏幕设备上测试
5. **网络环境**: 在网络较差的环境下，加载状态会更频繁出现

## 更新日志

### v1.1.0 (2025-07-17)
- 添加图片加载状态处理
- 添加图片加载错误重试功能
- 修复ref导入问题
- 添加props验证
- 改用Less样式和项目样式变量
- 完善组件文档
- 单张图片时自动隐藏导航按钮
- 添加change事件支持
